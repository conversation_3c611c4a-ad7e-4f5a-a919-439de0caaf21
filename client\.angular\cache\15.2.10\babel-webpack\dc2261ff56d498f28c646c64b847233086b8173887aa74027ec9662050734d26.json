{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from \"@angular/forms\";\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../services/auto-schedule.service\";\nimport * as i4 from \"../../../../services/loading.service\";\nexport class ModalCrudBreakComponent {\n  constructor(_modalService, _translateService, _autoSchedule, _loadingService) {\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._autoSchedule = _autoSchedule;\n    this._loadingService = _loadingService;\n    this.breakModalParams = {\n      locationId: null,\n      tournamentId: null,\n      timeSlotId: null\n    };\n    this.onSubmit = new EventEmitter();\n    this.addBreakForm = new FormGroup({});\n    this.addBreakModel = {};\n    this.addBreakFields = [{\n      key: 'tournament_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'location_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'description',\n      type: 'input',\n      props: {\n        label: 'Event name',\n        placeholder: 'Enter event name (Default: Break)',\n        required: false,\n        type: 'text'\n      },\n      defaultValue: \"Break\"\n    }, {\n      key: 'break_durations',\n      type: 'input',\n      props: {\n        label: 'Break duration',\n        placeholder: 'Enter break duration (in minutes)',\n        required: true,\n        type: 'number',\n        min: 1\n      },\n      defaultValue: 30,\n      validation: {\n        messages: {\n          required: this._translateService.instant('Break duration is required.'),\n          min: this._translateService.instant('Break duration must be at least 1 minute.')\n        }\n      }\n    }, {\n      key: 'time_slot_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n  }\n  ngOnInit() {\n    this.addBreakFields[0].defaultValue = this.breakModalParams.tournamentId;\n    this.addBreakFields[1].defaultValue = this.breakModalParams.locationId;\n    if (this.breakModalParams.timeSlotId) {\n      this.addBreakFields[2].defaultValue = this.breakModalParams.description;\n      this.addBreakFields[3].defaultValue = this.breakModalParams.breakDurations;\n      this.addBreakFields[4].defaultValue = this.breakModalParams.timeSlotId;\n    }\n  }\n  onSubmitCrudBreak(model) {\n    console.log(model);\n    this._loadingService.show();\n    const action = this.breakModalParams.timeSlotId ? this._autoSchedule.updateBreak(model) : this._autoSchedule.addBreak(model);\n    action.subscribe({\n      next: res => {\n        console.log('res', res);\n        if (this.breakModalParams.timeSlotId) {\n          Swal.fire({\n            title: this._translateService.instant('Success!'),\n            text: this._translateService.instant('Break updated successfully'),\n            icon: 'success'\n          });\n        }\n        this.onSubmit.emit(res);\n        this._modalService.dismissAll();\n      },\n      error: error => {\n        console.error(this.breakModalParams.timeSlotId ? 'Error updating break:' : 'Error adding break:', error);\n      },\n      complete: () => {\n        this._loadingService.dismiss();\n      }\n    });\n  }\n  closeModal() {\n    this.addBreakModel = {};\n    this._modalService.dismissAll();\n  }\n  clearForm() {\n    this.addBreakForm.reset();\n  }\n  static #_ = this.ɵfac = function ModalCrudBreakComponent_Factory(t) {\n    return new (t || ModalCrudBreakComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AutoScheduleService), i0.ɵɵdirectiveInject(i4.LoadingService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalCrudBreakComponent,\n    selectors: [[\"app-modal-crud-break\"]],\n    inputs: {\n      breakModalParams: \"breakModalParams\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalAddBreak\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalCrudBreakComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalCrudBreakComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalCrudBreakComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitCrudBreak(ctx.addBreakModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalCrudBreakComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitCrudBreak(ctx.addBreakModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Add Break\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.addBreakForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.addBreakForm)(\"fields\", ctx.addBreakFields)(\"model\", ctx.addBreakModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.addBreakForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Submit\"), \" \");\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AAEtE,SAASC,SAAS,QAAQ,gBAAgB;AAK1C,OAAOC,IAAI,MAAM,aAAa;;;;;;AAiB9B,OAAM,MAAOC,uBAAuB;EAkEhCC,YACYC,aAAuB,EACvBC,iBAAmC,EACnCC,aAAkC,EAClCC,eAA+B;IAH/B,kBAAa,GAAbH,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IACjB,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IApElB,qBAAgB,GAAqB;MAC1CC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;KACf;IAES,aAAQ,GAAG,IAAIX,YAAY,EAAE;IAEvC,iBAAY,GAAG,IAAIC,SAAS,CAAC,EAAE,CAAC;IAChC,kBAAa,GAAG,EAAE;IAClB,mBAAc,GAAwB,CAClC;MACIW,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,YAAY;QACnBC,WAAW,EAAE,mCAAmC;QAChDC,QAAQ,EAAE,KAAK;QACfJ,IAAI,EAAE;OACT;MACDK,YAAY,EAAE;KACjB,EACD;MACIN,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,mCAAmC;QAChDC,QAAQ,EAAE,IAAI;QACdJ,IAAI,EAAE,QAAQ;QACdM,GAAG,EAAE;OACR;MACDD,YAAY,EAAE,EAAE;MAChBE,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNJ,QAAQ,EAAE,IAAI,CAACX,iBAAiB,CAACgB,OAAO,CAAC,6BAA6B,CAAC;UACvEH,GAAG,EAAE,IAAI,CAACb,iBAAiB,CAACgB,OAAO,CAAC,2CAA2C;;;KAG1F,EACD;MACIV,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,CAEJ;EASD;EAEAU,QAAQ;IACJ,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,CAACN,YAAY,GAAG,IAAI,CAACO,gBAAgB,CAACf,YAAY;IACxE,IAAI,CAACc,cAAc,CAAC,CAAC,CAAC,CAACN,YAAY,GAAG,IAAI,CAACO,gBAAgB,CAAChB,UAAU;IAEtE,IAAI,IAAI,CAACgB,gBAAgB,CAACd,UAAU,EAAE;MAClC,IAAI,CAACa,cAAc,CAAC,CAAC,CAAC,CAACN,YAAY,GAAG,IAAI,CAACO,gBAAgB,CAACC,WAAW;MACvE,IAAI,CAACF,cAAc,CAAC,CAAC,CAAC,CAACN,YAAY,GAAG,IAAI,CAACO,gBAAgB,CAACE,cAAc;MAC1E,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC,CAACN,YAAY,GAAG,IAAI,CAACO,gBAAgB,CAACd,UAAU;;EAE9E;EAEAiB,iBAAiB,CAACC,KAAK;IACnBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,IAAI,CAACrB,eAAe,CAACwB,IAAI,EAAE;IAE3B,MAAMC,MAAM,GAAG,IAAI,CAACR,gBAAgB,CAACd,UAAU,GACzC,IAAI,CAACJ,aAAa,CAAC2B,WAAW,CAACL,KAAK,CAAC,GACrC,IAAI,CAACtB,aAAa,CAAC4B,QAAQ,CAACN,KAAK,CAAC;IAExCI,MAAM,CAACG,SAAS,CAAC;MACbC,IAAI,EAAGC,GAAG,IAAI;QACVR,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEO,GAAG,CAAC;QACvB,IAAI,IAAI,CAACb,gBAAgB,CAACd,UAAU,EAAE;UAClCT,IAAI,CAACqC,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAAClC,iBAAiB,CAACgB,OAAO,CAAC,UAAU,CAAC;YACjDmB,IAAI,EAAE,IAAI,CAACnC,iBAAiB,CAACgB,OAAO,CAAC,4BAA4B,CAAC;YAClEoB,IAAI,EAAE;WACT,CAAC;;QAEN,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACN,GAAG,CAAC;QACvB,IAAI,CAACjC,aAAa,CAACwC,UAAU,EAAE;MACnC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACbhB,OAAO,CAACgB,KAAK,CACT,IAAI,CAACrB,gBAAgB,CAACd,UAAU,GAC1B,uBAAuB,GACvB,qBAAqB,EAC3BmC,KAAK,CACR;MACL,CAAC;MACDC,QAAQ,EAAE,MAAK;QACX,IAAI,CAACvC,eAAe,CAACwC,OAAO,EAAE;MAClC;KACH,CAAC;EACN;EAEAC,UAAU;IACN,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC7C,aAAa,CAACwC,UAAU,EAAE;EACnC;EAEAM,SAAS;IACL,IAAI,CAACC,YAAY,CAACC,KAAK,EAAE;EAC7B;EAAC;qBAhIQlD,uBAAuB;EAAA;EAAA;UAAvBA,uBAAuB;IAAAmD;IAAAC;MAAA9B;IAAA;IAAA+B;MAAAb;IAAA;IAAAc;IAAAC;IAAAC;IAAAC;MAAA;QCxBpCC,8BAA0B;QACqBA,YAA6B;;QAAAA,iBAAK;QAC7EA,iCAKC;QAFOA;UAAA,OAASC,gBAAY;QAAA,EAAC;QAG1BD,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG/CA,+BAGC;QADOA;UAAA,OAAYC,wCAAgC;QAAA,EAAC;QAEjDD,8BAAkD;QAKtCA;UAAA,OAAUC,wCAAgC;QAAA,EAAC;QAClDD,iBAAc;QAEnBA,+BAA0B;QAIlBA,aACJ;;QAAAA,iBAAS;;;QA3B8BA,eAA6B;QAA7BA,uDAA6B;QAWpEA,eAA0B;QAA1BA,4CAA0B;QAKlBA,eAAqB;QAArBA,uCAAqB;QAQzBA,eAAiC;QAAjCA,mDAAiC;QAEjCA,eACJ;QADIA,gEACJ", "names": ["EventEmitter", "FormGroup", "<PERSON><PERSON>", "ModalCrudBreakComponent", "constructor", "_modalService", "_translateService", "_autoSchedule", "_loadingService", "locationId", "tournamentId", "timeSlotId", "key", "type", "props", "label", "placeholder", "required", "defaultValue", "min", "validation", "messages", "instant", "ngOnInit", "addBreakFields", "breakModalParams", "description", "breakDurations", "onSubmitCrudBreak", "model", "console", "log", "show", "action", "updateBreak", "addBreak", "subscribe", "next", "res", "fire", "title", "text", "icon", "onSubmit", "emit", "dismissAll", "error", "complete", "dismiss", "closeModal", "addBreakModel", "clearForm", "addBreakForm", "reset", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-crud-break\\modal-crud-break.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-crud-break\\modal-crud-break.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { NgbModal } from \"@ng-bootstrap/ng-bootstrap\";\r\nimport { FormGroup } from \"@angular/forms\";\r\nimport { FormlyFieldConfig } from \"@ngx-formly/core\";\r\nimport { TranslateService } from \"@ngx-translate/core\";\r\nimport { AutoScheduleService } from \"../../../../services/auto-schedule.service\";\r\nimport { LoadingService } from \"../../../../services/loading.service\";\r\nimport Swal from 'sweetalert2';\r\n\r\nexport type BreakModalParams = {\r\n    locationId: number | null;\r\n    tournamentId: number | null;\r\n    timeSlotId?: number | null;\r\n    description?: string | null;\r\n    breakDurations?: number | null;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-modal-crud-break',\r\n    templateUrl: './modal-crud-break.component.html',\r\n    styleUrls: ['./modal-crud-break.component.scss']\r\n})\r\n\r\n\r\nexport class ModalCrudBreakComponent {\r\n\r\n    @Input() breakModalParams: BreakModalParams = {\r\n        locationId: null,\r\n        tournamentId: null,\r\n        timeSlotId: null,\r\n    };\r\n\r\n    @Output() onSubmit = new EventEmitter();\r\n\r\n    addBreakForm = new FormGroup({});\r\n    addBreakModel = {};\r\n    addBreakFields: FormlyFieldConfig[] = [\r\n        {\r\n            key: 'tournament_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'location_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'description',\r\n            type: 'input',\r\n            props: {\r\n                label: 'Event name',\r\n                placeholder: 'Enter event name (Default: Break)',\r\n                required: false,\r\n                type: 'text'\r\n            },\r\n            defaultValue: \"Break\"\r\n        },\r\n        {\r\n            key: 'break_durations',\r\n            type: 'input',\r\n            props: {\r\n                label: 'Break duration',\r\n                placeholder: 'Enter break duration (in minutes)',\r\n                required: true,\r\n                type: 'number',\r\n                min: 1\r\n            },\r\n            defaultValue: 30,\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Break duration is required.'),\r\n                    min: this._translateService.instant('Break duration must be at least 1 minute.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'time_slot_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        }\r\n\r\n    ];\r\n\r\n    constructor(\r\n        private _modalService: NgbModal,\r\n        private _translateService: TranslateService,\r\n        private _autoSchedule: AutoScheduleService,\r\n        private _loadingService: LoadingService,\r\n    ) {\r\n\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.addBreakFields[0].defaultValue = this.breakModalParams.tournamentId;\r\n        this.addBreakFields[1].defaultValue = this.breakModalParams.locationId;\r\n\r\n        if (this.breakModalParams.timeSlotId) {\r\n            this.addBreakFields[2].defaultValue = this.breakModalParams.description;\r\n            this.addBreakFields[3].defaultValue = this.breakModalParams.breakDurations;\r\n            this.addBreakFields[4].defaultValue = this.breakModalParams.timeSlotId;\r\n        }\r\n    }\r\n\r\n    onSubmitCrudBreak(model) {\r\n        console.log(model);\r\n        this._loadingService.show();\r\n\r\n        const action = this.breakModalParams.timeSlotId\r\n            ? this._autoSchedule.updateBreak(model)\r\n            : this._autoSchedule.addBreak(model);\r\n\r\n        action.subscribe({\r\n            next: (res) => {\r\n                console.log('res', res);\r\n                if (this.breakModalParams.timeSlotId) {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Success!'),\r\n                        text: this._translateService.instant('Break updated successfully'),\r\n                        icon: 'success'\r\n                    });\r\n                }\r\n                this.onSubmit.emit(res);\r\n                this._modalService.dismissAll();\r\n            },\r\n            error: (error) => {\r\n                console.error(\r\n                    this.breakModalParams.timeSlotId\r\n                        ? 'Error updating break:'\r\n                        : 'Error adding break:',\r\n                    error\r\n                );\r\n            },\r\n            complete: () => {\r\n                this._loadingService.dismiss();\r\n            }\r\n        });\r\n    }\r\n\r\n    closeModal() {\r\n        this.addBreakModel = {};\r\n        this._modalService.dismissAll();\r\n    }\r\n\r\n    clearForm() {\r\n        this.addBreakForm.reset();\r\n    }\r\n}\r\n", "<div class=\"modal-header\">\r\n    <h5 class=\"modal-title\" id=\"modalAddBreak\">{{ \"Add Break\" | translate }}</h5>\r\n    <button\r\n            type=\"button\"\r\n            class=\"close\"\r\n            (click)=\"closeModal()\"\r\n            aria-label=\"Close\"\r\n    >\r\n        <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n</div>\r\n<form\r\n        [formGroup]=\"addBreakForm\"\r\n        (ngSubmit)=\"onSubmitCrudBreak(addBreakModel)\"\r\n>\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n        <formly-form\r\n                [form]=\"addBreakForm\"\r\n                [fields]=\"addBreakFields\"\r\n                [model]=\"addBreakModel\"\r\n                (submit)=\"onSubmitCrudBreak(addBreakModel)\"\r\n        ></formly-form>\r\n    </div>\r\n    <div class=\"modal-footer\">\r\n        <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n            [disabled]=\"addBreakForm.invalid\"\r\n        >\r\n            {{ 'Submit' | translate }}\r\n        </button>\r\n    </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}