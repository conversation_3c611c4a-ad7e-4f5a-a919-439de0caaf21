{"ast": null, "code": "import { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport { AppConfig } from 'app/app-config';\nimport { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"app/services/registration.service\";\nimport * as i9 from \"app/services/season.service\";\nimport * as i10 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i11 from \"@angular/platform-browser\";\nconst _c0 = [\"rowActionBtn\"];\nconst _c1 = [\"modalLimitTimeUpdateScore\"];\nconst _c2 = [\"modalManageReferee\"];\nfunction LeagueComponent_ng_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", season_r8.name, \" \");\n  }\n}\nfunction LeagueComponent_ng_container_8_div_4_ng_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r12.name, \" \");\n  }\n}\nfunction LeagueComponent_ng_container_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23)(4, \"ng-select\", 24);\n    i0.ɵɵlistener(\"ngModelChange\", function LeagueComponent_ng_container_8_div_4_Template_ng_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.groupId = $event);\n    })(\"change\", function LeagueComponent_ng_container_8_div_4_Template_ng_select_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onSelectedGroupChange($event));\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, LeagueComponent_ng_container_8_div_4_ng_option_6_Template, 2, 2, \"ng-option\", 25);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(5, 3, \"Filter Groups\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r10.groupId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.currentGroups);\n  }\n}\nfunction LeagueComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function LeagueComponent_ng_container_8_Template_button_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const variable_r9 = restoredCtx.ngIf;\n      return i0.ɵɵresetView(variable_r9.isShowFilter = !variable_r9.isShowFilter);\n    });\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, LeagueComponent_ng_container_8_div_4_Template, 7, 5, \"div\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const variable_r9 = ctx.ngIf;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", variable_r9.isShowFilter);\n  }\n}\nfunction LeagueComponent_ng_template_13_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" * \", i0.ɵɵpipeBind1(2, 1, ctx_r19.limitTimeError), \" \");\n  }\n}\nfunction LeagueComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h5\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function LeagueComponent_ng_template_13_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const modal_r18 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r18.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"form\", 31);\n    i0.ɵɵlistener(\"ngSubmit\", function LeagueComponent_ng_template_13_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onSubmitLimitTime(ctx_r22.limitTimeModel));\n    });\n    i0.ɵɵelementStart(8, \"div\", 32);\n    i0.ɵɵelement(9, \"formly-form\", 33);\n    i0.ɵɵtemplate(10, LeagueComponent_ng_template_13_p_10_Template, 3, 3, \"p\", 34);\n    i0.ɵɵelement(11, \"hr\");\n    i0.ɵɵelementStart(12, \"div\", 35)(13, \"p\", 36);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 37);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"button\", 39);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 9, \"Limit update score time\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.limitTimeForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"form\", ctx_r3.limitTimeForm)(\"fields\", ctx_r3.limitTimeFields)(\"model\", ctx_r3.limitTimeModel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.limitTimeError);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 11, \"* Input \\\"0\\\" means there's no limit on how long scores can be updated.\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 13, \"Warning: Submit will change all tournament leagues. Be sure before confirming!\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 15, \"Submit\"), \" \");\n  }\n}\nfunction LeagueComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-modal-manage-referees\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"seasonId\", ctx_r5.seasonId);\n  }\n}\nfunction LeagueComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function LeagueComponent_ng_template_17_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const data_r24 = restoredCtx.adtData;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onClickAutoSchedule(data_r24));\n    });\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-btn-dropdown-action\", 45);\n    i0.ɵɵlistener(\"emitter\", function LeagueComponent_ng_template_17_Template_app_btn_dropdown_action_emitter_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const emitter_r25 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r25($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r24 = ctx.adtData;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"actions\", ctx_r7.rowActions)(\"data\", data_r24);\n  }\n}\nconst _c3 = function () {\n  return {\n    isShowFilter: true\n  };\n};\nexport class LeagueComponent {\n  constructor(route, _router, _commonsService, _http, _trans, renderer, _teamService, _modalService, _loadingService, _registrationService, _seasonService, _translateService, _coreSidebarService, _titleService) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._seasonService = _seasonService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._titleService = _titleService;\n    this.dtElement = DataTableDirective;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.model = {};\n    this.tableID = 'league-table';\n    this.isInitTable = false;\n    this.selectedSeason = {};\n    this.selectedGroup = {};\n    this.table_name = 'tournament-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Create New Tournament'),\n        edit: 'Edit tournament',\n        remove: 'Delete tournament'\n      },\n      url: `${environment.apiUrl}/tournaments/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields_subject = new Subject();\n    this.fields = [{\n      key: 'group_id',\n      type: 'ng-select',\n      props: {\n        label: this._translateService.instant('Group'),\n        placeholder: this._translateService.instant('Select group'),\n        closeOnSelect: true,\n        required: true,\n        options: []\n      }\n    }, {\n      key: 'name',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Name'),\n        placeholder: this._translateService.instant('Enter name of tournament'),\n        required: true\n      }\n    }, {\n      key: 'type',\n      type: 'radio',\n      props: {\n        label: this._translateService.instant('Type'),\n        placeholder: this._translateService.instant('Select tournament type'),\n        required: true,\n        options: [{\n          value: AppConfig.TOURNAMENT_TYPES.league,\n          label: this._translateService.instant(AppConfig.TOURNAMENT_TYPES.league)\n        }, {\n          value: AppConfig.TOURNAMENT_TYPES.groups_knockouts,\n          label: this._translateService.instant(AppConfig.TOURNAMENT_TYPES.groups_knockouts)\n        }]\n      }\n    }, {\n      key: 'type_knockout',\n      type: 'radio',\n      props: {\n        label: this._translateService.instant('Type of group + knockout'),\n        placeholder: this._translateService.instant('Select type of group + knockout'),\n        required: true,\n        options: [{\n          value: AppConfig.KNOCKOUT_TYPES.type1,\n          label: this._translateService.instant(AppConfig.KNOCKOUT_TYPES.type1)\n        }, {\n          value: AppConfig.KNOCKOUT_TYPES.type2,\n          label: this._translateService.instant(AppConfig.KNOCKOUT_TYPES.type2)\n        }, {\n          value: AppConfig.KNOCKOUT_TYPES.type3,\n          label: this._translateService.instant(AppConfig.KNOCKOUT_TYPES.type3)\n        }, {\n          value: AppConfig.KNOCKOUT_TYPES.type4,\n          label: this._translateService.instant(AppConfig.KNOCKOUT_TYPES.type4)\n        }]\n      },\n      expressions: {\n        hide: `model.type != \"${AppConfig.TOURNAMENT_TYPES.groups_knockouts}\"`\n      }\n    }, {\n      key: 'number_groups',\n      type: 'ng-select',\n      props: {\n        label: this._translateService.instant('Number of groups'),\n        description: this._translateService.instant('Please enter the group number to the power of 2'),\n        options: [{\n          value: 2,\n          label: '2'\n        }, {\n          value: 4,\n          label: '4'\n        }, {\n          value: 8,\n          label: '8'\n        }, {\n          value: 16,\n          label: '16'\n        }],\n        type: 'number',\n        required: true\n      },\n      defaultValue: 2,\n      expressions: {\n        hide: `model.type != \"${AppConfig.TOURNAMENT_TYPES.groups_knockouts}\"`\n      }\n    }, {\n      key: 'no_knockout_type_1',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Number of knockouts'),\n        placeholder: this._translateService.instant('Enter number of knockouts'),\n        description: this._translateService.instant('Ex: Enter 4 for Cup,Plate,Bowl,Shield'),\n        type: 'number',\n        required: true,\n        readonly: true\n      },\n      defaultValue: 4,\n      expressions: {\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type1}\"`\n      }\n    }, {\n      key: 'no_knockout_type_2',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Number of knockouts'),\n        placeholder: this._translateService.instant('Enter number of knockouts'),\n        description: this._translateService.instant('Ex: Enter 4 for Cup,Plate,Bowl,Shield'),\n        type: 'number',\n        required: true,\n        readonly: true\n      },\n      defaultValue: 2,\n      expressions: {\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type2}\"`\n      }\n    }, {\n      key: 'no_knockout_type_3',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Number of knockouts'),\n        placeholder: this._translateService.instant('Enter number of knockouts'),\n        description: this._translateService.instant('Ex: Enter 4 for Cup,Plate,Bowl,Shield'),\n        type: 'number',\n        required: true,\n        readonly: true\n      },\n      defaultValue: 1,\n      expressions: {\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type3}\"`\n      }\n    }, {\n      key: 'no_knockout_type_4',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Number of knockouts'),\n        placeholder: this._translateService.instant('Enter number of knockouts'),\n        description: this._translateService.instant('Ex: Enter 4 for Cup,Plate,Bowl,Shield'),\n        type: 'number',\n        required: true\n      },\n      defaultValue: 1,\n      expressions: {\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type4}\"`\n      }\n    }, {\n      key: 'id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n    this.rowActions = [{\n      type: 'collection',\n      buttons: [{\n        label: 'Edit',\n        onClick: row => {\n          this.editor('edit', row);\n        },\n        icon: 'fa-regular fa-pen-to-square'\n      }, {\n        label: 'Delete',\n        onClick: row => {\n          this.editor('remove', row);\n        },\n        icon: 'fa-regular fa-trash'\n      }]\n    }];\n    // setup modal limit time edit score\n    this.limitTimeForm = new FormGroup({});\n    this.limitTimeModel = {\n      season_id: null,\n      limit_time: null\n    };\n    this.limitTimeFields = [{\n      key: 'season_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'limit_time',\n      type: 'input',\n      props: {\n        placeholder: this._translateService.instant('Enter limit update score time (hours)'),\n        type: 'number',\n        min: 0\n      }\n    }];\n    this.limitTimeError = null;\n    _titleService.setTitle('Manage Leagues');\n  }\n  set tableManageLeague(data) {\n    let str_data = JSON.stringify(data);\n    // merge data if exist\n    let exist_data = this.tableManageLeague;\n    if (exist_data) {\n      str_data = JSON.stringify({\n        ...exist_data,\n        ...data\n      });\n    }\n    localStorage.setItem('tableManageLeague', str_data);\n  }\n  get tableManageLeague() {\n    let data = localStorage.getItem('tableManageLeague');\n    if (data) {\n      return JSON.parse(data);\n    }\n    return null;\n  }\n  _getCurrentSeason() {\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.currentSeasons = data;\n      // if not exist selectedSeason tableManageLeague then set default, else get from tableManageLeague\n      let tableManageLeague = this.tableManageLeague;\n      if (!tableManageLeague?.selectedSeason) {\n        let tableData = {\n          selectedSeason: this.currentSeasons[0].id\n        };\n        this.tableManageLeague = tableData;\n      }\n      // check if selectedSeason is not in currentSeasons then set selectedSeason = currentSeasons[0]\n      else if (!this.currentSeasons.find(season => {\n        return season.id == tableManageLeague.selectedSeason;\n      })) {\n        let tableData = {\n          selectedSeason: this.currentSeasons[0].id\n        };\n        this.tableManageLeague = tableData;\n      }\n      this.selectedSeason = this.tableManageLeague.selectedSeason;\n      this.seasonId = this.selectedSeason;\n      this.limitTimeModel = {\n        season_id: this.seasonId,\n        limit_time: this.currentSeasons.find(season => season.id == this.seasonId)?.default_limit_update_score_time ?? 0\n      };\n      this._getGroupsBySeason();\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  _getGroupsBySeason() {\n    if (!this.seasonId) return;\n    this._loadingService.show();\n    this._seasonService.getGroupsBySeason(this.seasonId).subscribe(data => {\n      this.currentGroups = data;\n      // if not exist selectedGroup tableManageLeague then set default, else get from tableManageLeague\n      let tableManageLeague = this.tableManageLeague;\n      if (!tableManageLeague?.selectedGroup) {\n        let tableData = {\n          selectedGroup: undefined\n        };\n        this.tableManageLeague = tableData;\n      } else if (tableManageLeague?.selectedGroup?.season_id != this.seasonId) {\n        this.tableManageLeague = {\n          selectedGroup: undefined\n        };\n      }\n      // check if selectedGroup is not in currentGroups then set selectedGroup = currentGroups[0]\n      else if (!this.currentGroups.find(group => {\n        return group.id == tableManageLeague.selectedGroup.id;\n      })) {\n        this.tableManageLeague = {\n          selectedGroup: undefined\n        };\n      }\n      this.selectedGroup = this.tableManageLeague.selectedGroup;\n      console.log(this.selectedGroup);\n      this.groupId = this.selectedGroup ? this.selectedGroup.id : undefined;\n      this.converGroupsToOptions(this.currentGroups);\n      if (!this.isInitTable) {\n        this.isInitTable = true;\n        this.buildTable();\n        this.dtTrigger.next(this.dtOptions);\n        this.addListener();\n      } else {\n        // reload datatable\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  converGroupsToOptions(groups) {\n    let options = [];\n    groups.forEach(group => {\n      options.push({\n        value: group.id,\n        label: group.name\n      });\n    });\n    // update options of field group_id\n    this.fields[0].props.options = options;\n    this.fields_subject.next(this.fields);\n  }\n  onSelectedSeasonChange($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this.tableManageLeague = {\n        selectedSeason: this.seasonId\n      };\n      this.limitTimeModel = {\n        season_id: this.seasonId,\n        limit_time: this.currentSeasons.find(season => season.id == this.seasonId)?.default_limit_update_score_time ?? 0\n      };\n      this._getGroupsBySeason();\n      resolve(true);\n    });\n  }\n  onSelectedGroupChange($event) {\n    // find group by id in currentGroups\n    this.groupId = $event;\n    console.log('groupId', this.groupId);\n    this.selectedGroup = this.currentGroups.find(group => {\n      return group.id == this.groupId;\n    });\n    this.tableManageLeague = {\n      selectedGroup: this.selectedGroup\n    };\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('All Tournaments'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('All Tournaments'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n  }\n  buildTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom_table_card,\n      ajax: (dataTablesParameters, callback) => {\n        dataTablesParameters.group_id = this.groupId;\n        dataTablesParameters.season_id = this.seasonId;\n        this._http.post(`${environment.apiUrl}/tournaments/all-in-group`, dataTablesParameters).subscribe(resp => {\n          this._commonsService.customSearchInput(this.tableID);\n          callback({\n            // this function callback is used to return data to datatable\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      stateSave: false,\n      rowGroup: {\n        dataSrc: 'group.name'\n      },\n      order: [[2, 'asc']],\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      responsive: true,\n      scrollX: false,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        ...{\n          search: '',\n          searchPlaceholder: this._trans.instant('Search'),\n          attr: {\n            class: 'w-100'\n          }\n        }\n      },\n      createdRow: function (row, data, dataIndex, cells) {\n        $(row).addClass('col-12 col-md-6 col-lg-4 p-0');\n      },\n      columnDefs: [\n        // { responsivePriority: 1, targets: -1 },\n        // { responsivePriority: 2, targets: 2 },\n      ],\n      columns: [{\n        title: 'Name',\n        data: 'name',\n        className: 'h3 w-bold col-12 d-table-cell text-warp'\n      }, {\n        data: 'id',\n        className: 'col-1 d-table-cell',\n        defaultContent: '',\n        ngTemplateRef: {\n          ref: this.rowActionBtn,\n          context: {\n            captureEvents: this.onCaptureEvent.bind(this)\n          }\n        }\n      }, {\n        data: 'group.name',\n        className: 'd-flex',\n        type: 'any-number',\n        visible: false\n      }, {\n        data: null,\n        className: 'd-flex text-center border-right-0',\n        render: (data, type, row) => {\n          // count stages with type = knockout\n          let knockout_count = row.stages.filter(stage => {\n            return stage.type == AppConfig.TOURNAMENT_TYPES.knockout;\n          }).length;\n          // count stages with type = league\n          let league_count = row.stages.filter(stage => {\n            return stage.type == AppConfig.TOURNAMENT_TYPES.league;\n          }).length;\n          // count stages with type = groups\n          let groups_count = row.stages.filter(stage => {\n            return stage.type == AppConfig.TOURNAMENT_TYPES.groups;\n          }).length;\n          let col_knockout = knockout_count > 1 ? 6 : 12;\n          let col_league = league_count > 1 ? 6 : 12;\n          let col_groups = groups_count > 1 ? 6 : 12;\n          let btns = '';\n          let btn_knockout = `<div class=\"col-${col_knockout} pt-50 pb-50\" >\n              <button type=\"button\" tournament_id=\"${row.id}\" stage_id=\"{stage_id}\" class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-danger btn-block\" > \n              <i style=\"margin-right: 0.5rem\" class=\"bi bi-diagram-2 fa-xl\"></i>\n              {stage_name}\n              </button>\n            </div>`;\n          let btn_league = `<div class=\"col-${col_league} pt-50 pb-50\" >\n              <button type=\"button\" tournament_id=\"${row.id}\" stage_id=\"{stage_id}\" class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-success btn-block\" >\n              <i style=\"margin-right: 0.5rem;\" class=\"bi bi-trophy\"></i>\n              {stage_name}\n              </button>\n            </div>`;\n          let btn_groups = `<div class=\"col-${col_groups} pt-50 pb-50\" >\n              <button type=\"button\" tournament_id=\"${row.id}\" stage_id=\"{stage_id}\" class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-warning btn-block\" >\n              <i style=\"margin-right: 0.5rem;\" class=\"bi bi-ui-checks-grid\"></i>\n              {stage_name}\n              </button>\n            </div>`;\n          // for each row.stages, create a button\n          row.stages.forEach(stage => {\n            let stage_name = stage.name;\n            stage_name = this._translateService.instant(stage_name);\n            let btn = '';\n            switch (stage.type) {\n              case AppConfig.TOURNAMENT_TYPES.knockout:\n                btn = btn_knockout.replace('{stage_name}', stage_name);\n                btn = btn.replace('{stage_id}', stage.id);\n                btns += btn;\n                break;\n              case AppConfig.TOURNAMENT_TYPES.league:\n                btn = btn_league.replace('{stage_name}', stage_name);\n                btn = btn.replace('{stage_id}', stage.id);\n                btns += btn;\n                break;\n              case AppConfig.TOURNAMENT_TYPES.groups:\n                btn = btn_groups.replace('{stage_name}', stage_name);\n                btn = btn.replace('{stage_id}', stage.id);\n                btns += btn;\n                break;\n              default:\n                break;\n            }\n          });\n          let html = `<div class=\"btn-stages row flex-grow-1 justify-content-start\" >\n            ${btns}\n            </div>`;\n          // return 2 button\n          return html;\n        }\n      }, {\n        data: 'group_id',\n        visible: false\n      }],\n      lengthMenu: [[25, 50, 100, -1], [25, 50, 100, 'All']],\n      displayLength: -1,\n      paging: false,\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom_table_card,\n        buttons: [{\n          text: '<i class=\"feather icon-plus\"></i> ' + this._translateService.instant('New Tournament'),\n          action: () => this.editor('create')\n        }, {\n          className: 'btn btn-outline-primary',\n          text: '<i class=\"feather icon-settings\"></i> ' + this._translateService.instant('Limit update score time'),\n          init: function (api, node, config) {\n            $(node).removeClass().addClass('btn btn-outline-primary');\n          },\n          action: () => this.openModalLimitTime()\n        }, {\n          className: 'btn btn-outline-primary',\n          text: '<i class=\"feather icon-settings\"></i> ' + this._translateService.instant('Manage Referees'),\n          init: function (api, node, config) {\n            $(node).removeClass().addClass('btn btn-outline-primary');\n          },\n          action: () => this.openModalManageReferee()\n        }]\n      }\n    };\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  editor(action, row) {\n    switch (action) {\n      case 'create':\n        this.fields[0].props.disabled = false;\n        this.fields_subject.next(this.fields);\n        break;\n      case 'edit':\n        // clone fields and remove field 'type'\n        let clone_fields = JSON.parse(JSON.stringify(this.fields));\n        const hiddenFieldIndices = [2];\n        clone_fields = clone_fields.filter((_, index) => !hiddenFieldIndices.includes(index));\n        // disable field 'group_id'\n        clone_fields[0].props.disabled = true;\n        this.fields_subject.next(clone_fields);\n        break;\n      case 'remove':\n        break;\n      default:\n        break;\n    }\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  openModalLimitTime() {\n    this._modalService.open(this.modalLimitTimeUpdateScore, {\n      centered: true,\n      size: 'lg',\n      beforeDismiss: () => {\n        this.limitTimeError = null;\n        this.limitTimeModel = {\n          season_id: this.seasonId,\n          limit_time: this.currentSeasons.find(season => season.id == this.seasonId)?.default_limit_update_score_time ?? 0\n        };\n        return true;\n      }\n    });\n  }\n  openModalManageReferee() {\n    console.log(this.modalManageReferee);\n    this._modalService.open(this.modalManageReferee, {\n      // centered: true,\n      size: 'lg',\n      scrollable: true\n    });\n  }\n  onSubmitLimitTime(model) {\n    this.limitTimeError = null;\n    if (!model.limit_time && model.limit_time != 0) {\n      this.limitTimeError = 'Please enter limit update score time';\n      return;\n    }\n    if (model.limit_time < 0 || model.limit_time > 3000) {\n      this.limitTimeError = 'Please enter a valid number between 0 and 3000';\n      return;\n    }\n    this._seasonService.updateLimitTime(model).subscribe(response => {\n      Swal.fire({\n        title: 'Success',\n        text: response.message,\n        icon: 'success',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n      this.currentSeasons.forEach(season => {\n        if (season.id === this.seasonId) {\n          season.default_limit_update_score_time = model.limit_time;\n        }\n      });\n      this._modalService.dismissAll();\n    }, error => {\n      this.limitTimeError = error.message;\n    });\n  }\n  addListener() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament_id')) {\n        let tournament_id = event.target.getAttribute('tournament_id');\n        let stage_id = event.target.getAttribute('stage_id');\n        //  navigate to path ':tournament_id/stages/:stage_id'\n        this._router.navigate([tournament_id, 'stages', stage_id], {\n          relativeTo: this.route\n        });\n      }\n    });\n  }\n  onSuccess($event) {\n    // reload table\n    this.dtElement.dtInstance.then(dtInstance => {\n      this._loadingService.show();\n      dtInstance.ajax.reload();\n    });\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.unlistener();\n  }\n  onClickAutoSchedule(data) {\n    console.log('data', data);\n    this._router.navigate(['leagues', 'manage', data.id, 'auto-schedule']);\n  }\n  static #_ = this.ɵfac = function LeagueComponent_Factory(t) {\n    return new (t || LeagueComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.RegistrationService), i0.ɵɵdirectiveInject(i9.SeasonService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i10.CoreSidebarService), i0.ɵɵdirectiveInject(i11.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueComponent,\n    selectors: [[\"app-league\"]],\n    viewQuery: function LeagueComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowActionBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalLimitTimeUpdateScore = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalManageReferee = _t.first);\n      }\n    },\n    decls: 19,\n    vars: 19,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\", \"m-0\"], [1, \"col\", \"col-md-6\", \"col-lg-3\", \"mb-1\", \"pl-25\", \"pr-50\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"datatable\", \"\", 1, \"card_table\", 3, \"dtOptions\", \"dtTrigger\", \"id\"], [1, \"row\", \"ml-0\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields_subject\", \"fields\", \"params\", \"new_model\", \"onSuccess\"], [\"modalLimitTimeUpdateScore\", \"\"], [\"modalManageReferee\", \"\"], [\"rowActionBtn\", \"\"], [3, \"value\"], [1, \"col-auto\", \"col-md\", \"pl-50\", \"pr-0\", \"d-flex\", \"justify-content-end\", \"align-items-start\"], [\"type\", \"button\", 1, \"btn\", \"btn-flat\", \"pl-25\", \"pr-25\", 3, \"click\"], [1, \"fa-light\", \"fa-filter-list\", \"fa-xl\", \"mr-25\"], [\"class\", \"col-12 col-md-auto p-25 mb-1\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-auto\", \"p-25\", \"mb-1\"], [1, \"row\", \"mr-0\"], [1, \"col\", \"pr-0\"], [2, \"min-width\", \"130px\"], [3, \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [\"name\", \"group.id\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"name\", \"group.id\", 3, \"value\"], [1, \"modal-header\"], [\"id\", \"modalLimitTimeUpdateScore\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\"], [1, \"m-0\", \"badge-align\", \"badge\", \"badge-light-default\", \"text-dark\", \"w-auto\", \"text-wrap\", \"text-md-start\"], [1, \"m-0\", \"badge-align\", \"badge\", \"badge-light-warning\", \"w-auto\", \"text-wrap\", \"text-md-start\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"], [1, \"text-danger\"], [3, \"seasonId\"], [1, \"d-flex\", \"flex-row\", \"align-items-center\", \"justify-content-end\", 2, \"gap\", \"4px\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"Auto Schedule\", 1, \"auto-schedule-btn\", \"btn\", \"btn-link\", 3, \"click\"], [1, \"fa-solid\", \"fa-wand-magic-sparkles\"], [\"btnStyle\", \"font-size:15px;color:black!important\", \"btnActionStyle\", \"font-size:20px;color:black!important\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function LeagueComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"ng-select\", 5);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueComponent_Template_ng_select_ngModelChange_5_listener($event) {\n          return ctx.selectedSeason = $event;\n        })(\"change\", function LeagueComponent_Template_ng_select_change_5_listener($event) {\n          return ctx.onSelectedSeasonChange($event);\n        });\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵtemplate(7, LeagueComponent_ng_option_7_Template, 2, 2, \"ng-option\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, LeagueComponent_ng_container_8_Template, 5, 1, \"ng-container\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"table\", 8);\n        i0.ɵɵelement(10, \"tbody\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"core-sidebar\", 10)(12, \"app-editor-sidebar\", 11);\n        i0.ɵɵlistener(\"onSuccess\", function LeagueComponent_Template_app_editor_sidebar_onSuccess_12_listener($event) {\n          return ctx.onSuccess($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(13, LeagueComponent_ng_template_13_Template, 23, 17, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(15, LeagueComponent_ng_template_15_Template, 1, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(17, LeagueComponent_ng_template_17_Template, 4, 2, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(6, 16, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx.selectedSeason);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.currentSeasons);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction0(18, _c3));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger)(\"id\", ctx.tableID);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields_subject\", ctx.fields_subject)(\"fields\", ctx.fields)(\"params\", ctx.params)(\"new_model\", ctx.model);\n      }\n    },\n    styles: [\".mr-05 {\\n  margin-right: 0.125rem;\\n}\\n\\n.badge-align {\\n  text-align: center;\\n}\\n\\n@media (min-width: 768px) {\\n  .badge-align {\\n    text-align: left;\\n  }\\n}\\n.auto-schedule-btn {\\n  color: #ccc;\\n  transition: all;\\n  transition-duration: 300ms;\\n  padding: 2px;\\n}\\n\\n.auto-schedule-btn:hover {\\n  color: #616161;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlL2xlYWd1ZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLHNCQUFBO0FBQ0Y7O0FBR0E7RUFDRSxrQkFBQTtBQUFGOztBQUdBO0VBQ0U7SUFDRSxnQkFBQTtFQUFGO0FBQ0Y7QUFHQTtFQUNFLFdBQUE7RUFDQSxlQUFBO0VBQ0EsMEJBQUE7RUFDQSxZQUFBO0FBREY7O0FBSUE7RUFDRSxjQUFBO0FBREYiLCJzb3VyY2VzQ29udGVudCI6WyIubXItMDUge1xyXG4gIG1hcmdpbi1yaWdodDogMC4xMjVyZW07XHJcbn1cclxuXHJcblxyXG4uYmFkZ2UtYWxpZ24ge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XHJcbiAgLmJhZGdlLWFsaWduIHtcclxuICAgIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgfVxyXG59XHJcblxyXG4uYXV0by1zY2hlZHVsZS1idG4ge1xyXG4gIGNvbG9yOiAjY2NjO1xyXG4gIHRyYW5zaXRpb246IGFsbDtcclxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAzMDBtcztcclxuICBwYWRkaW5nOiAycHg7XHJcbn1cclxuXHJcbi5hdXRvLXNjaGVkdWxlLWJ0bjpob3ZlciB7XHJcbiAgY29sb3I6ICM2MTYxNjE7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAaA,SAASA,kBAAkB,QAAQ,oBAAoB;AAKvD,SAASC,WAAW,QAAQ,0BAA0B;AAGtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,SAAS,QAAQ,gBAAgB;AAK1C,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;IClBhCC,qCAAqE;IAAAA,YACrE;IAAAA,iBAAY;;;;IADqCA,oCAAmB;IAACA,eACrE;IADqEA,8CACrE;;;;;IAiBQA,qCAAkF;IAChFA,YACF;IAAAA,iBAAY;;;;IAFmCA,oCAAkB;IAC/DA,eACF;IADEA,+CACF;;;;;;IARVA,+BAAwE;IAILA;MAAAA;MAAA;MAAA;IAAA,EAAqB;MAAAA;MAAA;MAAA,OAC3DA,oDAA6B;IAAA,EAD8B;;IAE9EA,kGAEY;IACdA,iBAAY;;;;IALDA,eAA+C;IAA/CA,8EAA+C;IAACA,yCAAqB;IAEjDA,eAAgB;IAAhBA,+CAAgB;;;;;;IAdzDA,6BAAwD;IACtDA,+BAAqF;IAE3EA;MAAA;MAAA;MAAA,OAAWA,oEAA8C;IAAA;IAC/DA,wBAAmD;IACrDA,iBAAS;IAGXA,gFAaM;IACRA,0BAAe;;;;IAd8BA,eAA2B;IAA3BA,+CAA2B;;;;;IAuCxEA,6BAA8C;IAC5CA,YACF;;IAAAA,iBAAI;;;;IADFA,eACF;IADEA,+EACF;;;;;;IAXJA,+BAA0B;IAC+BA,YAA2C;;IAAAA,iBAAK;IACvGA,kCAA8F;IAA1DA;MAAA;MAAA;MAAA,OAASA,iCAAc,aAAa,CAAC;IAAA,EAAC;IACxEA,gCAAyB;IAAAA,sBAAO;IAAAA,iBAAO;IAG3CA,gCAAiF;IAA/CA;MAAAA;MAAA;MAAA,OAAYA,gEAAiC;IAAA,EAAC;IAC9EA,+BAAkD;IAChDA,kCAAsG;IACtGA,8EAEI;IACJA,sBAAI;IACJA,gCAAgC;IAK5BA,aACF;;IAAAA,iBAAI;IAEJA,8BAAoF;IAClFA,aACF;;IAAAA,iBAAI;IAIRA,gCAA0B;IAEtBA,aACF;;IAAAA,iBAAS;;;;IA7B4CA,eAA2C;IAA3CA,qEAA2C;IAK9FA,eAA2B;IAA3BA,gDAA2B;IAEhBA,eAAsB;IAAtBA,2CAAsB;IAC/BA,eAAoB;IAApBA,4CAAoB;IASpBA,eACF;IADEA,kIACF;IAGEA,eACF;IADEA,yIACF;IAMAA,eACF;IADEA,iEACF;;;;;IAMJA,gDAAuD;;;;IAA5BA,qDAAyB;;;;;;IAIpDA,+BAGC;IAGSA;MAAA;MAAA;MAAA;MAAA,OAASA,oDAAyB;IAAA,EAAC;IAEzCA,wBACK;IACPA,iBAAS;IACTA,mDAE+E;IAFjBA;MAAA;MAAA;MAAA,OAAWA,kCAAe;IAAA,EAAC;IAEVA,iBAA0B;;;;;IAFhFA,eAAsB;IAAtBA,2CAAsB;;;;;;;;ADnEnD,OAAM,MAAOC,eAAe;EAyS1BC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,oBAAyC,EACzCC,cAA6B,EAC7BC,iBAAmC,EACnCC,mBAAuC,EACvCC,aAAoB;IAbnB,UAAK,GAALb,KAAK;IACN,YAAO,GAAPC,OAAO;IACP,oBAAe,GAAfC,eAAe;IACf,UAAK,GAALC,KAAK;IACL,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IACR,iBAAY,GAAZC,YAAY;IACZ,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,yBAAoB,GAApBC,oBAAoB;IACpB,mBAAc,GAAdC,cAAc;IACd,sBAAiB,GAAjBC,iBAAiB;IACjB,wBAAmB,GAAnBC,mBAAmB;IACnB,kBAAa,GAAbC,aAAa;IApTtB,cAAS,GAAQtB,kBAAkB;IACnC,cAAS,GAAyB,IAAIE,OAAO,EAAe;IAC5D,cAAS,GAAQ,EAAE;IACnB,UAAK,GAAQ,EAAE;IACf,YAAO,GAAW,cAAc;IAChC,gBAAW,GAAY,KAAK;IAUrB,mBAAc,GAAQ,EAAE;IACxB,kBAAa,GAAQ,EAAE;IAEvB,eAAU,GAAG,kBAAkB;IAE/B,WAAM,GAAwB;MACnCqB,SAAS,EAAE,IAAI,CAACC,UAAU;MAC1BC,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAACN,iBAAiB,CAACO,OAAO,CAAC,uBAAuB,CAAC;QAC/DC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAG7B,WAAW,CAAC8B,MAAM,qBAAqB;MAC/CC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,mBAAc,GAAG,IAAI/B,OAAO,EAAO;IACnC,WAAM,GAAU,CACrB;MACEgC,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,OAAO,CAAC;QAC9CW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CAAC,cAAc,CAAC;QAC3DY,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;;KAEZ,EACD;MACEP,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,MAAM,CAAC;QAC7CW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CAAC,0BAA0B,CAAC;QACvEa,QAAQ,EAAE;;KAEb,EACD;MACEN,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,MAAM,CAAC;QAC7CW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CAAC,wBAAwB,CAAC;QACrEa,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,CACP;UACEC,KAAK,EAAEtC,SAAS,CAACuC,gBAAgB,CAACC,MAAM;UACxCP,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CACnCvB,SAAS,CAACuC,gBAAgB,CAACC,MAAM;SAEpC,EACD;UACEF,KAAK,EAAEtC,SAAS,CAACuC,gBAAgB,CAACE,gBAAgB;UAClDR,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CACnCvB,SAAS,CAACuC,gBAAgB,CAACE,gBAAgB;SAE9C;;KAGN,EACD;MACEX,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,0BAA0B,CAAC;QACjEW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CAAC,iCAAiC,CAAC;QAC9Ea,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,CACP;UACEC,KAAK,EAAEtC,SAAS,CAAC0C,cAAc,CAACC,KAAK;UACrCV,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CACnCvB,SAAS,CAAC0C,cAAc,CAACC,KAAK;SAEjC,EACD;UACEL,KAAK,EAAEtC,SAAS,CAAC0C,cAAc,CAACE,KAAK;UACrCX,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CACnCvB,SAAS,CAAC0C,cAAc,CAACE,KAAK;SAEjC,EACD;UACEN,KAAK,EAAEtC,SAAS,CAAC0C,cAAc,CAACG,KAAK;UACrCZ,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CACnCvB,SAAS,CAAC0C,cAAc,CAACG,KAAK;SAEjC,EACD;UACEP,KAAK,EAAEtC,SAAS,CAAC0C,cAAc,CAACI,KAAK;UACrCb,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CACnCvB,SAAS,CAAC0C,cAAc,CAACI,KAAK;SAEjC;OAEJ;MACDC,WAAW,EAAE;QACXC,IAAI,EAAE,kBAAkBhD,SAAS,CAACuC,gBAAgB,CAACE,gBAAgB;;KAEtE,EACD;MACEX,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,kBAAkB,CAAC;QACzD0B,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAACO,OAAO,CACzC,iDAAiD,CAClD;QACDc,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,CAAC;UACRL,KAAK,EAAE;SACR,EACD;UACEK,KAAK,EAAE,CAAC;UACRL,KAAK,EAAE;SACR,EACD;UACEK,KAAK,EAAE,CAAC;UACRL,KAAK,EAAE;SACR,EACD;UACEK,KAAK,EAAE,EAAE;UACTL,KAAK,EAAE;SACR,CACF;QACDF,IAAI,EAAE,QAAQ;QACdK,QAAQ,EAAE;OACX;MACDc,YAAY,EAAE,CAAC;MACfH,WAAW,EAAE;QACXC,IAAI,EAAE,kBAAkBhD,SAAS,CAACuC,gBAAgB,CAACE,gBAAgB;;KAEtE,EACD;MACEX,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,qBAAqB,CAAC;QAC5DW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CACzC,2BAA2B,CAC5B;QACD0B,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAACO,OAAO,CACzC,uCAAuC,CACxC;QACDQ,IAAI,EAAE,QAAQ;QACdK,QAAQ,EAAE,IAAI;QACde,QAAQ,EAAE;OACX;MACDD,YAAY,EAAE,CAAC;MACfH,WAAW,EAAE;QACXC,IAAI,EAAE,2BAA2BhD,SAAS,CAAC0C,cAAc,CAACC,KAAK;;KAElE,EACD;MACEb,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,qBAAqB,CAAC;QAC5DW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CACzC,2BAA2B,CAC5B;QACD0B,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAACO,OAAO,CACzC,uCAAuC,CACxC;QACDQ,IAAI,EAAE,QAAQ;QACdK,QAAQ,EAAE,IAAI;QACde,QAAQ,EAAE;OACX;MACDD,YAAY,EAAE,CAAC;MACfH,WAAW,EAAE;QACXC,IAAI,EAAE,2BAA2BhD,SAAS,CAAC0C,cAAc,CAACE,KAAK;;KAElE,EACD;MACEd,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,qBAAqB,CAAC;QAC5DW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CACzC,2BAA2B,CAC5B;QACD0B,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAACO,OAAO,CACzC,uCAAuC,CACxC;QACDQ,IAAI,EAAE,QAAQ;QACdK,QAAQ,EAAE,IAAI;QACde,QAAQ,EAAE;OACX;MACDD,YAAY,EAAE,CAAC;MACfH,WAAW,EAAE;QACXC,IAAI,EAAE,2BAA2BhD,SAAS,CAAC0C,cAAc,CAACG,KAAK;;KAElE,EACD;MACEf,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACO,OAAO,CAAC,qBAAqB,CAAC;QAC5DW,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CACzC,2BAA2B,CAC5B;QACD0B,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAACO,OAAO,CACzC,uCAAuC,CACxC;QACDQ,IAAI,EAAE,QAAQ;QACdK,QAAQ,EAAE;OACX;MACDc,YAAY,EAAE,CAAC;MACfH,WAAW,EAAE;QACXC,IAAI,EAAE,2BAA2BhD,SAAS,CAAC0C,cAAc,CAACI,KAAK;;KAElE,EACD;MACEhB,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,CACF;IAEM,eAAU,GAAmB,CAClC;MACEA,IAAI,EAAE,YAAY;MAClBqB,OAAO,EAAE,CACP;QACEnB,KAAK,EAAE,MAAM;QACboB,OAAO,EAAGC,GAAQ,IAAI;UACpB,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,GAAG,CAAC;QAC1B,CAAC;QACDE,IAAI,EAAE;OACP,EACD;QACEvB,KAAK,EAAE,QAAQ;QACfoB,OAAO,EAAGC,GAAQ,IAAI;UACpB,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAED,GAAG,CAAC;QAC5B,CAAC;QACDE,IAAI,EAAE;OACP;KAEJ,CACF;IAED;IAEO,kBAAa,GAAG,IAAIvD,SAAS,CAAC,EAAE,CAAC;IACjC,mBAAc,GAAG;MACtBwD,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE;KACb;IACM,oBAAe,GAAG,CACvB;MACE5B,GAAG,EAAE,WAAW;MAChBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLD,IAAI,EAAE;;KAET,EACD;MACED,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLE,WAAW,EAAE,IAAI,CAAClB,iBAAiB,CAACO,OAAO,CAAC,uCAAuC,CAAC;QACpFQ,IAAI,EAAE,QAAQ;QACd4B,GAAG,EAAE;;KAER,CACF;IAEM,mBAAc,GAAkB,IAAI;IAyBzCzC,aAAa,CAAC0C,QAAQ,CAAC,gBAAgB,CAAC;EAE1C;EAEA,IAAIC,iBAAiB,CAACC,IAAI;IACxB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;IACA,IAAII,UAAU,GAAG,IAAI,CAACL,iBAAiB;IACvC,IAAIK,UAAU,EAAE;MACdH,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAAC;QAAE,GAAGC,UAAU;QAAE,GAAGJ;MAAI,CAAE,CAAC;;IAGvDK,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEL,QAAQ,CAAC;EACrD;EAEA,IAAIF,iBAAiB;IACnB,IAAIC,IAAI,GAAGK,YAAY,CAACE,OAAO,CAAC,mBAAmB,CAAC;IACpD,IAAIP,IAAI,EAAE;MACR,OAAOE,IAAI,CAACM,KAAK,CAACR,IAAI,CAAC;;IAEzB,OAAO,IAAI;EACb;EAEAS,iBAAiB;IACf,IAAI,CAACzD,oBAAoB,CAAC0D,kBAAkB,EAAE,CAACC,SAAS,CACrDX,IAAI,IAAI;MACP,IAAI,CAACY,cAAc,GAAGZ,IAAI;MAE1B;MACA,IAAID,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC9C,IAAI,CAACA,iBAAiB,EAAEc,cAAc,EAAE;QACtC,IAAIC,SAAS,GAAG;UACdD,cAAc,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC,CAACG;SACxC;QACD,IAAI,CAAChB,iBAAiB,GAAGe,SAAS;;MAEpC;MAAA,KACK,IACH,CAAC,IAAI,CAACF,cAAc,CAACI,IAAI,CAAEC,MAAM,IAAI;QACnC,OAAOA,MAAM,CAACF,EAAE,IAAIhB,iBAAiB,CAACc,cAAc;MACtD,CAAC,CAAC,EACF;QACA,IAAIC,SAAS,GAAG;UACdD,cAAc,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC,CAACG;SACxC;QACD,IAAI,CAAChB,iBAAiB,GAAGe,SAAS;;MAGpC,IAAI,CAACD,cAAc,GAAG,IAAI,CAACd,iBAAiB,CAACc,cAAc;MAE3D,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACL,cAAc;MAEnC,IAAI,CAACM,cAAc,GAAG;QACpBxB,SAAS,EAAE,IAAI,CAACuB,QAAQ;QACxBtB,UAAU,EAAE,IAAI,CAACgB,cAAc,CAACI,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACF,EAAE,IAAI,IAAI,CAACG,QAAQ,CAAC,EAAEE,+BAA+B,IAAI;OAClH;MAED,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EACAC,KAAK,IAAI;MACRrF,IAAI,CAACsF,IAAI,CAAC;QACRhE,KAAK,EAAE,OAAO;QACdiE,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnB/B,IAAI,EAAE,OAAO;QACbgC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACO,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEA4D,kBAAkB;IAChB,IAAI,CAAC,IAAI,CAACH,QAAQ,EAAE;IACpB,IAAI,CAACnE,eAAe,CAAC4E,IAAI,EAAE;IAC3B,IAAI,CAAC1E,cAAc,CAAC2E,iBAAiB,CAAC,IAAI,CAACV,QAAQ,CAAC,CAACP,SAAS,CAC3DX,IAAI,IAAI;MACP,IAAI,CAAC6B,aAAa,GAAG7B,IAAI;MAEzB;MACA,IAAID,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC9C,IAAI,CAACA,iBAAiB,EAAE+B,aAAa,EAAE;QACrC,IAAIhB,SAAS,GAAG;UACdgB,aAAa,EAAEC;SAChB;QACD,IAAI,CAAChC,iBAAiB,GAAGe,SAAS;OACnC,MAAM,IACLf,iBAAiB,EAAE+B,aAAa,EAAEnC,SAAS,IAAI,IAAI,CAACuB,QAAQ,EAC5D;QACA,IAAI,CAACnB,iBAAiB,GAAG;UACvB+B,aAAa,EAAEC;SAChB;;MAGH;MAAA,KACK,IACH,CAAC,IAAI,CAACF,aAAa,CAACb,IAAI,CAAEgB,KAAK,IAAI;QACjC,OAAOA,KAAK,CAACjB,EAAE,IAAIhB,iBAAiB,CAAC+B,aAAa,CAACf,EAAE;MACvD,CAAC,CAAC,EACF;QACA,IAAI,CAAChB,iBAAiB,GAAG;UACvB+B,aAAa,EAAEC;SAChB;;MAGH,IAAI,CAACD,aAAa,GAAG,IAAI,CAAC/B,iBAAiB,CAAC+B,aAAa;MAEzDG,OAAO,CAACC,GAAG,CAAC,IAAI,CAACJ,aAAa,CAAC;MAE/B,IAAI,CAACK,OAAO,GAAG,IAAI,CAACL,aAAa,GAAG,IAAI,CAACA,aAAa,CAACf,EAAE,GAAGgB,SAAS;MACrE,IAAI,CAACK,qBAAqB,CAAC,IAAI,CAACP,aAAa,CAAC;MAC9C,IAAI,CAAC,IAAI,CAACQ,WAAW,EAAE;QACrB,IAAI,CAACA,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,UAAU,EAAE;QACjB,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC;QACnC,IAAI,CAACC,WAAW,EAAE;OACnB,MAAM;QACL;QACA,IAAI,CAACC,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,EACAzB,KAAK,IAAI;MACRrF,IAAI,CAACsF,IAAI,CAAC;QACRhE,KAAK,EAAE,OAAO;QACdiE,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnB/B,IAAI,EAAE,OAAO;QACbgC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACO,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEA2E,qBAAqB,CAACY,MAAM;IAC1B,IAAIzE,OAAO,GAAG,EAAE;IAChByE,MAAM,CAACC,OAAO,CAAEjB,KAAK,IAAI;MACvBzD,OAAO,CAAC2E,IAAI,CAAC;QACX1E,KAAK,EAAEwD,KAAK,CAACjB,EAAE;QACf5C,KAAK,EAAE6D,KAAK,CAACmB;OACd,CAAC;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAClF,KAAK,CAACK,OAAO,GAAGA,OAAO;IACtC,IAAI,CAAC8E,cAAc,CAACb,IAAI,CAAC,IAAI,CAACY,MAAM,CAAC;EACvC;EAEAE,sBAAsB,CAACC,MAAM;IAC3B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACxC,QAAQ,GAAGqC,MAAM;MACtB,IAAI,CAACxD,iBAAiB,GAAG;QACvBc,cAAc,EAAE,IAAI,CAACK;OACtB;MAED,IAAI,CAACC,cAAc,GAAG;QACpBxB,SAAS,EAAE,IAAI,CAACuB,QAAQ;QACxBtB,UAAU,EAAE,IAAI,CAACgB,cAAc,CAACI,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACF,EAAE,IAAI,IAAI,CAACG,QAAQ,CAAC,EAAEE,+BAA+B,IAAI;OAClH;MAED,IAAI,CAACC,kBAAkB,EAAE;MACzBoC,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAE,qBAAqB,CAACJ,MAAM;IAC1B;IACA,IAAI,CAACpB,OAAO,GAAGoB,MAAM;IACrBtB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACC,OAAO,CAAC;IAEpC,IAAI,CAACL,aAAa,GAAG,IAAI,CAACD,aAAa,CAACb,IAAI,CAAEgB,KAAK,IAAI;MACrD,OAAOA,KAAK,CAACjB,EAAE,IAAI,IAAI,CAACoB,OAAO;IACjC,CAAC,CAAC;IAEF,IAAI,CAACpC,iBAAiB,GAAG;MACvB+B,aAAa,EAAE,IAAI,CAACA;KACrB;IACD,IAAI,CAACa,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAa,QAAQ;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACnH,MAAM,CAACc,OAAO,CAAC,iBAAiB,CAAC;MACnDsG,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACV/F,IAAI,EAAE,EAAE;QACRgG,KAAK,EAAE,CACL;UACEd,IAAI,EAAE,IAAI,CAACxG,MAAM,CAACc,OAAO,CAAC,aAAa,CAAC;UACxCyG,MAAM,EAAE;SACT,EACD;UACEf,IAAI,EAAE,IAAI,CAACxG,MAAM,CAACc,OAAO,CAAC,iBAAiB,CAAC;UAC5CyG,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACzD,iBAAiB,EAAE;EAC1B;EAEA6B,UAAU;IACR,IAAI,CAACG,SAAS,GAAG;MACf0B,GAAG,EAAE,IAAI,CAAC1H,eAAe,CAAC2H,iBAAiB,CAACC,cAAc;MAC1DvB,IAAI,EAAE,CAACwB,oBAAyB,EAAEC,QAAQ,KAAI;QAC5CD,oBAAoB,CAACE,QAAQ,GAAG,IAAI,CAACrC,OAAO;QAC5CmC,oBAAoB,CAAC3E,SAAS,GAAG,IAAI,CAACuB,QAAQ;QAE9C,IAAI,CAACxE,KAAK,CACP+H,IAAI,CACH,GAAG1I,WAAW,CAAC8B,MAAM,2BAA2B,EAChDyG,oBAAoB,CACrB,CACA3D,SAAS,CAAE+D,IAAS,IAAI;UACvB,IAAI,CAACjI,eAAe,CAACkI,iBAAiB,CAAC,IAAI,CAACC,OAAO,CAAC;UACpDL,QAAQ,CAAC;YACP;YACAM,YAAY,EAAEH,IAAI,CAACG,YAAY;YAC/BC,eAAe,EAAEJ,IAAI,CAACI,eAAe;YACrC9E,IAAI,EAAE0E,IAAI,CAAC1E;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACD+E,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE;QACRC,OAAO,EAAE;OACV;MACDC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;MACnBC,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;QACR,GAAG,IAAI,CAAC9I,eAAe,CAAC2H,iBAAiB,CAACoB,IAAI;QAC9C,GAAG;UACDC,MAAM,EAAE,EAAE;UACVC,iBAAiB,EAAE,IAAI,CAAC/I,MAAM,CAACc,OAAO,CAAC,QAAQ,CAAC;UAChDkI,IAAI,EAAE;YACJC,KAAK,EAAE;;;OAGZ;MACDC,UAAU,EAAE,UAASrG,GAAG,EAAEQ,IAAI,EAAE8F,SAAS,EAAEC,KAAK;QAC9CC,CAAC,CAACxG,GAAG,CAAC,CAACyG,QAAQ,CAAC,8BAA8B,CAAC;MACjD,CAAC;MACDC,UAAU,EAAE;QACV;QACA;MAAA,CACD;MACDC,OAAO,EAAE,CACP;QACE5I,KAAK,EAAE,MAAM;QACbyC,IAAI,EAAE,MAAM;QACZoG,SAAS,EAAE;OACZ,EAED;QACEpG,IAAI,EAAE,IAAI;QACVoG,SAAS,EAAE,oBAAoB;QAC/BC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;UACbC,GAAG,EAAE,IAAI,CAACC,YAAY;UACtBC,OAAO,EAAE;YACPC,aAAa,EAAE,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI;;;OAGjD,EACD;QACE5G,IAAI,EAAE,YAAY;QAClBoG,SAAS,EAAE,QAAQ;QACnBnI,IAAI,EAAE,YAAY;QAClB4I,OAAO,EAAE;OACV,EACD;QACE7G,IAAI,EAAE,IAAI;QACVoG,SAAS,EAAE,mCAAmC;QAC9CU,MAAM,EAAE,CAAC9G,IAAI,EAAE/B,IAAI,EAAEuB,GAAG,KAAI;UAC1B;UACA,IAAIuH,cAAc,GAAGvH,GAAG,CAACwH,MAAM,CAACC,MAAM,CAAEC,KAAK,IAAI;YAC/C,OAAOA,KAAK,CAACjJ,IAAI,IAAI/B,SAAS,CAACuC,gBAAgB,CAAC0I,QAAQ;UAC1D,CAAC,CAAC,CAACC,MAAM;UAET;UACA,IAAIC,YAAY,GAAG7H,GAAG,CAACwH,MAAM,CAACC,MAAM,CAAEC,KAAK,IAAI;YAC7C,OAAOA,KAAK,CAACjJ,IAAI,IAAI/B,SAAS,CAACuC,gBAAgB,CAACC,MAAM;UACxD,CAAC,CAAC,CAAC0I,MAAM;UAET;UACA,IAAIE,YAAY,GAAG9H,GAAG,CAACwH,MAAM,CAACC,MAAM,CAAEC,KAAK,IAAI;YAC7C,OAAOA,KAAK,CAACjJ,IAAI,IAAI/B,SAAS,CAACuC,gBAAgB,CAACuE,MAAM;UACxD,CAAC,CAAC,CAACoE,MAAM;UAET,IAAIG,YAAY,GAAGR,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;UAC9C,IAAIS,UAAU,GAAGH,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;UAC1C,IAAII,UAAU,GAAGH,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;UAC1C,IAAII,IAAI,GAAG,EAAE;UACb,IAAIC,YAAY,GAAG,mBAAmBJ,YAAY;qDACT/H,GAAG,CAACuB,EAAE;;;;mBAIxC;UAEP,IAAI6G,UAAU,GAAG,mBAAmBJ,UAAU;qDACLhI,GAAG,CAACuB,EAAE;;;;mBAIxC;UAEP,IAAI8G,UAAU,GAAG,mBAAmBJ,UAAU;qDACLjI,GAAG,CAACuB,EAAE;;;;mBAIxC;UAEP;UACAvB,GAAG,CAACwH,MAAM,CAAC/D,OAAO,CAAEiE,KAAK,IAAI;YAC3B,IAAIY,UAAU,GAAGZ,KAAK,CAAC/D,IAAI;YAE3B2E,UAAU,GAAG,IAAI,CAAC5K,iBAAiB,CAACO,OAAO,CAACqK,UAAU,CAAC;YACvD,IAAIC,GAAG,GAAG,EAAE;YACZ,QAAQb,KAAK,CAACjJ,IAAI;cAChB,KAAK/B,SAAS,CAACuC,gBAAgB,CAAC0I,QAAQ;gBACtCY,GAAG,GAAGJ,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEF,UAAU,CAAC;gBACtDC,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAEd,KAAK,CAACnG,EAAE,CAAC;gBACzC2G,IAAI,IAAIK,GAAG;gBACX;cACF,KAAK7L,SAAS,CAACuC,gBAAgB,CAACC,MAAM;gBACpCqJ,GAAG,GAAGH,UAAU,CAACI,OAAO,CAAC,cAAc,EAAEF,UAAU,CAAC;gBACpDC,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAEd,KAAK,CAACnG,EAAE,CAAC;gBACzC2G,IAAI,IAAIK,GAAG;gBACX;cACF,KAAK7L,SAAS,CAACuC,gBAAgB,CAACuE,MAAM;gBACpC+E,GAAG,GAAGF,UAAU,CAACG,OAAO,CAAC,cAAc,EAAEF,UAAU,CAAC;gBACpDC,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAEd,KAAK,CAACnG,EAAE,CAAC;gBACzC2G,IAAI,IAAIK,GAAG;gBACX;cACF;gBACE;YAAM;UAEZ,CAAC,CAAC;UAEF,IAAIE,IAAI,GAAG;cACTP,IAAI;mBACC;UACP;UACA,OAAOO,IAAI;QACb;OACD,EACD;QACEjI,IAAI,EAAE,UAAU;QAChB6G,OAAO,EAAE;OACV,CACF;MACDqB,UAAU,EAAE,CACV,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EACjB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CACrB;MACDC,aAAa,EAAE,CAAC,CAAC;MACjBC,MAAM,EAAE,KAAK;MACb9I,OAAO,EAAE;QACP6E,GAAG,EAAE,IAAI,CAAC1H,eAAe,CAAC2H,iBAAiB,CAAC9E,OAAO,CAAC+E,cAAc;QAClE/E,OAAO,EAAE,CACP;UACEkC,IAAI,EACF,oCAAoC,GACpC,IAAI,CAACtE,iBAAiB,CAACO,OAAO,CAAC,gBAAgB,CAAC;UAClDM,MAAM,EAAE,MAAM,IAAI,CAAC0B,MAAM,CAAC,QAAQ;SACnC,EACD;UACE2G,SAAS,EAAE,yBAAyB;UACpC5E,IAAI,EACF,wCAAwC,GACxC,IAAI,CAACtE,iBAAiB,CAACO,OAAO,CAAC,yBAAyB,CAAC;UAC3D4K,IAAI,EAAE,UAASC,GAAG,EAAEC,IAAI,EAAEC,MAAM;YAC9BxC,CAAC,CAACuC,IAAI,CAAC,CAACE,WAAW,EAAE,CAACxC,QAAQ,CAAC,yBAAyB,CAAC;UAC3D,CAAC;UACDlI,MAAM,EAAE,MAAM,IAAI,CAAC2K,kBAAkB;SACtC,EACD;UACEtC,SAAS,EAAE,yBAAyB;UACpC5E,IAAI,EACF,wCAAwC,GACxC,IAAI,CAACtE,iBAAiB,CAACO,OAAO,CAAC,iBAAiB,CAAC;UACnD4K,IAAI,EAAE,UAASC,GAAG,EAAEC,IAAI,EAAEC,MAAM;YAC9BxC,CAAC,CAACuC,IAAI,CAAC,CAACE,WAAW,EAAE,CAACxC,QAAQ,CAAC,yBAAyB,CAAC;UAC3D,CAAC;UACDlI,MAAM,EAAE,MAAM,IAAI,CAAC4K,sBAAsB;SAC1C;;KAGN;EACH;EAEAhC,cAAc,CAACiC,KAAU;IACvB;EAAA;EAGFnJ,MAAM,CAAC1B,MAAM,EAAEyB,GAAI;IACjB,QAAQzB,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAACqF,MAAM,CAAC,CAAC,CAAC,CAAClF,KAAK,CAAC2K,QAAQ,GAAG,KAAK;QACrC,IAAI,CAACxF,cAAc,CAACb,IAAI,CAAC,IAAI,CAACY,MAAM,CAAC;QACrC;MACF,KAAK,MAAM;QACT;QACA,IAAI0F,YAAY,GAAG5I,IAAI,CAACM,KAAK,CAACN,IAAI,CAACC,SAAS,CAAC,IAAI,CAACiD,MAAM,CAAC,CAAC;QAC1D,MAAM2F,kBAAkB,GAAG,CAAC,CAAC,CAAC;QAC9BD,YAAY,GAAGA,YAAY,CAAC7B,MAAM,CAAC,CAAC+B,CAAC,EAAEC,KAAK,KAAK,CAACF,kBAAkB,CAACG,QAAQ,CAACD,KAAK,CAAC,CAAC;QACrF;QACAH,YAAY,CAAC,CAAC,CAAC,CAAC5K,KAAK,CAAC2K,QAAQ,GAAG,IAAI;QACrC,IAAI,CAACxF,cAAc,CAACb,IAAI,CAACsG,YAAY,CAAC;QACtC;MACF,KAAK,QAAQ;QACX;MACF;QACE;IAAM;IAEV,IAAI,CAACK,MAAM,CAACpL,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACoL,MAAM,CAAC3J,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACrC,mBAAmB,CAACiM,kBAAkB,CAAC,IAAI,CAAC9L,UAAU,CAAC,CAAC+L,UAAU,EAAE;EAC3E;EAEAX,kBAAkB;IAChB,IAAI,CAAC5L,aAAa,CAACwM,IAAI,CAAC,IAAI,CAACC,yBAAyB,EAAE;MACtDC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,MAAK;QAClB,IAAI,CAACC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACxI,cAAc,GAAG;UACpBxB,SAAS,EAAE,IAAI,CAACuB,QAAQ;UACxBtB,UAAU,EAAE,IAAI,CAACgB,cAAc,CAACI,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACF,EAAE,IAAI,IAAI,CAACG,QAAQ,CAAC,EAAEE,+BAA+B,IAAI;SAClH;QACD,OAAO,IAAI;MACb;KACD,CAAC;EACJ;EAEAuH,sBAAsB;IACpB1G,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC0H,kBAAkB,CAAC;IACpC,IAAI,CAAC9M,aAAa,CAACwM,IAAI,CAAC,IAAI,CAACM,kBAAkB,EAAE;MAC/C;MACAH,IAAI,EAAE,IAAI;MACVI,UAAU,EAAE;KACb,CAAC;EACJ;EAEAC,iBAAiB,CAACC,KAAK;IAErB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACI,KAAK,CAACnK,UAAU,IAAImK,KAAK,CAACnK,UAAU,IAAI,CAAC,EAAE;MAC9C,IAAI,CAAC+J,cAAc,GAAG,sCAAsC;MAC5D;;IAGF,IAAII,KAAK,CAACnK,UAAU,GAAG,CAAC,IAAImK,KAAK,CAACnK,UAAU,GAAG,IAAI,EAAE;MACnD,IAAI,CAAC+J,cAAc,GAAG,gDAAgD;MACtE;;IAGF,IAAI,CAAC1M,cAAc,CAAC+M,eAAe,CAACD,KAAK,CAAC,CAACpJ,SAAS,CAAEsJ,QAAQ,IAAI;MAChEhO,IAAI,CAACsF,IAAI,CAAC;QACRhE,KAAK,EAAE,SAAS;QAChBiE,IAAI,EAAEyI,QAAQ,CAACxI,OAAO;QACtB/B,IAAI,EAAE,SAAS;QACfgC,iBAAiB,EAAE,IAAI,CAACxE,iBAAiB,CAACO,OAAO,CAAC,IAAI;OACvD,CAAC;MACF,IAAI,CAACmD,cAAc,CAACqC,OAAO,CAAEhC,MAAM,IAAI;QACrC,IAAIA,MAAM,CAACF,EAAE,KAAK,IAAI,CAACG,QAAQ,EAAE;UAC/BD,MAAM,CAACG,+BAA+B,GAAG2I,KAAK,CAACnK,UAAU;;MAE7D,CAAC,CAAC;MACF,IAAI,CAAC9C,aAAa,CAACoN,UAAU,EAAE;IACjC,CAAC,EAAG5I,KAAK,IAAI;MACX,IAAI,CAACqI,cAAc,GAAGrI,KAAK,CAACG,OAAO;IACrC,CAAC,CAAC;EAEJ;EAGAiB,WAAW;IACT,IAAI,CAACyH,UAAU,GAAG,IAAI,CAACvN,QAAQ,CAACwN,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGxB,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACyB,MAAM,CAACC,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAG3B,KAAK,CAACyB,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;QAC9D,IAAIC,QAAQ,GAAG7B,KAAK,CAACyB,MAAM,CAACG,YAAY,CAAC,UAAU,CAAC;QACpD;QACA,IAAI,CAAChO,OAAO,CAACkO,QAAQ,CAAC,CAACH,aAAa,EAAE,QAAQ,EAAEE,QAAQ,CAAC,EAAE;UACzDE,UAAU,EAAE,IAAI,CAACpO;SAClB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAqO,SAAS,CAACrH,MAAM;IACd;IACA,IAAI,CAACZ,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5D,IAAI,CAAC7F,eAAe,CAAC4E,IAAI,EAAE;MAC3BiB,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA8H,WAAW;IACT,IAAI,CAACtI,SAAS,CAACuI,WAAW,EAAE;IAC5B,IAAI,CAACX,UAAU,EAAE;EACnB;EAEAY,mBAAmB,CAAC/K,IAAI;IACtBiC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAElC,IAAI,CAAC;IACzB,IAAI,CAACxD,OAAO,CAACkO,QAAQ,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE1K,IAAI,CAACe,EAAE,EAAE,eAAe,CAAC,CAAC;EACxE;EAAC;qBA1zBU1E,eAAe;EAAA;EAAA;UAAfA,eAAe;IAAA2O;IAAAC;MAAA;;uBAEfnP,kBAAkB;;;;;;;;;;;;;;;;;QCzC/BM,8BAA+C;QAG3CA,wCAAyE;QAIzEA,8BAAqB;QAKNA;UAAA;QAAA,EAA4B;UAAA,OAAW8O,kCAA8B;QAAA,EAAzC;;QACrC9O,4EACY;QACdA,iBAAY;QAEdA,kFAsBe;QACjBA,iBAAM;QACNA,gCAAmG;QACjGA,4BAAgC;QAClCA,iBAAQ;QAGZA,yCAAqH;QAC/FA;UAAA,OAAa8O,qBAAiB;QAAA,EAAC;QAGnD9O,iBAAqB;QAIvBA,sHAkCc;QAEdA,oHAEc;QAEdA,oHAgBc;;;QA3GUA,eAA+B;QAA/BA,iDAA+B;QAQKA,eAA+C;QAA/CA,+EAA+C;QAAxFA,kCAAoB;QAECA,eAAiB;QAAjBA,4CAAiB;QAIpCA,eAA4B;QAA5BA,kDAA4B;QAwB5BA,eAAuB;QAAvBA,yCAAuB;QAKuBA,eAAmB;QAAnBA,qCAAmB;QAChCA,eAAmB;QAAnBA,qCAAmB", "names": ["DataTableDirective", "environment", "Subject", "<PERSON><PERSON>", "AppConfig", "FormGroup", "i0", "LeagueComponent", "constructor", "route", "_router", "_commonsService", "_http", "_trans", "renderer", "_teamService", "_modalService", "_loadingService", "_registrationService", "_seasonService", "_translateService", "_coreSidebarService", "_titleService", "editor_id", "table_name", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "key", "type", "props", "label", "placeholder", "closeOnSelect", "required", "options", "value", "TOURNAMENT_TYPES", "league", "groups_knockouts", "KNOCKOUT_TYPES", "type1", "type2", "type3", "type4", "expressions", "hide", "description", "defaultValue", "readonly", "buttons", "onClick", "row", "editor", "icon", "season_id", "limit_time", "min", "setTitle", "tableManageLeague", "data", "str_data", "JSON", "stringify", "exist_data", "localStorage", "setItem", "getItem", "parse", "_getCurrentSeason", "getAllSeasonActive", "subscribe", "currentSeasons", "selectedS<PERSON>on", "tableData", "id", "find", "season", "seasonId", "limitTimeModel", "default_limit_update_score_time", "_getGroupsBySeason", "error", "fire", "text", "message", "confirmButtonText", "show", "getGroupsBySeason", "currentGroups", "selectedGroup", "undefined", "group", "console", "log", "groupId", "converGroupsToOptions", "isInitTable", "buildTable", "dtTrigger", "next", "dtOptions", "addListener", "dtElement", "dtInstance", "then", "ajax", "reload", "groups", "for<PERSON>ach", "push", "name", "fields", "fields_subject", "onSelectedSeasonChange", "$event", "Promise", "resolve", "reject", "onSelectedGroupChange", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "isLink", "dom", "dataTableDefaults", "dom_table_card", "dataTablesParameters", "callback", "group_id", "post", "resp", "customSearchInput", "tableID", "recordsTotal", "recordsFiltered", "stateSave", "rowGroup", "dataSrc", "order", "select", "rowId", "responsive", "scrollX", "language", "lang", "search", "searchPlaceholder", "attr", "class", "createdRow", "dataIndex", "cells", "$", "addClass", "columnDefs", "columns", "className", "defaultContent", "ngTemplateRef", "ref", "rowActionBtn", "context", "captureEvents", "onCaptureEvent", "bind", "visible", "render", "knockout_count", "stages", "filter", "stage", "knockout", "length", "league_count", "groups_count", "col_knockout", "col_league", "col_groups", "btns", "btn_knockout", "btn_league", "btn_groups", "stage_name", "btn", "replace", "html", "lengthMenu", "displayLength", "paging", "init", "api", "node", "config", "removeClass", "openModalLimitTime", "openModalManageReferee", "event", "disabled", "clone_fields", "hiddenFieldIndices", "_", "index", "includes", "params", "getSidebarRegistry", "toggle<PERSON><PERSON>", "open", "modalLimitTimeUpdateScore", "centered", "size", "<PERSON><PERSON><PERSON><PERSON>", "limitTimeError", "modalManageReferee", "scrollable", "onSubmitLimitTime", "model", "updateLimitTime", "response", "dismissAll", "unlistener", "listen", "target", "hasAttribute", "tournament_id", "getAttribute", "stage_id", "navigate", "relativeTo", "onSuccess", "ngOnDestroy", "unsubscribe", "onClickAutoSchedule", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league\\league.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league\\league.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  AfterViewInit,\r\n  ViewEncapsulation,\r\n  TemplateRef\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { Subject } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { options } from '@fullcalendar/core/preact';\r\nimport { FormGroup } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-league',\r\n  templateUrl: './league.component.html',\r\n  styleUrls: ['./league.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class LeagueComponent implements OnInit {\r\n  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  dtOptions: any = {};\r\n  model: any = {};\r\n  tableID: string = 'league-table';\r\n  isInitTable: boolean = false;\r\n  private unlistener: () => void;\r\n\r\n  public seasonId: any;\r\n  public clubId: any;\r\n  public groupId: any;\r\n  public modalRef: any;\r\n  public contentHeader: object;\r\n  public currentSeasons;\r\n  public currentGroups;\r\n  public selectedSeason: any = {};\r\n  public selectedGroup: any = {};\r\n\r\n  public table_name = 'tournament-table';\r\n\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Create New Tournament'),\r\n      edit: 'Edit tournament',\r\n      remove: 'Delete tournament'\r\n    },\r\n    url: `${environment.apiUrl}/tournaments/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n\r\n  public fields_subject = new Subject<any>();\r\n  public fields: any[] = [\r\n    {\r\n      key: 'group_id',\r\n      type: 'ng-select',\r\n      props: {\r\n        label: this._translateService.instant('Group'),\r\n        placeholder: this._translateService.instant('Select group'),\r\n        closeOnSelect: true,\r\n        required: true,\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'name',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Name'),\r\n        placeholder: this._translateService.instant('Enter name of tournament'),\r\n        required: true\r\n      }\r\n    },\r\n    {\r\n      key: 'type',\r\n      type: 'radio',\r\n      props: {\r\n        label: this._translateService.instant('Type'),\r\n        placeholder: this._translateService.instant('Select tournament type'),\r\n        required: true,\r\n        options: [\r\n          {\r\n            value: AppConfig.TOURNAMENT_TYPES.league,\r\n            label: this._translateService.instant(\r\n              AppConfig.TOURNAMENT_TYPES.league\r\n            )\r\n          },\r\n          {\r\n            value: AppConfig.TOURNAMENT_TYPES.groups_knockouts,\r\n            label: this._translateService.instant(\r\n              AppConfig.TOURNAMENT_TYPES.groups_knockouts\r\n            )\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    {\r\n      key: 'type_knockout',\r\n      type: 'radio',\r\n      props: {\r\n        label: this._translateService.instant('Type of group + knockout'),\r\n        placeholder: this._translateService.instant('Select type of group + knockout'),\r\n        required: true,\r\n        options: [\r\n          {\r\n            value: AppConfig.KNOCKOUT_TYPES.type1,\r\n            label: this._translateService.instant(\r\n              AppConfig.KNOCKOUT_TYPES.type1\r\n            )\r\n          },\r\n          {\r\n            value: AppConfig.KNOCKOUT_TYPES.type2,\r\n            label: this._translateService.instant(\r\n              AppConfig.KNOCKOUT_TYPES.type2\r\n            )\r\n          },\r\n          {\r\n            value: AppConfig.KNOCKOUT_TYPES.type3,\r\n            label: this._translateService.instant(\r\n              AppConfig.KNOCKOUT_TYPES.type3\r\n            )\r\n          },\r\n          {\r\n            value: AppConfig.KNOCKOUT_TYPES.type4,\r\n            label: this._translateService.instant(\r\n              AppConfig.KNOCKOUT_TYPES.type4\r\n            )\r\n          }\r\n        ]\r\n      },\r\n      expressions: {\r\n        hide: `model.type != \"${AppConfig.TOURNAMENT_TYPES.groups_knockouts}\"`\r\n      }\r\n    },\r\n    {\r\n      key: 'number_groups',\r\n      type: 'ng-select',\r\n      props: {\r\n        label: this._translateService.instant('Number of groups'),\r\n        description: this._translateService.instant(\r\n          'Please enter the group number to the power of 2'\r\n        ),\r\n        options: [\r\n          {\r\n            value: 2,\r\n            label: '2'\r\n          },\r\n          {\r\n            value: 4,\r\n            label: '4'\r\n          },\r\n          {\r\n            value: 8,\r\n            label: '8'\r\n          },\r\n          {\r\n            value: 16,\r\n            label: '16'\r\n          }\r\n        ],\r\n        type: 'number',\r\n        required: true\r\n      },\r\n      defaultValue: 2,\r\n      expressions: {\r\n        hide: `model.type != \"${AppConfig.TOURNAMENT_TYPES.groups_knockouts}\"`\r\n      }\r\n    },\r\n    {\r\n      key: 'no_knockout_type_1',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Number of knockouts'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter number of knockouts'\r\n        ),\r\n        description: this._translateService.instant(\r\n          'Ex: Enter 4 for Cup,Plate,Bowl,Shield'\r\n        ),\r\n        type: 'number',\r\n        required: true,\r\n        readonly: true\r\n      },\r\n      defaultValue: 4,\r\n      expressions: {\r\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type1}\"`\r\n      }\r\n    },\r\n    {\r\n      key: 'no_knockout_type_2',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Number of knockouts'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter number of knockouts'\r\n        ),\r\n        description: this._translateService.instant(\r\n          'Ex: Enter 4 for Cup,Plate,Bowl,Shield'\r\n        ),\r\n        type: 'number',\r\n        required: true,\r\n        readonly: true\r\n      },\r\n      defaultValue: 2,\r\n      expressions: {\r\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type2}\"`\r\n      }\r\n    },\r\n    {\r\n      key: 'no_knockout_type_3',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Number of knockouts'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter number of knockouts'\r\n        ),\r\n        description: this._translateService.instant(\r\n          'Ex: Enter 4 for Cup,Plate,Bowl,Shield'\r\n        ),\r\n        type: 'number',\r\n        required: true,\r\n        readonly: true\r\n      },\r\n      defaultValue: 1,\r\n      expressions: {\r\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type3}\"`\r\n      }\r\n    },\r\n    {\r\n      key: 'no_knockout_type_4',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Number of knockouts'),\r\n        placeholder: this._translateService.instant(\r\n          'Enter number of knockouts'\r\n        ),\r\n        description: this._translateService.instant(\r\n          'Ex: Enter 4 for Cup,Plate,Bowl,Shield'\r\n        ),\r\n        type: 'number',\r\n        required: true\r\n      },\r\n      defaultValue: 1,\r\n      expressions: {\r\n        hide: `model.type_knockout != \"${AppConfig.KNOCKOUT_TYPES.type4}\"`\r\n      }\r\n    },\r\n    {\r\n      key: 'id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    }\r\n  ];\r\n\r\n  public rowActions: EZBtnActions[] = [\r\n    {\r\n      type: 'collection',\r\n      buttons: [\r\n        {\r\n          label: 'Edit',\r\n          onClick: (row: any) => {\r\n            this.editor('edit', row);\r\n          },\r\n          icon: 'fa-regular fa-pen-to-square'\r\n        },\r\n        {\r\n          label: 'Delete',\r\n          onClick: (row: any) => {\r\n            this.editor('remove', row);\r\n          },\r\n          icon: 'fa-regular fa-trash'\r\n        }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  // setup modal limit time edit score\r\n\r\n  public limitTimeForm = new FormGroup({});\r\n  public limitTimeModel = {\r\n    season_id: null,\r\n    limit_time: null\r\n  };\r\n  public limitTimeFields = [\r\n    { //edit\r\n      key: 'season_id',\r\n      type: 'input',\r\n      props: {\r\n        type: 'hidden'\r\n      }\r\n    },\r\n    {\r\n      key: 'limit_time',\r\n      type: 'input',\r\n      props: {\r\n        placeholder: this._translateService.instant('Enter limit update score time (hours)'),\r\n        type: 'number',\r\n        min: 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  public limitTimeError: null | string = null;\r\n\r\n  @ViewChild('modalLimitTimeUpdateScore') modalLimitTimeUpdateScore!: TemplateRef<any>;\r\n  @ViewChild('modalManageReferee') modalManageReferee!: TemplateRef<any>;\r\n\r\n\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _registrationService: RegistrationService,\r\n    public _seasonService: SeasonService,\r\n    public _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _titleService: Title\r\n  ) {\r\n    _titleService.setTitle('Manage Leagues');\r\n\r\n  }\r\n\r\n  set tableManageLeague(data) {\r\n    let str_data = JSON.stringify(data);\r\n    // merge data if exist\r\n    let exist_data = this.tableManageLeague;\r\n    if (exist_data) {\r\n      str_data = JSON.stringify({ ...exist_data, ...data });\r\n    }\r\n\r\n    localStorage.setItem('tableManageLeague', str_data);\r\n  }\r\n\r\n  get tableManageLeague() {\r\n    let data = localStorage.getItem('tableManageLeague');\r\n    if (data) {\r\n      return JSON.parse(data);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.currentSeasons = data;\r\n\r\n        // if not exist selectedSeason tableManageLeague then set default, else get from tableManageLeague\r\n        let tableManageLeague = this.tableManageLeague;\r\n        if (!tableManageLeague?.selectedSeason) {\r\n          let tableData = {\r\n            selectedSeason: this.currentSeasons[0].id\r\n          };\r\n          this.tableManageLeague = tableData;\r\n        }\r\n        // check if selectedSeason is not in currentSeasons then set selectedSeason = currentSeasons[0]\r\n        else if (\r\n          !this.currentSeasons.find((season) => {\r\n            return season.id == tableManageLeague.selectedSeason;\r\n          })\r\n        ) {\r\n          let tableData = {\r\n            selectedSeason: this.currentSeasons[0].id\r\n          };\r\n          this.tableManageLeague = tableData;\r\n        }\r\n\r\n        this.selectedSeason = this.tableManageLeague.selectedSeason;\r\n\r\n        this.seasonId = this.selectedSeason;\r\n\r\n        this.limitTimeModel = {\r\n          season_id: this.seasonId,\r\n          limit_time: this.currentSeasons.find((season) => season.id == this.seasonId)?.default_limit_update_score_time ?? 0\r\n        };\r\n\r\n        this._getGroupsBySeason();\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  _getGroupsBySeason() {\r\n    if (!this.seasonId) return;\r\n    this._loadingService.show();\r\n    this._seasonService.getGroupsBySeason(this.seasonId).subscribe(\r\n      (data) => {\r\n        this.currentGroups = data;\r\n\r\n        // if not exist selectedGroup tableManageLeague then set default, else get from tableManageLeague\r\n        let tableManageLeague = this.tableManageLeague;\r\n        if (!tableManageLeague?.selectedGroup) {\r\n          let tableData = {\r\n            selectedGroup: undefined\r\n          };\r\n          this.tableManageLeague = tableData;\r\n        } else if (\r\n          tableManageLeague?.selectedGroup?.season_id != this.seasonId\r\n        ) {\r\n          this.tableManageLeague = {\r\n            selectedGroup: undefined\r\n          };\r\n        }\r\n\r\n        // check if selectedGroup is not in currentGroups then set selectedGroup = currentGroups[0]\r\n        else if (\r\n          !this.currentGroups.find((group) => {\r\n            return group.id == tableManageLeague.selectedGroup.id;\r\n          })\r\n        ) {\r\n          this.tableManageLeague = {\r\n            selectedGroup: undefined\r\n          };\r\n        }\r\n\r\n        this.selectedGroup = this.tableManageLeague.selectedGroup;\r\n\r\n        console.log(this.selectedGroup);\r\n\r\n        this.groupId = this.selectedGroup ? this.selectedGroup.id : undefined;\r\n        this.converGroupsToOptions(this.currentGroups);\r\n        if (!this.isInitTable) {\r\n          this.isInitTable = true;\r\n          this.buildTable();\r\n          this.dtTrigger.next(this.dtOptions);\r\n          this.addListener();\r\n        } else {\r\n          // reload datatable\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  converGroupsToOptions(groups) {\r\n    let options = [];\r\n    groups.forEach((group) => {\r\n      options.push({\r\n        value: group.id,\r\n        label: group.name\r\n      });\r\n    });\r\n    // update options of field group_id\r\n    this.fields[0].props.options = options;\r\n    this.fields_subject.next(this.fields);\r\n  }\r\n\r\n  onSelectedSeasonChange($event) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this.tableManageLeague = {\r\n        selectedSeason: this.seasonId\r\n      };\r\n\r\n      this.limitTimeModel = {\r\n        season_id: this.seasonId,\r\n        limit_time: this.currentSeasons.find((season) => season.id == this.seasonId)?.default_limit_update_score_time ?? 0\r\n      };\r\n\r\n      this._getGroupsBySeason();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectedGroupChange($event) {\r\n    // find group by id in currentGroups\r\n    this.groupId = $event;\r\n    console.log('groupId', this.groupId);\r\n\r\n    this.selectedGroup = this.currentGroups.find((group) => {\r\n      return group.id == this.groupId;\r\n    });\r\n\r\n    this.tableManageLeague = {\r\n      selectedGroup: this.selectedGroup\r\n    };\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('All Tournaments'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('All Tournaments'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n  }\r\n\r\n  buildTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom_table_card,\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        dataTablesParameters.group_id = this.groupId;\r\n        dataTablesParameters.season_id = this.seasonId;\r\n\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/tournaments/all-in-group`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this._commonsService.customSearchInput(this.tableID);\r\n            callback({\r\n              // this function callback is used to return data to datatable\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      stateSave: false,\r\n      rowGroup: {\r\n        dataSrc: 'group.name'\r\n      },\r\n      order: [[2, 'asc']],\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      responsive: true,\r\n      scrollX: false,\r\n      language: {\r\n        ...this._commonsService.dataTableDefaults.lang,\r\n        ...{\r\n          search: '',\r\n          searchPlaceholder: this._trans.instant('Search'),\r\n          attr: {\r\n            class: 'w-100'\r\n          }\r\n        }\r\n      },\r\n      createdRow: function(row, data, dataIndex, cells) {\r\n        $(row).addClass('col-12 col-md-6 col-lg-4 p-0');\r\n      },\r\n      columnDefs: [\r\n        // { responsivePriority: 1, targets: -1 },\r\n        // { responsivePriority: 2, targets: 2 },\r\n      ],\r\n      columns: [\r\n        {\r\n          title: 'Name',\r\n          data: 'name',\r\n          className: 'h3 w-bold col-12 d-table-cell text-warp'\r\n        },\r\n\r\n        {\r\n          data: 'id',\r\n          className: 'col-1 d-table-cell',\r\n          defaultContent: '',\r\n          ngTemplateRef: {\r\n            ref: this.rowActionBtn,\r\n            context: {\r\n              captureEvents: this.onCaptureEvent.bind(this)\r\n            }\r\n          }\r\n        },\r\n        {\r\n          data: 'group.name',\r\n          className: 'd-flex',\r\n          type: 'any-number',\r\n          visible: false\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'd-flex text-center border-right-0',\r\n          render: (data, type, row) => {\r\n            // count stages with type = knockout\r\n            let knockout_count = row.stages.filter((stage) => {\r\n              return stage.type == AppConfig.TOURNAMENT_TYPES.knockout;\r\n            }).length;\r\n\r\n            // count stages with type = league\r\n            let league_count = row.stages.filter((stage) => {\r\n              return stage.type == AppConfig.TOURNAMENT_TYPES.league;\r\n            }).length;\r\n\r\n            // count stages with type = groups\r\n            let groups_count = row.stages.filter((stage) => {\r\n              return stage.type == AppConfig.TOURNAMENT_TYPES.groups;\r\n            }).length;\r\n\r\n            let col_knockout = knockout_count > 1 ? 6 : 12;\r\n            let col_league = league_count > 1 ? 6 : 12;\r\n            let col_groups = groups_count > 1 ? 6 : 12;\r\n            let btns = '';\r\n            let btn_knockout = `<div class=\"col-${col_knockout} pt-50 pb-50\" >\r\n              <button type=\"button\" tournament_id=\"${row.id}\" stage_id=\"{stage_id}\" class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-danger btn-block\" > \r\n              <i style=\"margin-right: 0.5rem\" class=\"bi bi-diagram-2 fa-xl\"></i>\r\n              {stage_name}\r\n              </button>\r\n            </div>`;\r\n\r\n            let btn_league = `<div class=\"col-${col_league} pt-50 pb-50\" >\r\n              <button type=\"button\" tournament_id=\"${row.id}\" stage_id=\"{stage_id}\" class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-success btn-block\" >\r\n              <i style=\"margin-right: 0.5rem;\" class=\"bi bi-trophy\"></i>\r\n              {stage_name}\r\n              </button>\r\n            </div>`;\r\n\r\n            let btn_groups = `<div class=\"col-${col_groups} pt-50 pb-50\" >\r\n              <button type=\"button\" tournament_id=\"${row.id}\" stage_id=\"{stage_id}\" class=\"btn w-100 h-100 pt-1 pb-1 pl-50 pr-50 btn-outline-warning btn-block\" >\r\n              <i style=\"margin-right: 0.5rem;\" class=\"bi bi-ui-checks-grid\"></i>\r\n              {stage_name}\r\n              </button>\r\n            </div>`;\r\n\r\n            // for each row.stages, create a button\r\n            row.stages.forEach((stage) => {\r\n              let stage_name = stage.name;\r\n\r\n              stage_name = this._translateService.instant(stage_name);\r\n              let btn = '';\r\n              switch (stage.type) {\r\n                case AppConfig.TOURNAMENT_TYPES.knockout:\r\n                  btn = btn_knockout.replace('{stage_name}', stage_name);\r\n                  btn = btn.replace('{stage_id}', stage.id);\r\n                  btns += btn;\r\n                  break;\r\n                case AppConfig.TOURNAMENT_TYPES.league:\r\n                  btn = btn_league.replace('{stage_name}', stage_name);\r\n                  btn = btn.replace('{stage_id}', stage.id);\r\n                  btns += btn;\r\n                  break;\r\n                case AppConfig.TOURNAMENT_TYPES.groups:\r\n                  btn = btn_groups.replace('{stage_name}', stage_name);\r\n                  btn = btn.replace('{stage_id}', stage.id);\r\n                  btns += btn;\r\n                  break;\r\n                default:\r\n                  break;\r\n              }\r\n            });\r\n\r\n            let html = `<div class=\"btn-stages row flex-grow-1 justify-content-start\" >\r\n            ${btns}\r\n            </div>`;\r\n            // return 2 button\r\n            return html;\r\n          }\r\n        },\r\n        {\r\n          data: 'group_id',\r\n          visible: false\r\n        }\r\n      ],\r\n      lengthMenu: [\r\n        [25, 50, 100, -1],\r\n        [25, 50, 100, 'All']\r\n      ],\r\n      displayLength: -1,\r\n      paging: false,\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom_table_card,\r\n        buttons: [\r\n          {\r\n            text:\r\n              '<i class=\"feather icon-plus\"></i> ' +\r\n              this._translateService.instant('New Tournament'),\r\n            action: () => this.editor('create')\r\n          },\r\n          {\r\n            className: 'btn btn-outline-primary',\r\n            text:\r\n              '<i class=\"feather icon-settings\"></i> ' +\r\n              this._translateService.instant('Limit update score time'),\r\n            init: function(api, node, config) {\r\n              $(node).removeClass().addClass('btn btn-outline-primary');\r\n            },\r\n            action: () => this.openModalLimitTime()\r\n          },\r\n          {\r\n            className: 'btn btn-outline-primary',\r\n            text:\r\n              '<i class=\"feather icon-settings\"></i> ' +\r\n              this._translateService.instant('Manage Referees'),\r\n            init: function(api, node, config) {\r\n              $(node).removeClass().addClass('btn btn-outline-primary');\r\n            },\r\n            action: () => this.openModalManageReferee()\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  editor(action, row?) {\r\n    switch (action) {\r\n      case 'create':\r\n        this.fields[0].props.disabled = false;\r\n        this.fields_subject.next(this.fields);\r\n        break;\r\n      case 'edit':\r\n        // clone fields and remove field 'type'\r\n        let clone_fields = JSON.parse(JSON.stringify(this.fields));\r\n        const hiddenFieldIndices = [2];\r\n        clone_fields = clone_fields.filter((_, index) => !hiddenFieldIndices.includes(index));\r\n        // disable field 'group_id'\r\n        clone_fields[0].props.disabled = true;\r\n        this.fields_subject.next(clone_fields);\r\n        break;\r\n      case 'remove':\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  openModalLimitTime() {\r\n    this._modalService.open(this.modalLimitTimeUpdateScore, {\r\n      centered: true,\r\n      size: 'lg',\r\n      beforeDismiss: () => {\r\n        this.limitTimeError = null;\r\n        this.limitTimeModel = {\r\n          season_id: this.seasonId,\r\n          limit_time: this.currentSeasons.find((season) => season.id == this.seasonId)?.default_limit_update_score_time ?? 0\r\n        };\r\n        return true;\r\n      }\r\n    });\r\n  }\r\n\r\n  openModalManageReferee() {\r\n    console.log(this.modalManageReferee);\r\n    this._modalService.open(this.modalManageReferee, {\r\n      // centered: true,\r\n      size: 'lg',\r\n      scrollable: true\r\n    });\r\n  }\r\n\r\n  onSubmitLimitTime(model) {\r\n\r\n    this.limitTimeError = null;\r\n\r\n    if (!model.limit_time && model.limit_time != 0) {\r\n      this.limitTimeError = 'Please enter limit update score time';\r\n      return;\r\n    }\r\n\r\n    if (model.limit_time < 0 || model.limit_time > 3000) {\r\n      this.limitTimeError = 'Please enter a valid number between 0 and 3000';\r\n      return;\r\n    }\r\n\r\n    this._seasonService.updateLimitTime(model).subscribe((response) => {\r\n      Swal.fire({\r\n        title: 'Success',\r\n        text: response.message,\r\n        icon: 'success',\r\n        confirmButtonText: this._translateService.instant('OK')\r\n      });\r\n      this.currentSeasons.forEach((season) => {\r\n        if (season.id === this.seasonId) {\r\n          season.default_limit_update_score_time = model.limit_time;\r\n        }\r\n      });\r\n      this._modalService.dismissAll();\r\n    }, (error) => {\r\n      this.limitTimeError = error.message;\r\n    });\r\n\r\n  }\r\n\r\n\r\n  addListener() {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament_id')) {\r\n        let tournament_id = event.target.getAttribute('tournament_id');\r\n        let stage_id = event.target.getAttribute('stage_id');\r\n        //  navigate to path ':tournament_id/stages/:stage_id'\r\n        this._router.navigate([tournament_id, 'stages', stage_id], {\r\n          relativeTo: this.route\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSuccess($event) {\r\n    // reload table\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      this._loadingService.show();\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    this.unlistener();\r\n  }\r\n\r\n  onClickAutoSchedule(data) {\r\n    console.log('data', data);\r\n    this._router.navigate(['leagues', 'manage', data.id, 'auto-schedule']);\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <!-- <div class=\"card\">\r\n      <div class=\"card-header\"> -->\r\n    <div class=\"row m-0\">\r\n      <!-- ng select season -->\r\n      <div class=\"col col-md-6 col-lg-3 mb-1 pl-25 pr-50\">\r\n        <!-- <label class=\"form-label\" for=\"season\">{{ 'Season' | translate }}</label> -->\r\n        <ng-select [searchable]=\"false\" [clearable]=\"false\" placeholder=\"{{ 'Select Season' | translate }}\"\r\n                   [(ngModel)]=\"selectedSeason\" (change)=\"onSelectedSeasonChange($event)\">\r\n          <ng-option *ngFor=\"let season of currentSeasons\" [value]=\"season.id\">{{ season.name }}\r\n          </ng-option>\r\n        </ng-select>\r\n      </div>\r\n      <ng-container *ngIf=\"{ isShowFilter:true } as variable\">\r\n        <div class=\"col-auto col-md pl-50 pr-0 d-flex justify-content-end align-items-start\">\r\n          <button type=\"button\" class=\"btn btn-flat pl-25 pr-25\"\r\n                  (click)=\" variable.isShowFilter = !variable.isShowFilter\">\r\n            <i class=\"fa-light fa-filter-list fa-xl mr-25\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"col-12 col-md-auto p-25 mb-1\" *ngIf=\"variable.isShowFilter\">\r\n          <div class=\"row mr-0\">\r\n            <div class=\"col pr-0\">\r\n              <div style=\"min-width: 130px;\">\r\n                <ng-select placeholder=\"{{ 'Filter Groups' | translate }}\" [(ngModel)]=\"groupId\"\r\n                           (change)=\"onSelectedGroupChange($event)\">\r\n                  <ng-option *ngFor=\"let group of currentGroups\" [value]=\"group.id\" name=\"group.id\">\r\n                    {{ group.name }}\r\n                  </ng-option>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n    <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"card_table\" [id]=\"tableID\">\r\n      <tbody class=\"row ml-0\"></tbody>\r\n    </table>\r\n  </div>\r\n</div>\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n  <app-editor-sidebar (onSuccess)=\"onSuccess($event)\" [table]=\"dtElement\" [fields_subject]=\"fields_subject\"\r\n                      [fields]=\"fields\" [params]=\"params\"\r\n                      [new_model]=\"model\">\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n\r\n<ng-template #modalLimitTimeUpdateScore let-modal>\r\n  <div class=\"modal-header\">\r\n    <h5 class=\"modal-title\" id=\"modalLimitTimeUpdateScore\">{{ 'Limit update score time' | translate }}</h5>\r\n    <button type=\"button\" class=\"close\" (click)=\"modal.dismiss('Cross click')\" aria-label=\"Close\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <form [formGroup]=\"limitTimeForm\" (ngSubmit)=\"onSubmitLimitTime(limitTimeModel)\">\r\n    <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n      <formly-form [form]=\"limitTimeForm\" [fields]=\"limitTimeFields\" [model]=\"limitTimeModel\"></formly-form>\r\n      <p *ngIf=\"limitTimeError\" class=\"text-danger\">\r\n        * {{ limitTimeError | translate }}\r\n      </p>\r\n      <hr>\r\n      <div class=\"d-flex flex-column\">\r\n        <!--      <small class=\"mb-2\">-->\r\n        <!--        {{ '* Input \"0\" means there\\'s no limit on how long scores can be updated.' | translate }}-->\r\n        <!--      </small>-->\r\n        <p class=\"m-0 badge-align badge badge-light-default text-dark w-auto text-wrap text-md-start\">\r\n          {{ '* Input \"0\" means there\\'s no limit on how long scores can be updated.' | translate }}\r\n        </p>\r\n\r\n        <p class=\"m-0 badge-align badge badge-light-warning w-auto text-wrap text-md-start\">\r\n          {{ 'Warning: Submit will change all tournament leagues. Be sure before confirming!' | translate }}\r\n        </p>\r\n      </div>\r\n\r\n    </div>\r\n    <div class=\"modal-footer\">\r\n      <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n        {{ 'Submit' | translate }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</ng-template>\r\n\r\n<ng-template #modalManageReferee let-modal>\r\n  <app-modal-manage-referees seasonId=\"{{ seasonId }}\" />\r\n</ng-template>\r\n\r\n<ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\r\n  <div\r\n    class=\"d-flex flex-row align-items-center justify-content-end\"\r\n    style=\"gap: 4px;\"\r\n  >\r\n    <button type=\"button\" rippleEffect class=\"auto-schedule-btn btn btn-link\" placement=\"right\" container=\"body\"\r\n            ngbTooltip=\"Auto Schedule\"\r\n            (click)=\"onClickAutoSchedule(data)\"\r\n    >\r\n      <i class=\"fa-solid fa-wand-magic-sparkles\"\r\n      ></i>\r\n    </button>\r\n    <app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\"\r\n                             btnStyle=\"font-size:15px;color:black!important\"\r\n                             btnActionStyle=\"font-size:20px;color:black!important\"></app-btn-dropdown-action>\r\n  </div>\r\n</ng-template>\r\n\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}