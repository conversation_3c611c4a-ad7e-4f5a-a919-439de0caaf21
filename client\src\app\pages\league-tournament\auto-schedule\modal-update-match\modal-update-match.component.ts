import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormlyFieldConfig } from "@ngx-formly/core";
import { FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { SeasonService } from "../../../../services/season.service";
import { TournamentService } from 'app/services/tournament.service';
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { LoadingService } from "../../../../services/loading.service";
import { AutoScheduleService } from "../../../../services/auto-schedule.service";
import Swal from "sweetalert2";
import { ToastrService } from "ngx-toastr";

@Component({
    selector: 'app-modal-update-match',
    templateUrl: './modal-update-match.component.html',
    styleUrls: ['./modal-update-match.component.scss']
})
export class ModalUpdateMatchComponent {

    @Output() onSubmit = new EventEmitter();
    @Input() seasonId: any;
    @Input() timeSlotInfo: any;

    constructor(
        private _translateService: TranslateService,
        private _seasonService: SeasonService,
        private _modalService: NgbModal,
        private _loadingService: LoadingService,
        private _autoScheduleService: AutoScheduleService,
        private _toastService: ToastrService
    ) {
    }

    updateMatchForm = new FormGroup({});
    updateMatchModel = {};
    updateMatchFields: FormlyFieldConfig[] = [
        {
            key: 'time_slot_id',
            type: 'input',
            props: {
                type: 'hidden'
            },
        },
        {
            key: 'list_referees',
            type: 'ng-select',
            props: {
                multiple: true,
                hideOnMultiple: true,
                defaultValue: [],
                label: this._translateService.instant('Current match referees'),
                placeholder: this._translateService.instant('Select match referees'),
                options: []
            },
            hooks: {}
        }
    ];

    ngOnInit() {
        this.getSeasonReferees();
        this.updateMatchFields[0].defaultValue = this.timeSlotInfo.time_slot_id;
    }

    onSubmitUpdateMatch(model) {
        console.log(model);
        this._autoScheduleService.updateMatchReferee(model).subscribe((res) => {
            this._toastService.success(this._translateService.instant('Match referee updated successfully'));
            this.onSubmit.emit(res);
            this._modalService.dismissAll();
        }, (error) => {
            console.log("🚀 ~ onSubmitUpdateMatch ~ error: ", error);
        });
    }

    closeModal() {
        this.updateMatchModel = {};
        this._modalService.dismissAll();
    }

    getSeasonReferees() {
        this._loadingService.show();
        this._seasonService.getListSeasonReferees(this.seasonId).subscribe((response) => {
            // set options for field list_referees
            this.updateMatchFields[1].props.options = response['data'].map((referee) => {
                return {
                    label: referee.user
                        ? `${referee.user.first_name} ${referee.user.last_name}`
                        : referee.referee_name,
                    value: referee.id
                };
            })

            // set value for field list_referees
            this.updateMatchModel = {
                ...this.updateMatchModel,
                list_referees: this.timeSlotInfo.referee_ids
            }
        }, () => { }, () => {
            this._loadingService.dismiss();
        });
    }
}
