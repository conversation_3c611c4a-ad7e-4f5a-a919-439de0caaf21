<div class="modal-header">
    <h5 class="modal-title" id="modalAddBreak">{{ "Add Break" | translate }}</h5>
    <button
            type="button"
            class="close"
            (click)="closeModal()"
            aria-label="Close"
    >
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<form
        [formGroup]="addBreakForm"
        (ngSubmit)="onSubmitCrudBreak(addBreakModel)"
>
    <div class="modal-body" tabindex="0" ngbAutofocus>
        <formly-form
                [form]="addBreakForm"
                [fields]="addBreakFields"
                [model]="addBreakModel"
                (submit)="onSubmitCrudBreak(addBreakModel)"
        ></formly-form>
    </div>
    <div class="modal-footer">
        <button type="submit" class="w-100 btn btn-primary" rippleEffect
            [disabled]="addBreakForm.invalid"
        >
            {{ 'Submit' | translate }}
        </button>
    </div>
</form>