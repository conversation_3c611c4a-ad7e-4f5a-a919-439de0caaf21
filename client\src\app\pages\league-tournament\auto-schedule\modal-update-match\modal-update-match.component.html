<div class="modal-header">
  <h5 class="modal-title" id="updateMatch">{{ "Update Match Referee" | translate }}</h5>
  <button
    type="button"
    class="close"
    (click)="closeModal()"
    aria-label="Close"
  >
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<form
  [formGroup]="updateMatchForm"
  (ngSubmit)="onSubmitUpdateMatch(updateMatchModel)"
>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <formly-form
      [form]="updateMatchForm"
      [fields]="updateMatchFields"
      [model]="updateMatchModel"
      (submit)="onSubmitUpdateMatch(updateMatchModel)"
    ></formly-form>
  </div>
  <div class="modal-footer">
      <button type="submit" class="w-100 btn btn-primary" rippleEffect
      [disabled]="updateMatchForm.invalid"
      >
      {{ 'Update' | translate }}
    </button>
  </div>
</form>
