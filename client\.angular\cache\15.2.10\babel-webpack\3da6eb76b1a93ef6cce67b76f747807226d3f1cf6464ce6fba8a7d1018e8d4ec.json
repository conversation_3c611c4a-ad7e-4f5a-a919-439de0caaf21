{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { AppConfig } from 'app/app-config';\n// sweet alert\nimport Swal from 'sweetalert2';\nimport { ModalAddGroupTeamComponent } from './modal-add-group-team/modal-add-group-team.component';\nimport { FormGroup } from '@angular/forms';\nimport { CorePipesModule } from '@core/pipes/pipes.module';\nimport { SortByNamePipe } from '@core/pipes/sort-by-name.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"app/services/tournament.service\";\nimport * as i4 from \"app/services/team.service\";\nimport * as i5 from \"app/services/stage.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"app/services/commons.service\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"@angular/common/http\";\nimport * as i11 from \"@core/pipes/sort-by-name.pipe\";\nfunction StageTeamsComponent_button_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbTooltip\", \"Please remove match before add team\");\n  }\n}\nfunction StageTeamsComponent_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.addGroupTeam());\n    });\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, StageTeamsComponent_button_6_ng_container_3_Template, 3, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.stage_type == ctx_r0.AppConfig.TOURNAMENT_TYPES.league ? ctx_r0._translateService.instant(\"Add Team\") : ctx_r0.stage_type == ctx_r0.AppConfig.TOURNAMENT_TYPES.groups ? ctx_r0._translateService.instant(\"Add Team & Group\") : ctx_r0._translateService.instant(\"Add Team\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.allowEditTeam == false);\n  }\n}\nfunction StageTeamsComponent_div_7_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbTooltip\", \"Please remove match before add team\");\n  }\n}\nfunction StageTeamsComponent_div_7_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbTooltip\", \"Please remove match before add team\");\n  }\n}\nfunction StageTeamsComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addGroupTeam());\n    });\n    i0.ɵɵelement(2, \"i\", 10);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵtemplate(5, StageTeamsComponent_div_7_ng_container_5_Template, 3, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_7_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.importTeams());\n    });\n    i0.ɵɵelement(7, \"i\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵtemplate(10, StageTeamsComponent_div_7_ng_container_10_Template, 3, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.allowEditTeam == false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, \"Add Team\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowEditTeam == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.allowEditTeam == false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 8, \"Import From Groups\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowEditTeam == false);\n  }\n}\nfunction StageTeamsComponent_div_10_span_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 25);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_10_span_1_i_3_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const group_stage_r13 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.removeGroup(group_stage_r13));\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 1, \"Remove Group\"));\n  }\n}\nfunction StageTeamsComponent_div_10_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, StageTeamsComponent_div_10_span_1_i_3_Template, 2, 3, \"i\", 23);\n    i0.ɵɵelementStart(4, \"i\", 24);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_10_span_1_Template_i_click_4_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const group_stage_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r22 = i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(12);\n      return i0.ɵɵresetView(ctx_r22.openModalEdit(_r3, group_stage_r13[0]));\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_stage_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(2, 4, \"group\"), \" \", group_stage_r13[0].group, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.allowEditTeam == true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(5, 6, \"Edit group\"));\n  }\n}\nfunction StageTeamsComponent_div_10_span_2_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 27);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_10_span_2_i_1_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const group_stage_r13 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.removeAllTeam(group_stage_r13));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r26.allowEditTeam == false);\n  }\n}\nfunction StageTeamsComponent_div_10_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtemplate(1, StageTeamsComponent_div_10_span_2_i_1_Template, 1, 1, \"i\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.allowEditTeam == true);\n  }\n}\nfunction StageTeamsComponent_div_10_div_4_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_10_div_4_a_19_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const stage_r30 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.removeTeam(stage_r30, \"remove\"));\n    });\n    i0.ɵɵelementStart(1, \"i\", 43);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_10_div_4_a_19_Template_i_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const stage_r30 = i0.ɵɵnextContext().$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.removeTeam(stage_r30, \"remove\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleMap(ctx_r32.btnStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 3, \"Remove Team\"));\n  }\n}\nfunction StageTeamsComponent_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"div\", 31);\n    i0.ɵɵelement(4, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵelement(6, \"h4\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 35)(8, \"div\", 36)(9, \"button\", 37);\n    i0.ɵɵelement(10, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 39);\n    i0.ɵɵelementContainerStart(12);\n    i0.ɵɵelementStart(13, \"a\", 40);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_div_10_div_4_Template_a_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const stage_r30 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.changeTeam(stage_r30, \"edit\"));\n    });\n    i0.ɵɵelement(14, \"i\", 41);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18);\n    i0.ɵɵtemplate(19, StageTeamsComponent_div_10_div_4_a_19_Template, 5, 5, \"a\", 42);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const stage_r30 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"src\", stage_r30.team.club.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", stage_r30.team.name, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(ctx_r17.btnStyle);\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleMap(ctx_r17.btnStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 8, \"Change Team\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.allowEditTeam == true);\n  }\n}\nfunction StageTeamsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, StageTeamsComponent_div_10_span_1_Template, 6, 8, \"span\", 19);\n    i0.ɵɵtemplate(2, StageTeamsComponent_div_10_span_2_Template, 2, 1, \"span\", 19);\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtemplate(4, StageTeamsComponent_div_10_div_4_Template, 20, 10, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_stage_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.stage_type == ctx_r2.AppConfig.TOURNAMENT_TYPES.groups);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.stage_type != ctx_r2.AppConfig.TOURNAMENT_TYPES.groups);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.sortBy(group_stage_r13));\n  }\n}\nfunction StageTeamsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 44);\n    i0.ɵɵlistener(\"ngSubmit\", function StageTeamsComponent_ng_template_11_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onEdit(ctx_r41.model));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45)(2, \"h4\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function StageTeamsComponent_ng_template_11_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r42);\n      const modal_r40 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r40.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵelementStart(6, \"span\", 48);\n    i0.ɵɵtext(7, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 49)(9, \"div\", 50);\n    i0.ɵɵelement(10, \"img\", 51);\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"formly-form\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"button\", 54);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.form);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 7, \"Edit group\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Enter group name\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"form\", ctx_r4.form)(\"fields\", ctx_r4.fields)(\"model\", ctx_r4.model);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 11, \"Save\"));\n  }\n}\nexport class StageTeamsComponent {\n  constructor(_trans, _route, _router, _tournamentService, _teamService, _stageService, _loadingService, _toastrService, _translateService, _commonsService, _modalService, _http, _sortByNamePipe) {\n    this._trans = _trans;\n    this._route = _route;\n    this._router = _router;\n    this._tournamentService = _tournamentService;\n    this._teamService = _teamService;\n    this._stageService = _stageService;\n    this._loadingService = _loadingService;\n    this._toastrService = _toastrService;\n    this._translateService = _translateService;\n    this._commonsService = _commonsService;\n    this._modalService = _modalService;\n    this._http = _http;\n    this._sortByNamePipe = _sortByNamePipe;\n    this.group_stages = [];\n    this.allowEditTeam = true;\n    this.onDataChange = new EventEmitter();\n    this.stage_type = null;\n    this.team_by_group = [];\n    this.team_id = null;\n    this.groups = [];\n    this.AppConfig = AppConfig;\n    this.form = new FormGroup({});\n    this.model = {};\n    this.fields = [{\n      key: 'group_name',\n      type: 'input',\n      props: {\n        label: '',\n        placeholder: this._trans.instant('Group Name'),\n        required: true,\n        maxLength: 20\n      },\n      // validate message\n      validation: {\n        messages: {\n          required: this._trans.instant('Group Name is required')\n        }\n      }\n    }];\n    this.rowActions = [{\n      type: 'collection',\n      buttons: [{\n        label: 'Change team',\n        icon: 'fa-regular fa-pen-to-square'\n      }, {\n        label: 'Delete',\n        icon: 'fa-regular fa-trash'\n      }]\n    }];\n  }\n  ngOnInit() {\n    this.stage_type = this.stage.type;\n    this.getTeamsByGroup();\n  }\n  getTeamByGroup(group_id) {\n    this._loadingService.show();\n    return this._teamService.getTeamNotInStage(this.stage.id, group_id).toPromise();\n  }\n  removeTeamFromStage(stage_team, action) {\n    let params = new FormData();\n    params.append('action', action);\n    params.append('data[' + stage_team.id + '][stage_id]', stage_team.stage_id);\n    params.append('data[' + stage_team.id + '][team_id]', stage_team.team_id);\n    params.append('data[' + stage_team.id + '][group]', stage_team.group);\n    return this._stageService.removeTeamFromStage(params).toPromise();\n  }\n  removeTeam(stage_team, action) {\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: `\n        <div class=\"text-center\">\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n\n        <p class=\"text-center\"> ` + this._translateService.instant('You will not be able to recover this team') + `      \n        </p>\n      </div>\n        `,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true,\n      showLoaderOnConfirm: true,\n      preConfirm: () => {\n        return new Promise(resolve => {\n          this.removeTeamFromStage(stage_team, action).then(res => {\n            if (res) {\n              this.getTeamsInStage();\n              Swal.fire({\n                title: this._translateService.instant('Success'),\n                text: this._translateService.instant('Team removed successfully'),\n                icon: 'success',\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              }).then(result => {\n                if (result.isConfirmed) {}\n              });\n            } else {\n              Swal.fire({\n                title: this._translateService.instant('Error'),\n                text: this._translateService.instant('Team not removed'),\n                icon: 'error',\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              });\n            }\n          });\n        });\n      }\n    });\n  }\n  changeTeam(stage_team, action) {\n    this.getTeamByGroup(this.tournament.group_id).then(res => {\n      res = res.data;\n      if (res) {\n        // sort by name\n        res.sort((a, b) => {\n          if (a.name < b.name) {\n            return -1;\n          }\n          if (a.name > b.name) {\n            return 1;\n          }\n          return 0;\n        });\n        let options = '';\n        res.forEach(team => {\n          options += `<option value=\"${team.id}\">${team.name}</option>`;\n        });\n        // pop up to select team\n        Swal.fire({\n          title: this._translateService.instant('Select team to change'),\n          html: `<select class=\"form-control\" id=\"team_id\">\n        ${options}\n        </select>`,\n          showCancelButton: true,\n          confirmButtonText: this._translateService.instant('Change'),\n          cancelButtonText: this._translateService.instant('Cancel'),\n          reverseButtons: true,\n          showLoaderOnConfirm: true,\n          preConfirm: () => {\n            return new Promise(resolve => {\n              let team_id = document.getElementById('team_id').value;\n              let params = new FormData();\n              params.append('action', action);\n              params.append('data[' + stage_team.id + '][stage_id]', stage_team.stage_id);\n              params.append('data[' + stage_team.id + '][team_id]', team_id);\n              if (stage_team.group) {\n                params.append('data[' + stage_team.id + '][group]', stage_team.group);\n              }\n              this._stageService.changeTeamInStage(params).subscribe(res => {\n                this.getTeamsByGroup();\n                if (res) {\n                  Swal.fire({\n                    title: this._translateService.instant('Success'),\n                    html: `\n                      <div class=\"text-center\">\n                      <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n              \n                      <p class=\"text-center\"> ` + this._translateService.instant('Team changed successfully') + `      \n                      </p>\n                    </div>\n                      `,\n                    confirmButtonText: this._translateService.instant('OK'),\n                    confirmButtonColor: '#3085d6'\n                  }).then(result => {\n                    this.getTeamsInStage();\n                  });\n                } else {\n                  Swal.fire({\n                    title: 'Error',\n                    text: 'Team not changed',\n                    icon: 'error',\n                    confirmButtonText: this._translateService.instant('OK'),\n                    confirmButtonColor: '#3085d6'\n                  });\n                }\n              }, error => {\n                console.log('error', error);\n                Swal.fire({\n                  title: 'Error',\n                  text: 'No team available for change',\n                  icon: 'error',\n                  confirmButtonText: this._translateService.instant('OK'),\n                  confirmButtonColor: '#3085d6'\n                });\n              });\n            });\n          }\n        });\n      }\n    });\n  }\n  addGroupTeam() {\n    if (this.allowEditTeam == false) {\n      Swal.fire({\n        title: this._translateService.instant('Error'),\n        text: this._translateService.instant('You must remove all schedule before add team'),\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK'),\n        confirmButtonColor: '#3085d6'\n      });\n      return;\n    }\n    this.openModalAddTeam();\n  }\n  getTeamsByGroup() {\n    if (!this.tournament.group_id) return;\n    this.getTeamByGroup(this.tournament.group_id).then(res => {\n      res = res.data;\n      if (res) {\n        this.team_by_group = res;\n      }\n    });\n  }\n  openModalAddTeam() {\n    if (this.team_by_group.length == 0) {\n      Swal.fire({\n        title: this._translateService.instant('No team to add'),\n        icon: 'info',\n        confirmButtonText: this._translateService.instant('OK'),\n        confirmButtonColor: '#3085d6'\n      });\n      return;\n    }\n    const modalRef = this._modalService.open(ModalAddGroupTeamComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      centered: true,\n      windowClass: 'modal-add-group-team'\n    });\n    if (this.team_by_group) {\n      const sortTeam = this._sortByNamePipe.transform(this.team_by_group, 'name');\n      modalRef.componentInstance.teams = sortTeam;\n      modalRef.componentInstance.stage_type = this.stage_type;\n      // get current group\n      let current_group = [];\n      this.group_stages.forEach(group => {\n        current_group.push(group[0].group);\n      });\n      modalRef.componentInstance.current_group = current_group;\n      modalRef.result.then(result => {\n        if (result) {\n          let group_name = this.stage_type == 'Groups' ? result.group_name : null;\n          let selected_team = result.selected_team;\n          selected_team = selected_team.join(',');\n          let params = new FormData();\n          params.append('stage_id', this.stage.id);\n          if (this.stage_type == 'Groups') {\n            params.append('group', group_name);\n          }\n          params.append('teams', selected_team);\n          this._stageService.createTeamMultiple(params).subscribe(res => {\n            if (res) {\n              this.getTeamsByGroup();\n              Swal.fire({\n                title: this._translateService.instant('Success'),\n                html: `\n                  <div class=\"text-center\">\n                    <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n                    <p class=\"text-center\"> ` + this._translateService.instant('Teams added successfully') + `      \n                    </p>\n                  </div>`,\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              }).then(result => {\n                this.getTeamsInStage();\n              });\n            } else {\n              Swal.fire({\n                title: 'Error',\n                text: 'Team not added',\n                icon: 'error',\n                confirmButtonText: this._translateService.instant('OK'),\n                confirmButtonColor: '#3085d6'\n              });\n            }\n          });\n        }\n      });\n    }\n  }\n  removeGroup(group_stage) {\n    console.log('removeGroup');\n    let teams = [];\n    group_stage.forEach(group => {\n      teams.push(group.team_id);\n    });\n    // implode teams\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: ` \n      <div class=\"text-center\">\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n        <p class=\"text-center\"> ` + this._translateService.instant('You will not be able to recover this group') + `\n        </p>\n      </div>\n      \n      `,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true\n    }).then(result => {\n      let stage_id = group_stage[0].stage_id;\n      let group = group_stage[0].group;\n      teams = teams.join(',');\n      let params = new FormData();\n      // stage_id: 139\n      // group: A\n      // teams: 7, 8, 9\n      params.append('stage_id', stage_id);\n      params.append('group', group);\n      params.append('teams', teams);\n      if (result.value) {\n        this._stageService.removeTeamMultiple(params).subscribe(res => {\n          if (res) {\n            this._loadingService.show();\n            this.getTeamsInStage();\n            this.getTeamsByGroup();\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: this._translateService.instant('Group removed successfully'),\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          } else {\n            Swal.fire({\n              title: 'Error',\n              text: this._translateService.instant('Group not removed'),\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          }\n        });\n      }\n    });\n  }\n  removeAllTeam(group_stage) {\n    let teams = [];\n    group_stage.forEach(group => {\n      teams.push(group.team_id);\n    });\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      html: `\n      <div class=\"text-center\">\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\n\n        <p class=\"text-center\"> ` + this._translateService.instant('You are about to remove all teams in this group') + `      \n        </p>\n      </div>`,\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('No'),\n      reverseButtons: true\n    }).then(result => {\n      let stage_id = group_stage[0].stage_id;\n      teams = teams.join(',');\n      let params = new FormData();\n      // stage_id: 139\n      // teams: 7, 8, 9\n      params.append('stage_id', stage_id);\n      params.append('teams', teams);\n      if (result.value) {\n        this._stageService.removeTeamMultiple(params).subscribe(res => {\n          if (res) {\n            Swal.fire({\n              title: this._translateService.instant('Success'),\n              text: this._translateService.instant('Team removed successfully'),\n              icon: 'success',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            }).then(result => {\n              // if (result.isConfirmed) {\n              this._loadingService.show();\n              setTimeout(() => {\n                this.getTeamsByGroup();\n                this.getTeamsInStage();\n              }, 400);\n              // }\n            });\n          } else {\n            Swal.fire({\n              title: 'Error',\n              text: 'Teams not removed',\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n              confirmButtonColor: '#3085d6'\n            });\n          }\n        });\n      }\n    });\n  }\n  getTeamsInStage() {\n    this._stageService.getTeamsInStage(this.stage.id).subscribe(res => {\n      this.group_stages = res.data;\n      this.onDataChange.emit(this.group_stages);\n    });\n    this._loadingService.dismiss();\n  }\n  checkTeamAlreadyInStage(teams) {\n    return new Promise((resolve, reject) => {\n      // check if teams are already in stage\n      this._teamService.getTeamListInStage(this.stage.id).subscribe(res => {\n        let teams_in_stage = res.data;\n        teams_in_stage.sort((a, b) => a.id - b.id);\n        teams_in_stage = teams_in_stage.map(team => team.id);\n        let list_teams_in_stage = teams_in_stage.join(',');\n        let list_teams = teams.join(',');\n        if (list_teams == list_teams_in_stage) {\n          console.log('teams already imported');\n          resolve(false);\n        } else {\n          // find the teams that are not in stage\n          let teams_not_in_stage = teams.filter(team => !teams_in_stage.includes(team));\n          resolve(teams_not_in_stage);\n        }\n      });\n    });\n  }\n  sortBy(stage_teams) {\n    stage_teams.sort((a, b) => a.team.name.localeCompare(b.team.name));\n    return stage_teams;\n  }\n  importTeams() {\n    this._stageService.getDataByTournament(this.tournament.id).subscribe(res => {\n      let stages = res.data;\n      stages = stages.filter(stage => stage.type == 'Groups');\n      Swal.fire({\n        title: this._translateService.instant('Do you want to import teams from') + ' ' + this.tournament.name + ' ' + stages[0].name + '? ',\n        showCancelButton: true,\n        cancelButtonText: this._translateService.instant('No'),\n        confirmButtonText: this._translateService.instant('Yes')\n      }).then(result => {\n        if (result.isConfirmed) {\n          let stage_id = stages[0].id;\n          // get teams from stages\n          this._teamService.getTeamListInStage(stage_id).subscribe(res => {\n            let teams = res.data;\n            teams.sort((a, b) => a.id - b.id);\n            teams = teams.map(team => team.id);\n            // check if teams are already in stage\n            this.checkTeamAlreadyInStage(teams).then(res => {\n              if (res) {\n                teams = res;\n                // sort teams by name\n                let params = new FormData();\n                teams = teams.join(',');\n                params.append('stage_id', this.stage.id);\n                params.append('teams', teams);\n                this._stageService.createTeamMultiple(params).subscribe(res => {\n                  if (res) {\n                    Swal.fire({\n                      title: 'Success',\n                      text: this._translateService.instant('Teams imported successfully'),\n                      icon: 'success',\n                      confirmButtonText: this._translateService.instant('OK'),\n                      confirmButtonColor: '#3085d6'\n                    }).then(result => {\n                      this.getTeamsByGroup();\n                      this._loadingService.show();\n                      setTimeout(() => {\n                        this._stageService.getTeamsInStage(this.stage.id).subscribe(res => {\n                          this.group_stages = res.data;\n                          this._loadingService.dismiss();\n                        });\n                      }, 400);\n                    });\n                  } else {\n                    Swal.fire({\n                      title: 'Error',\n                      text: this._translateService.instant('Teams not imported'),\n                      icon: 'error',\n                      confirmButtonText: this._translateService.instant('OK'),\n                      confirmButtonColor: '#3085d6'\n                    });\n                  }\n                });\n              } else {\n                Swal.fire({\n                  title: 'Error',\n                  text: this._translateService.instant('No teams available to add'),\n                  icon: 'error',\n                  confirmButtonText: this._translateService.instant('OK'),\n                  confirmButtonColor: '#3085d6'\n                });\n              }\n            });\n          });\n        }\n      });\n    });\n  }\n  openModalEdit(content, groupStage) {\n    this.groupStage = groupStage;\n    if (this.groupStage.group) {\n      this.model = {\n        group_name: this.groupStage.group\n      };\n    }\n    this._modalService.open(content, {\n      ariaLabelledBy: 'modal-basic-title'\n    }).result.then(result => {\n      this.groupStage = null;\n    }, reason => {\n      this.groupStage = null;\n    });\n  }\n  onEdit(model) {\n    this.groupStage;\n    if (this.form.invalid || !this.groupStage) return;\n    let params = new FormData();\n    params.append('stage_id', this.groupStage.stage_id);\n    params.append('old_group', this.groupStage.group.toString());\n    params.append('group', model.group_name);\n    params.append('all_teams_in_groups', 'true');\n    this._loadingService.show();\n    this.editGroup(params).then(res => {\n      this.getTeamsInStage();\n      this._modalService.dismissAll();\n    }, error => {\n      if (error.errors) {\n        this.form.controls[\"group_name\"].setErrors({\n          serverError: error.message\n        });\n      }\n    });\n  }\n  editGroup(params) {\n    return this._stageService.editGroup(params).toPromise().then(res => {\n      if (res) {\n        return res;\n      }\n    });\n  }\n  static #_ = this.ɵfac = function StageTeamsComponent_Factory(t) {\n    return new (t || StageTeamsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TournamentService), i0.ɵɵdirectiveInject(i4.TeamService), i0.ɵɵdirectiveInject(i5.StageService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.ToastrService), i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i8.CommonsService), i0.ɵɵdirectiveInject(i9.NgbModal), i0.ɵɵdirectiveInject(i10.HttpClient), i0.ɵɵdirectiveInject(i11.SortByNamePipe));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageTeamsComponent,\n    selectors: [[\"stage-teams\"]],\n    inputs: {\n      stage: \"stage\",\n      tournament: \"tournament\",\n      group_stages: \"group_stages\",\n      allowEditTeam: \"allowEditTeam\"\n    },\n    outputs: {\n      onDataChange: \"onDataChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([CorePipesModule, SortByNamePipe])],\n    decls: 13,\n    vars: 6,\n    consts: [[1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [\"class\", \"btn btn-primary float-right\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-group mt-1 float-right\", 3, \"disabled\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"row\"], [\"style\", \"width: 100%; padding: 10px\", \"class\", \"col-12 bg-light card p-1\", 4, \"ngFor\", \"ngForOf\"], [\"content\", \"\"], [1, \"btn\", \"btn-primary\", \"float-right\", 3, \"click\"], [1, \"fa\", \"fa-plus\"], [4, \"ngIf\"], [1, \"ml-1\", 3, \"ngbTooltip\"], [1, \"fa\", \"fa-info-circle\"], [1, \"btn-group\", \"mt-1\", \"float-right\", 3, \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-warning\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-arrow-up-from-line\"], [1, \"col-12\", \"bg-light\", \"card\", \"p-1\", 2, \"width\", \"100%\", \"padding\", \"10px\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [1, \"row\", \"d-flex\", \"justify-content-left\"], [\"class\", \"col-lg-3 col-md-6 col-sm-12 mt-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-primary\"], [\"style\", \"z-index: 1; position: relative\", \"class\", \"fa fa-trash float-right text-danger p-1\", \"placement\", \"left\", 3, \"ngbTooltip\", \"click\", 4, \"ngIf\"], [\"placement\", \"left\", 1, \"fa\", \"fa-edit\", \"float-right\", \"text-warning\", \"p-1\", 2, \"z-index\", \"1\", \"position\", \"relative\", 3, \"ngbTooltip\", \"click\"], [\"placement\", \"left\", 1, \"fa\", \"fa-trash\", \"float-right\", \"text-danger\", \"p-1\", 2, \"z-index\", \"1\", \"position\", \"relative\", 3, \"ngbTooltip\", \"click\"], [\"style\", \"z-index: 1; position: relative\", \"class\", \"fa fa-trash fa-lg float-right text-danger\", \"placement\", \"left\", \"ngbTooltip\", \"Remove all teams\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"placement\", \"left\", \"ngbTooltip\", \"Remove all teams\", 1, \"fa\", \"fa-trash\", \"fa-lg\", \"float-right\", \"text-danger\", 2, \"z-index\", \"1\", \"position\", \"relative\", 3, \"disabled\", \"click\"], [1, \"col-lg-3\", \"col-md-6\", \"col-sm-12\", \"mt-1\"], [1, \"rounded\", \"bg-white\"], [1, \"row\", \"p-1\", \"align-items-center\"], [1, \"col-auto\"], [\"alt\", \"logo\", \"width\", \"60px\", \"height\", \"60px\", 1, \"avatar\", \"avatar-sm\", \"bg-white\", \"rounded-0\", 3, \"src\"], [1, \"col\"], [1, \"text-nowrap\", 3, \"innerHTML\"], [1, \"col\", \"text-right\"], [\"ngbDropdown\", \"\", \"container\", \"body\", 1, \"col-auto\", \"p-0\", \"m-0\"], [\"type\", \"button\", \"ngbDropdownToggle\", \"\", \"data-toggle\", \"dropdown\", 1, \"btn\", \"hide-arrow\", \"p-0\", \"text-secondary\"], [1, \"fa-regular\", \"fa-ellipsis-vertical\"], [\"ngbDropdownMenu\", \"\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"placement\", \"left\", \"ngbTooltip\", \"Change another team\", 1, \"fa-light\", \"fa-arrows-repeat\", \"fa-lg\", \"mr-1\"], [\"ngbDropdownItem\", \"\", 3, \"style\", \"click\", 4, \"ngIf\"], [\"id\", \"trash\", \"placement\", \"left\", \"ngbTooltip\", \"Remove this team\", 1, \"fal\", \"fa-trash\", \"fa-lg\", \"mr-1\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"modal-header\"], [\"id\", \"modal-basic-title\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"text-center\"], [\"src\", \"assets/images/ai/Frame.svg\", \"alt\", \"Frame\", \"width\", \"200px\", \"height\", \"149px\"], [3, \"form\", \"fields\", \"model\"], [1, \"modal-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"]],\n    template: function StageTeamsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"div\");\n        i0.ɵɵtemplate(6, StageTeamsComponent_button_6_Template, 4, 2, \"button\", 3);\n        i0.ɵɵtemplate(7, StageTeamsComponent_div_7_Template, 11, 10, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6);\n        i0.ɵɵtemplate(10, StageTeamsComponent_div_10_Template, 5, 3, \"div\", 7);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(11, StageTeamsComponent_ng_template_11_Template, 19, 13, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 4, \"Stage Teams\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == ctx.AppConfig.TOURNAMENT_TYPES.league || ctx.stage_type == ctx.AppConfig.TOURNAMENT_TYPES.groups);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == \"Knockout\" && ctx.tournament.type_knockout == ctx.AppConfig.KNOCKOUT_TYPES.type4);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.group_stages);\n      }\n    },\n    styles: [\"i[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\ni[_ngcontent-%COMP%]:hover {\\n  color: #6f6b7d;\\n}\\ni[_ngcontent-%COMP%]:active {\\n  transform: scale(1.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlLXRlYW1zL3N0YWdlLXRlYW1zLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZUFBQTtBQUNGO0FBQ0U7RUFDRSxjQUFBO0FBQ0o7QUFDRTtFQUVFLHFCQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyJpIHtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgY29sb3I6ICM2ZjZiN2Q7XHJcbiAgfVxyXG4gICY6YWN0aXZlIHtcclxuICAgIC8vIHNjYWxlIDEuMlxyXG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpO1xyXG5cclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAAoBA,YAAY,QAA+B,eAAe;AAc9E,SAASC,SAAS,QAAQ,gBAAgB;AAE1C;AACA,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,0BAA0B,QAAQ,uDAAuD;AAClG,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,+BAA+B;;;;;;;;;;;;;;;ICNxDC,6BAA6C;IAC3CA,gCAAwE;IACtEA,wBAAiC;IACnCA,iBAAO;IACTA,0BAAe;;;IAHMA,eAAoD;IAApDA,kEAAoD;;;;;;IAd3EA,iCAG6B;IAAzBA;MAAAA;MAAA;MAAA,OAASA,oCAAc;IAAA,EAAC;IAC1BA,wBAA0B;IAE1BA,YAOA;IAAAA,gGAIe;IACjBA,iBAAS;;;;IAZPA,eAOA;IAPAA,mTAOA;IAAeA,eAA4B;IAA5BA,oDAA4B;;;;;IAYzCA,6BAA6C;IAC3CA,gCAAwE;IACtEA,wBAAiC;IACnCA,iBAAO;IACTA,0BAAe;;;IAHMA,eAAoD;IAApDA,kEAAoD;;;;;IAWzEA,6BAA6C;IAC3CA,gCAAwE;IACtEA,wBAAiC;IACnCA,iBAAO;IACTA,0BAAe;;;IAHMA,eAAoD;IAApDA,kEAAoD;;;;;;IAlB7EA,+BAA2K;IACzIA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IACtDA,wBAA0B;IAE1BA,YACA;;IAAAA,6FAIe;IACjBA,iBAAS;IAGTA,kCAA4F;IAA5DA;MAAAA;MAAA;MAAA,OAASA,oCAAa;IAAA,EAAC;IACrDA,wBAA8C;IAE9CA,YACA;;IAAAA,+FAIe;IACjBA,iBAAS;;;;IAtB4HA,wDAAmC;IAItKA,eACA;IADAA,iEACA;IAAeA,eAA4B;IAA5BA,oDAA4B;IAQWA,eAAmC;IAAnCA,wDAAmC;IAGzFA,eACA;IADAA,2EACA;IAAeA,eAA4B;IAA5BA,oDAA4B;;;;;;IAkBzCA,6BAE4C;IADMA;MAAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;;IACxCA,iBAAI;;;IAA9CA,4EAAyC;;;;;;IAJ7CA,gCAAmF;IAAAA,YAEjF;;IAAAA,+EAEgD;IAChDA,6BAC0F;IAAhDA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAASA,0DAAkC,CAAC,EAAE;IAAA,EAAC;;IAACA,iBAAI;;;;;IANbA,eAEjF;IAFiFA,4FAEjF;IAAIA,eAA2B;IAA3BA,oDAA2B;IAI7BA,eAAuC;IAAvCA,0EAAuC;;;;;;IAKzCA,6BAEoE;IADhBA;MAAAA;MAAA;MAAA;MAAA,OAASA,qDAA0B;IAAA,EAAC;IACpBA,iBAAI;;;;IAAtEA,yDAAmC;;;;;IAHvCA,gCAAmF;IACjFA,+EAEwE;IAC1EA,iBAAO;;;;IAHDA,eAA2B;IAA3BA,oDAA2B;;;;;;IAkCjBA,6BACqB;IAD4BA;MAAAA;MAAA;MAAA;MAAA,OAASA,6CAAkB,QAAQ,CAAC;IAAA,EAAC;IAEpFA,6BACiD;IADHA;MAAAA;MAAA;MAAA;MAAA,OAASA,6CAAkB,QAAQ,CAAC;IAAA,EAAC;IAClCA,iBAAI;IACrDA,4BAAM;IAAAA,YAA+B;;IAAAA,iBAAO;;;;IAH5CA,+BAAkB;IAGZA,eAA+B;IAA/BA,yDAA+B;;;;;;IAhCrDA,+BAAuG;IAI/FA,0BACkB;IACpBA,iBAAM;IAENA,+BAAiB;IACfA,yBAA2D;IAC7DA,iBAAM;IAENA,+BAA4B;IAItBA,yBAA+C;IACjDA,iBAAS;IACTA,gCAAqB;IACnBA,8BAAc;IACZA,8BAA0E;IAAvDA;MAAA;MAAA;MAAA;MAAA,OAASA,6CAAkB,MAAM,CAAC;IAAA,EAAC;IACpDA,yBACuC;IACvCA,6BAAM;IAAAA,aAA+B;;IAAAA,iBAAO;IAEhDA,0BAAe;IAEfA,8BAAc;IACZA,gFAKI;IACNA,0BAAe;IACjBA,iBAAM;;;;;IA/ByCA,eAAgC;IAAhCA,2EAAgC;IAK7EA,eAA6B;IAA7BA,kEAA6B;IAKiCA,eAAkB;IAAlBA,+BAAkB;IAMvBA,eAAkB;IAAlBA,+BAAkB;IAGjEA,eAA+B;IAA/BA,0DAA+B;IAKnCA,eAA2B;IAA3BA,oDAA2B;;;;;IA/CjDA,+BAC0D;IACxDA,8EAOO;IAGPA,8EAIO;IAEPA,+BAA6C;IAC3CA,8EAwCM;IACRA,iBAAM;;;;;IA1DCA,eAAqD;IAArDA,oFAAqD;IAUrDA,eAAqD;IAArDA,oFAAqD;IAOMA,eAAwB;IAAxBA,wDAAwB;;;;;;IAmDhGA,gCAAoD;IAA3BA;MAAAA;MAAA;MAAA,OAAYA,4CAAa;IAAA,EAAC;IACjDA,+BAA0B;IACuBA,YAA4B;;IAAAA,iBAAK;IAChFA,kCAA8F;IAA1DA;MAAA;MAAA;MAAA,OAASA,iCAAc,aAAa,CAAC;IAAA,EAAC;IACxEA,gCAAyB;IAAAA,sBAAO;IAAAA,iBAAO;IAG3CA,+BAAwB;IAEpBA,2BAA+E;IAC/EA,0BAAG;IACDA,aACF;;IAAAA,iBAAI;IAENA,mCAA2E;IAC7EA,iBAAM;IACNA,gCAA0B;IACsBA,aAAsB;;IAAAA,iBAAS;;;;IAjB3EA,uCAAkB;IAE2BA,eAA4B;IAA5BA,wDAA4B;IASvEA,eACF;IADEA,0EACF;IAEWA,eAAa;IAAbA,kCAAa;IAGoBA,eAAsB;IAAtBA,oDAAsB;;;AD/G1E,OAAM,MAAOC,mBAAmB;EAiC9BC,YACSC,MAAwB,EACxBC,MAAsB,EACtBC,OAAe,EACfC,kBAAqC,EACrCC,YAAyB,EACzBC,aAA2B,EAC3BC,eAA+B,EAC/BC,cAA6B,EAC7BC,iBAAmC,EACnCC,eAA+B,EAC/BC,aAAuB,EACvBC,KAAiB,EAChBC,eAA+B;IAZhC,WAAM,GAANZ,MAAM;IACN,WAAM,GAANC,MAAM;IACN,YAAO,GAAPC,OAAO;IACP,uBAAkB,GAAlBC,kBAAkB;IAClB,iBAAY,GAAZC,YAAY;IACZ,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,mBAAc,GAAdC,cAAc;IACd,sBAAiB,GAAjBC,iBAAiB;IACjB,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IACb,UAAK,GAALC,KAAK;IACJ,oBAAe,GAAfC,eAAe;IA3ChB,iBAAY,GAAQ,EAAE;IACtB,kBAAa,GAAG,IAAI;IACnB,iBAAY,GAAsB,IAAItB,YAAY,EAAO;IACnE,eAAU,GAAQ,IAAI;IACtB,kBAAa,GAAQ,EAAE;IACvB,YAAO,GAAG,IAAI;IACd,WAAM,GAAQ,EAAE;IAChB,cAAS,GAAGC,SAAS;IACrB,SAAI,GAAG,IAAIG,SAAS,CAAC,EAAE,CAAC;IACxB,UAAK,GAAG,EAAE;IACV,WAAM,GAAwB,CAC5B;MACEmB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,IAAI,CAACjB,MAAM,CAACkB,OAAO,CAAC,YAAY,CAAC;QAC9CC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;OACZ;MACD;MACAC,UAAU,EAAE;QACVC,QAAQ,EAAE;UACRH,QAAQ,EAAE,IAAI,CAACnB,MAAM,CAACkB,OAAO,CAAC,wBAAwB;;;KAI3D,CACF;IA2bM,eAAU,GAAmB,CAClC;MACEJ,IAAI,EAAE,YAAY;MAClBS,OAAO,EAAE,CACP;QACEP,KAAK,EAAE,aAAa;QAEpBQ,IAAI,EAAE;OACP,EACD;QACER,KAAK,EAAE,QAAQ;QAEfQ,IAAI,EAAE;OACP;KAEJ,CACF;EA3bE;EAEHC,QAAQ;IACN,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACb,IAAI;IACjC,IAAI,CAACc,eAAe,EAAE;EACxB;EAEAC,cAAc,CAACC,QAAQ;IACrB,IAAI,CAACxB,eAAe,CAACyB,IAAI,EAAE;IAC3B,OAAO,IAAI,CAAC3B,YAAY,CACrB4B,iBAAiB,CAAC,IAAI,CAACL,KAAK,CAACM,EAAE,EAAEH,QAAQ,CAAC,CAC1CI,SAAS,EAAE;EAChB;EAEAC,mBAAmB,CAACC,UAAU,EAAEC,MAAM;IACpC,IAAIC,MAAM,GAAG,IAAIC,QAAQ,EAAE;IAC3BD,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,MAAM,CAAC;IAC/BC,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,aAAa,EAAEG,UAAU,CAACK,QAAQ,CAAC;IAC3EH,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,YAAY,EAAEG,UAAU,CAACM,OAAO,CAAC;IACzEJ,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,UAAU,EAAEG,UAAU,CAACO,KAAK,CAAC;IACrE,OAAO,IAAI,CAACtC,aAAa,CAAC8B,mBAAmB,CAACG,MAAM,CAAC,CAACJ,SAAS,EAAE;EACnE;EAEAU,UAAU,CAACR,UAAU,EAAEC,MAAM;IAC3B7C,IAAI,CAACqD,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,eAAe,CAAC;MACtD6B,IAAI,EACF;;;;iCAIyB,GACzB,IAAI,CAACvC,iBAAiB,CAACU,OAAO,CAC5B,2CAA2C,CAC5C,GACD;;;SAGC;MACH8B,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,KAAK,CAAC;MACxDgC,gBAAgB,EAAE,IAAI,CAAC1C,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;MACtDiC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,UAAU,EAAE,MAAK;QACf,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;UAC7B,IAAI,CAACpB,mBAAmB,CAACC,UAAU,EAAEC,MAAM,CAAC,CAACmB,IAAI,CAAEC,GAAQ,IAAI;YAC7D,IAAIA,GAAG,EAAE;cACP,IAAI,CAACC,eAAe,EAAE;cACtBlE,IAAI,CAACqD,IAAI,CAAC;gBACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,SAAS,CAAC;gBAChDyC,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAClC,2BAA2B,CAC5B;gBACDM,IAAI,EAAE,SAAS;gBACfyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;gBACvD0C,kBAAkB,EAAE;eACrB,CAAC,CAACJ,IAAI,CAAEK,MAAM,IAAI;gBACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;cAE1B,CAAC,CAAC;aACH,MAAM;cACLtE,IAAI,CAACqD,IAAI,CAAC;gBACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,OAAO,CAAC;gBAC9CyC,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAAC,kBAAkB,CAAC;gBACxDM,IAAI,EAAE,OAAO;gBACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;gBACvD0C,kBAAkB,EAAE;eACrB,CAAC;;UAEN,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;KACD,CAAC;EACJ;EAEAG,UAAU,CAAC3B,UAAU,EAAEC,MAAM;IAC3B,IAAI,CAACR,cAAc,CAAC,IAAI,CAACmC,UAAU,CAAClC,QAAQ,CAAC,CAAC0B,IAAI,CAAEC,GAAQ,IAAI;MAC9DA,GAAG,GAAGA,GAAG,CAACQ,IAAI;MACd,IAAIR,GAAG,EAAE;QACP;QACAA,GAAG,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAChB,IAAID,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAE;YACnB,OAAO,CAAC,CAAC;;UAEX,IAAIF,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAE;YACnB,OAAO,CAAC;;UAEV,OAAO,CAAC;QACV,CAAC,CAAC;QAEF,IAAIC,OAAO,GAAG,EAAE;QAChBb,GAAG,CAACc,OAAO,CAAEC,IAAI,IAAI;UACnBF,OAAO,IAAI,kBAAkBE,IAAI,CAACvC,EAAE,KAAKuC,IAAI,CAACH,IAAI,WAAW;QAC/D,CAAC,CAAC;QACF;QACA7E,IAAI,CAACqD,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,uBAAuB,CAAC;UAC9D6B,IAAI,EAAE;UACNuB,OAAO;kBACC;UACRtB,gBAAgB,EAAE,IAAI;UACtBC,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,QAAQ,CAAC;UAC3DgC,gBAAgB,EAAE,IAAI,CAAC1C,iBAAiB,CAACU,OAAO,CAAC,QAAQ,CAAC;UAC1DiC,cAAc,EAAE,IAAI;UACpBC,mBAAmB,EAAE,IAAI;UACzBC,UAAU,EAAE,MAAK;YACf,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;cAC7B,IAAIb,OAAO,GACT+B,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,CACjCC,KAAK;cACR,IAAIrC,MAAM,GAAG,IAAIC,QAAQ,EAAE;cAC3BD,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,MAAM,CAAC;cAC/BC,MAAM,CAACE,MAAM,CACX,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,aAAa,EACvCG,UAAU,CAACK,QAAQ,CACpB;cACDH,MAAM,CAACE,MAAM,CAAC,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,YAAY,EAAES,OAAO,CAAC;cAE9D,IAAIN,UAAU,CAACO,KAAK,EAAE;gBACpBL,MAAM,CAACE,MAAM,CACX,OAAO,GAAGJ,UAAU,CAACH,EAAE,GAAG,UAAU,EACpCG,UAAU,CAACO,KAAK,CACjB;;cAGH,IAAI,CAACtC,aAAa,CAACuE,iBAAiB,CAACtC,MAAM,CAAC,CAACuC,SAAS,CACnDpB,GAAQ,IAAI;gBACX,IAAI,CAAC7B,eAAe,EAAE;gBACtB,IAAI6B,GAAG,EAAE;kBACPjE,IAAI,CAACqD,IAAI,CAAC;oBACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,SAAS,CAAC;oBAChD6B,IAAI,EACF;;;;+CAIuB,GACvB,IAAI,CAACvC,iBAAiB,CAACU,OAAO,CAC5B,2BAA2B,CAC5B,GACD;;;uBAGD;oBACD+B,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;oBACvD0C,kBAAkB,EAAE;mBACrB,CAAC,CAACJ,IAAI,CAAEK,MAAM,IAAI;oBACjB,IAAI,CAACH,eAAe,EAAE;kBACxB,CAAC,CAAC;iBACH,MAAM;kBACLlE,IAAI,CAACqD,IAAI,CAAC;oBACRC,KAAK,EAAE,OAAO;oBACda,IAAI,EAAE,kBAAkB;oBACxBnC,IAAI,EAAE,OAAO;oBACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;oBACvD0C,kBAAkB,EAAE;mBACrB,CAAC;;cAEN,CAAC,EACAkB,KAAK,IAAI;gBACRC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,KAAK,CAAC;gBAC3BtF,IAAI,CAACqD,IAAI,CAAC;kBACRC,KAAK,EAAE,OAAO;kBACda,IAAI,EAAE,8BAA8B;kBACpCnC,IAAI,EAAE,OAAO;kBACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;kBACvD0C,kBAAkB,EAAE;iBACrB,CAAC;cACJ,CAAC,CACF;YACH,CAAC,CAAC;UACJ;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAqB,YAAY;IACV,IAAI,IAAI,CAACC,aAAa,IAAI,KAAK,EAAE;MAC/B1F,IAAI,CAACqD,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,OAAO,CAAC;QAC9CyC,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAClC,8CAA8C,CAC/C;QACDM,IAAI,EAAE,OAAO;QACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;QACvD0C,kBAAkB,EAAE;OACrB,CAAC;MAEF;;IAEF,IAAI,CAACuB,gBAAgB,EAAE;EACzB;EAEAvD,eAAe;IACb,IAAI,CAAC,IAAI,CAACoC,UAAU,CAAClC,QAAQ,EAAE;IAC/B,IAAI,CAACD,cAAc,CAAC,IAAI,CAACmC,UAAU,CAAClC,QAAQ,CAAC,CAAC0B,IAAI,CAAEC,GAAQ,IAAI;MAC9DA,GAAG,GAAGA,GAAG,CAACQ,IAAI;MACd,IAAIR,GAAG,EAAE;QACP,IAAI,CAAC2B,aAAa,GAAG3B,GAAG;;IAE5B,CAAC,CAAC;EACJ;EAEA0B,gBAAgB;IACd,IAAI,IAAI,CAACC,aAAa,CAACC,MAAM,IAAI,CAAC,EAAE;MAClC7F,IAAI,CAACqD,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,gBAAgB,CAAC;QACvDM,IAAI,EAAE,MAAM;QACZyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;QACvD0C,kBAAkB,EAAE;OACrB,CAAC;MACF;;IAGF,MAAM0B,QAAQ,GAAG,IAAI,CAAC5E,aAAa,CAAC6E,IAAI,CAAC9F,0BAA0B,EAAE;MACnE+F,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,MAAMS,QAAQ,GAAG,IAAI,CAACjF,eAAe,CAACkF,SAAS,CAAC,IAAI,CAACV,aAAa,EAAE,MAAM,CAAC;MAC3EE,QAAQ,CAACS,iBAAiB,CAACC,KAAK,GAAGH,QAAQ;MAC3CP,QAAQ,CAACS,iBAAiB,CAACrE,UAAU,GAAG,IAAI,CAACA,UAAU;MAEvD;MACA,IAAIuE,aAAa,GAAG,EAAE;MACtB,IAAI,CAACC,YAAY,CAAC3B,OAAO,CAAE5B,KAAK,IAAI;QAClCsD,aAAa,CAACE,IAAI,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC;MACpC,CAAC,CAAC;MAEF2C,QAAQ,CAACS,iBAAiB,CAACE,aAAa,GAAGA,aAAa;MACxDX,QAAQ,CAACzB,MAAM,CAACL,IAAI,CAAEK,MAAM,IAAI;QAC9B,IAAIA,MAAM,EAAE;UACV,IAAIuC,UAAU,GACZ,IAAI,CAAC1E,UAAU,IAAI,QAAQ,GAAGmC,MAAM,CAACuC,UAAU,GAAG,IAAI;UACxD,IAAIC,aAAa,GAAQxC,MAAM,CAACwC,aAAa;UAC7CA,aAAa,GAAGA,aAAa,CAACC,IAAI,CAAC,GAAG,CAAC;UACvC,IAAIhE,MAAM,GAAG,IAAIC,QAAQ,EAAE;UAC3BD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACb,KAAK,CAACM,EAAE,CAAC;UACxC,IAAI,IAAI,CAACP,UAAU,IAAI,QAAQ,EAAE;YAC/BY,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE4D,UAAU,CAAC;;UAEpC9D,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE6D,aAAa,CAAC;UAErC,IAAI,CAAChG,aAAa,CACfkG,kBAAkB,CAACjE,MAAM,CAAC,CAC1BuC,SAAS,CAAEpB,GAAQ,IAAI;YACtB,IAAIA,GAAG,EAAE;cACP,IAAI,CAAC7B,eAAe,EAAE;cACtBpC,IAAI,CAACqD,IAAI,CAAC;gBACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,SAAS,CAAC;gBAChD6B,IAAI,EACF;;;6CAGyB,GACzB,IAAI,CAACvC,iBAAiB,CAACU,OAAO,CAAC,0BAA0B,CAAC,GAC1D;;yBAEK;gBACP+B,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;gBACvD0C,kBAAkB,EAAE;eACrB,CAAC,CAACJ,IAAI,CAAEK,MAAM,IAAI;gBACjB,IAAI,CAACH,eAAe,EAAE;cACxB,CAAC,CAAC;aACH,MAAM;cACLlE,IAAI,CAACqD,IAAI,CAAC;gBACRC,KAAK,EAAE,OAAO;gBACda,IAAI,EAAE,gBAAgB;gBACtBnC,IAAI,EAAE,OAAO;gBACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;gBACvD0C,kBAAkB,EAAE;eACrB,CAAC;;UAEN,CAAC,CAAC;;MAER,CAAC,CAAC;;EAEN;EAEA4C,WAAW,CAACC,WAAW;IACrB1B,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,IAAIgB,KAAK,GAAQ,EAAE;IACnBS,WAAW,CAAClC,OAAO,CAAE5B,KAAK,IAAI;MAC5BqD,KAAK,CAACG,IAAI,CAACxD,KAAK,CAACD,OAAO,CAAC;IAC3B,CAAC,CAAC;IAEF;IAEAlD,IAAI,CAACqD,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,eAAe,CAAC;MACtD6B,IAAI,EACF;;;iCAGyB,GACzB,IAAI,CAACvC,iBAAiB,CAACU,OAAO,CAC5B,4CAA4C,CAC7C,GACD;;;;OAID;MACD8B,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,KAAK,CAAC;MACxDgC,gBAAgB,EAAE,IAAI,CAAC1C,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;MACtDiC,cAAc,EAAE;KACjB,CAAC,CAACK,IAAI,CAAEK,MAAM,IAAI;MACjB,IAAIpB,QAAQ,GAAGgE,WAAW,CAAC,CAAC,CAAC,CAAChE,QAAQ;MACtC,IAAIE,KAAK,GAAG8D,WAAW,CAAC,CAAC,CAAC,CAAC9D,KAAK;MAChCqD,KAAK,GAAGA,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;MAEvB,IAAIhE,MAAM,GAAG,IAAIC,QAAQ,EAAE;MAC3B;MACA;MACA;MACAD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEC,QAAQ,CAAC;MACnCH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEG,KAAK,CAAC;MAC7BL,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEwD,KAAK,CAAC;MAC7B,IAAInC,MAAM,CAACc,KAAK,EAAE;QAChB,IAAI,CAACtE,aAAa,CAACqG,kBAAkB,CAACpE,MAAM,CAAC,CAACuC,SAAS,CAAEpB,GAAQ,IAAI;UACnE,IAAIA,GAAG,EAAE;YACP,IAAI,CAACnD,eAAe,CAACyB,IAAI,EAAE;YAC3B,IAAI,CAAC2B,eAAe,EAAE;YACtB,IAAI,CAAC9B,eAAe,EAAE;YAEtBpC,IAAI,CAACqD,IAAI,CAAC;cACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,SAAS,CAAC;cAChDyC,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAClC,4BAA4B,CAC7B;cACDM,IAAI,EAAE,SAAS;cACfyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;cACvD0C,kBAAkB,EAAE;aACrB,CAAC;WACH,MAAM;YACLpE,IAAI,CAACqD,IAAI,CAAC;cACRC,KAAK,EAAE,OAAO;cACda,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAAC,mBAAmB,CAAC;cACzDM,IAAI,EAAE,OAAO;cACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;cACvD0C,kBAAkB,EAAE;aACrB,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA+C,aAAa,CAACF,WAAW;IACvB,IAAIT,KAAK,GAAQ,EAAE;IACnBS,WAAW,CAAClC,OAAO,CAAE5B,KAAK,IAAI;MAC5BqD,KAAK,CAACG,IAAI,CAACxD,KAAK,CAACD,OAAO,CAAC;IAC3B,CAAC,CAAC;IAEFlD,IAAI,CAACqD,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,eAAe,CAAC;MACtD6B,IAAI,EACF;;;;iCAIyB,GACzB,IAAI,CAACvC,iBAAiB,CAACU,OAAO,CAC5B,iDAAiD,CAClD,GACD;;aAEK;MACP8B,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,KAAK,CAAC;MACxDgC,gBAAgB,EAAE,IAAI,CAAC1C,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;MACtDiC,cAAc,EAAE;KACjB,CAAC,CAACK,IAAI,CAAEK,MAAM,IAAI;MACjB,IAAIpB,QAAQ,GAAGgE,WAAW,CAAC,CAAC,CAAC,CAAChE,QAAQ;MACtCuD,KAAK,GAAGA,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;MAEvB,IAAIhE,MAAM,GAAG,IAAIC,QAAQ,EAAE;MAC3B;MACA;MACAD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEC,QAAQ,CAAC;MACnCH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEwD,KAAK,CAAC;MAE7B,IAAInC,MAAM,CAACc,KAAK,EAAE;QAChB,IAAI,CAACtE,aAAa,CAACqG,kBAAkB,CAACpE,MAAM,CAAC,CAACuC,SAAS,CAAEpB,GAAQ,IAAI;UACnE,IAAIA,GAAG,EAAE;YACPjE,IAAI,CAACqD,IAAI,CAAC;cACRC,KAAK,EAAE,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,SAAS,CAAC;cAChDyC,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAAC,2BAA2B,CAAC;cACjEM,IAAI,EAAE,SAAS;cACfyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;cACvD0C,kBAAkB,EAAE;aACrB,CAAC,CAACJ,IAAI,CAAEK,MAAM,IAAI;cACjB;cACA,IAAI,CAACvD,eAAe,CAACyB,IAAI,EAAE;cAC3B6E,UAAU,CAAC,MAAK;gBACd,IAAI,CAAChF,eAAe,EAAE;gBACtB,IAAI,CAAC8B,eAAe,EAAE;cACxB,CAAC,EAAE,GAAG,CAAC;cACP;YACF,CAAC,CAAC;WACH,MAAM;YACLlE,IAAI,CAACqD,IAAI,CAAC;cACRC,KAAK,EAAE,OAAO;cACda,IAAI,EAAE,mBAAmB;cACzBnC,IAAI,EAAE,OAAO;cACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;cACvD0C,kBAAkB,EAAE;aACrB,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EACOF,eAAe;IACpB,IAAI,CAACrD,aAAa,CAACqD,eAAe,CAAC,IAAI,CAAC/B,KAAK,CAACM,EAAE,CAAC,CAAC4C,SAAS,CAAEpB,GAAQ,IAAI;MACvE,IAAI,CAACyC,YAAY,GAAGzC,GAAG,CAACQ,IAAI;MAC5B,IAAI,CAAC4C,YAAY,CAACC,IAAI,CAAC,IAAI,CAACZ,YAAY,CAAC;IAC3C,CAAC,CAAC;IACF,IAAI,CAAC5F,eAAe,CAACyG,OAAO,EAAE;EAChC;EAoBAC,uBAAuB,CAAChB,KAAK;IAC3B,OAAO,IAAI1C,OAAO,CAAC,CAACC,OAAO,EAAE0D,MAAM,KAAI;MACrC;MACA,IAAI,CAAC7G,YAAY,CACd8G,kBAAkB,CAAC,IAAI,CAACvF,KAAK,CAACM,EAAE,CAAC,CACjC4C,SAAS,CAAEpB,GAAQ,IAAI;QACtB,IAAI0D,cAAc,GAAQ1D,GAAG,CAACQ,IAAI;QAClCkD,cAAc,CAACjD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClC,EAAE,GAAGmC,CAAC,CAACnC,EAAE,CAAC;QAC1CkF,cAAc,GAAGA,cAAc,CAACC,GAAG,CAAE5C,IAAI,IAAKA,IAAI,CAACvC,EAAE,CAAC;QACtD,IAAIoF,mBAAmB,GAAGF,cAAc,CAACb,IAAI,CAAC,GAAG,CAAC;QAClD,IAAIgB,UAAU,GAAGtB,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;QAChC,IAAIgB,UAAU,IAAID,mBAAmB,EAAE;UACrCtC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACrCzB,OAAO,CAAC,KAAK,CAAC;SACf,MAAM;UACL;UACA,IAAIgE,kBAAkB,GAAGvB,KAAK,CAACwB,MAAM,CAClChD,IAAI,IAAK,CAAC2C,cAAc,CAACM,QAAQ,CAACjD,IAAI,CAAC,CACzC;UACDjB,OAAO,CAACgE,kBAAkB,CAAC;;MAE/B,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAG,MAAM,CAACC,WAAW;IAChBA,WAAW,CAACzD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACK,IAAI,CAACH,IAAI,CAACuD,aAAa,CAACxD,CAAC,CAACI,IAAI,CAACH,IAAI,CAAC,CAAC;IAClE,OAAOsD,WAAW;EACpB;EAEAE,WAAW;IACT,IAAI,CAACxH,aAAa,CACfyH,mBAAmB,CAAC,IAAI,CAAC9D,UAAU,CAAC/B,EAAE,CAAC,CACvC4C,SAAS,CAAEpB,GAAQ,IAAI;MACtB,IAAIsE,MAAM,GAAGtE,GAAG,CAACQ,IAAI;MACrB8D,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAE7F,KAAK,IAAKA,KAAK,CAACb,IAAI,IAAI,QAAQ,CAAC;MAEzDtB,IAAI,CAACqD,IAAI,CAAC;QACRC,KAAK,EACH,IAAI,CAACtC,iBAAiB,CAACU,OAAO,CAAC,kCAAkC,CAAC,GAClE,GAAG,GACH,IAAI,CAAC8C,UAAU,CAACK,IAAI,GACpB,GAAG,GACH0D,MAAM,CAAC,CAAC,CAAC,CAAC1D,IAAI,GACd,IAAI;QACNrB,gBAAgB,EAAE,IAAI;QACtBE,gBAAgB,EAAE,IAAI,CAAC1C,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;QACtD+B,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,KAAK;OACxD,CAAC,CAACsC,IAAI,CAAEK,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;UACtB,IAAIrB,QAAQ,GAAGsF,MAAM,CAAC,CAAC,CAAC,CAAC9F,EAAE;UAC3B;UACA,IAAI,CAAC7B,YAAY,CACd8G,kBAAkB,CAACzE,QAAQ,CAAC,CAC5BoC,SAAS,CAAEpB,GAAQ,IAAI;YACtB,IAAIuC,KAAK,GAAQvC,GAAG,CAACQ,IAAI;YACzB+B,KAAK,CAAC9B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClC,EAAE,GAAGmC,CAAC,CAACnC,EAAE,CAAC;YACjC+D,KAAK,GAAGA,KAAK,CAACoB,GAAG,CAAE5C,IAAI,IAAKA,IAAI,CAACvC,EAAE,CAAC;YAEpC;YACA,IAAI,CAAC+E,uBAAuB,CAAChB,KAAK,CAAC,CAACxC,IAAI,CAAEC,GAAG,IAAI;cAC/C,IAAIA,GAAG,EAAE;gBACPuC,KAAK,GAAGvC,GAAG;gBACX;gBACA,IAAInB,MAAM,GAAG,IAAIC,QAAQ,EAAE;gBAC3ByD,KAAK,GAAGA,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC;gBACvBhE,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACb,KAAK,CAACM,EAAE,CAAC;gBACxCK,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEwD,KAAK,CAAC;gBAC7B,IAAI,CAAC3F,aAAa,CACfkG,kBAAkB,CAACjE,MAAM,CAAC,CAC1BuC,SAAS,CAAEpB,GAAQ,IAAI;kBACtB,IAAIA,GAAG,EAAE;oBACPjE,IAAI,CAACqD,IAAI,CAAC;sBACRC,KAAK,EAAE,SAAS;sBAChBa,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAAC,6BAA6B,CAAC;sBACnEM,IAAI,EAAE,SAAS;sBACfyB,iBAAiB,EACf,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;sBACtC0C,kBAAkB,EAAE;qBACrB,CAAC,CAACJ,IAAI,CAAEK,MAAM,IAAI;sBACjB,IAAI,CAACjC,eAAe,EAAE;sBAEtB,IAAI,CAACtB,eAAe,CAACyB,IAAI,EAAE;sBAC3B6E,UAAU,CAAC,MAAK;wBACd,IAAI,CAACvG,aAAa,CACfqD,eAAe,CAAC,IAAI,CAAC/B,KAAK,CAACM,EAAE,CAAC,CAC9B4C,SAAS,CAAEpB,GAAQ,IAAI;0BACtB,IAAI,CAACyC,YAAY,GAAGzC,GAAG,CAACQ,IAAI;0BAE5B,IAAI,CAAC3D,eAAe,CAACyG,OAAO,EAAE;wBAChC,CAAC,CAAC;sBACN,CAAC,EAAE,GAAG,CAAC;oBACT,CAAC,CAAC;mBACH,MAAM;oBACLvH,IAAI,CAACqD,IAAI,CAAC;sBACRC,KAAK,EAAE,OAAO;sBACda,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAAC,oBAAoB,CAAC;sBAC1DM,IAAI,EAAE,OAAO;sBACbyB,iBAAiB,EACf,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;sBACtC0C,kBAAkB,EAAE;qBACrB,CAAC;;gBAEN,CAAC,CAAC;eACL,MAAM;gBACLpE,IAAI,CAACqD,IAAI,CAAC;kBACRC,KAAK,EAAE,OAAO;kBACda,IAAI,EAAE,IAAI,CAACnD,iBAAiB,CAACU,OAAO,CAAC,2BAA2B,CAAC;kBACjEM,IAAI,EAAE,OAAO;kBACbyB,iBAAiB,EAAE,IAAI,CAACzC,iBAAiB,CAACU,OAAO,CAAC,IAAI,CAAC;kBACvD0C,kBAAkB,EAAE;iBACrB,CAAC;;YAEN,CAAC,CAAC;UACJ,CAAC,CAAC;;MAER,CAAC,CAAC;IACJ,CAAC,CAAC;EACN;EAGAoE,aAAa,CAACC,OAAO,EAAEC,UAAU;IAC/B,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAACA,UAAU,CAACvF,KAAK,EAAE;MACzB,IAAI,CAACwF,KAAK,GAAG;QACX/B,UAAU,EAAE,IAAI,CAAC8B,UAAU,CAACvF;OAC7B;;IAEH,IAAI,CAACjC,aAAa,CACf6E,IAAI,CAAC0C,OAAO,EAAE;MACbG,cAAc,EAAE;KACjB,CAAC,CACDvE,MAAM,CAACL,IAAI,CACTK,MAAM,IAAI;MACT,IAAI,CAACqE,UAAU,GAAG,IAAI;IACxB,CAAC,EACAG,MAAM,IAAI;MACT,IAAI,CAACH,UAAU,GAAG,IAAI;IACxB,CAAC,CACF;EACL;EAEAI,MAAM,CAACH,KAAK;IACV,IAAI,CAACD,UAAU;IAEf,IAAI,IAAI,CAACK,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACN,UAAU,EAAE;IAC3C,IAAI5F,MAAM,GAAG,IAAIC,QAAQ,EAAE;IAC3BD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC0F,UAAU,CAACzF,QAAQ,CAAC;IACnDH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC0F,UAAU,CAACvF,KAAK,CAAC8F,QAAQ,EAAE,CAAC;IAC5DnG,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE2F,KAAK,CAAC/B,UAAU,CAAC;IACxC9D,MAAM,CAACE,MAAM,CAAC,qBAAqB,EAAE,MAAM,CAAC;IAE5C,IAAI,CAAClC,eAAe,CAACyB,IAAI,EAAE;IAC3B,IAAI,CAAC2G,SAAS,CAACpG,MAAM,CAAC,CAACkB,IAAI,CAAEC,GAAG,IAAI;MAClC,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAAChD,aAAa,CAACiI,UAAU,EAAE;IAC/B,CAAC,EACA7D,KAAK,IAAI;MACR,IAAIA,KAAK,CAAC8D,MAAM,EAAC;QACf,IAAI,CAACL,IAAI,CAACM,QAAQ,CAAC,YAAY,CAAC,CAACC,SAAS,CAAC;UACzCC,WAAW,EAAEjE,KAAK,CAACkE;SACpB,CAAC;;IAEN,CAAC,CACF;EACH;EAEQN,SAAS,CAACpG,MAAM;IAErB,OAAO,IAAI,CAACjC,aAAa,CAACqI,SAAS,CAACpG,MAAM,CAAC,CAACJ,SAAS,EAAE,CAACsB,IAAI,CAAEC,GAAQ,IAAI;MACzE,IAAIA,GAAG,EAAE;QACP,OAAOA,GAAG;;IAEd,CAAC,CACF;EACD;EAAC;qBA3pBU3D,mBAAmB;EAAA;EAAA;UAAnBA,mBAAmB;IAAAmJ;IAAAC;MAAAvH;MAAAqC;MAAAkC;MAAAhB;IAAA;IAAAiE;MAAAtC;IAAA;IAAAuC,iCAFnB,CAACzJ,eAAe,EAAEC,cAAc,CAAC;IAAAyJ;IAAAC;IAAAC;IAAAC;MAAA;QC7B9C3J,8BAAkB;QAESA,YAA+B;;QAAAA,iBAAK;QAC3DA,sBAAW;QACXA,0EAkBS;QAETA,sEAuBM;QAGRA,iBAAM;QACNA,8BAAuB;QAInBA,sEA+DM;QAERA,iBAAM;QAIVA,yHAqBc;;;QA/IaA,eAA+B;QAA/BA,yDAA+B;QAETA,eAG3C;QAH2CA,yIAG3C;QAiBuCA,eAA4F;QAA5FA,yHAA4F;QAgCzGA,eAAiB;QAAjBA,0CAAiB", "names": ["EventEmitter", "AppConfig", "<PERSON><PERSON>", "ModalAddGroupTeamComponent", "FormGroup", "CorePipesModule", "SortByNamePipe", "i0", "StageTeamsComponent", "constructor", "_trans", "_route", "_router", "_tournamentService", "_teamService", "_stageService", "_loadingService", "_toastrService", "_translateService", "_commonsService", "_modalService", "_http", "_sortByNamePipe", "key", "type", "props", "label", "placeholder", "instant", "required", "max<PERSON><PERSON><PERSON>", "validation", "messages", "buttons", "icon", "ngOnInit", "stage_type", "stage", "getTeamsByGroup", "getTeamByGroup", "group_id", "show", "getTeamNotInStage", "id", "to<PERSON>romise", "removeTeamFromStage", "stage_team", "action", "params", "FormData", "append", "stage_id", "team_id", "group", "removeTeam", "fire", "title", "html", "showCancelButton", "confirmButtonText", "cancelButtonText", "reverseButtons", "showLoaderOnConfirm", "preConfirm", "Promise", "resolve", "then", "res", "getTeamsInStage", "text", "confirmButtonColor", "result", "isConfirmed", "changeTeam", "tournament", "data", "sort", "a", "b", "name", "options", "for<PERSON>ach", "team", "document", "getElementById", "value", "changeTeamInStage", "subscribe", "error", "console", "log", "addGroupTeam", "allowEditTeam", "openModalAddTeam", "team_by_group", "length", "modalRef", "open", "size", "backdrop", "keyboard", "centered", "windowClass", "sortTeam", "transform", "componentInstance", "teams", "current_group", "group_stages", "push", "group_name", "selected_team", "join", "createTeamMultiple", "removeGroup", "group_stage", "removeTeamMultiple", "removeAllTeam", "setTimeout", "onDataChange", "emit", "dismiss", "checkTeamAlreadyInStage", "reject", "getTeamListInStage", "teams_in_stage", "map", "list_teams_in_stage", "list_teams", "teams_not_in_stage", "filter", "includes", "sortBy", "stage_teams", "localeCompare", "importTeams", "getDataByTournament", "stages", "openModalEdit", "content", "groupStage", "model", "ariaLabelledBy", "reason", "onEdit", "form", "invalid", "toString", "editGroup", "dismissAll", "errors", "controls", "setErrors", "serverError", "message", "selectors", "inputs", "outputs", "features", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\stage-teams.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\stage-teams.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { environment } from 'environments/environment';\r\nimport { Subject } from 'rxjs';\r\nimport { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { ToastrService } from 'ngx-toastr';\r\n// sweet alert\r\nimport Swal from 'sweetalert2';\r\nimport { ModalAddGroupTeamComponent } from './modal-add-group-team/modal-add-group-team.component';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { CorePipesModule } from '@core/pipes/pipes.module';\r\nimport { SortByNamePipe } from '@core/pipes/sort-by-name.pipe';\r\n\r\n@Component({\r\n  selector: 'stage-teams',\r\n  templateUrl: './stage-teams.component.html',\r\n  styleUrls: ['./stage-teams.component.scss'],\r\n  providers: [CorePipesModule, SortByNamePipe]\r\n})\r\nexport class StageTeamsComponent implements OnInit {\r\n  @Input() stage: any;\r\n  @Input() tournament: any;\r\n  @Input() group_stages: any = [];\r\n  @Input() allowEditTeam = true;\r\n  @Output() onDataChange: EventEmitter<any> = new EventEmitter<any>();\r\n  stage_type: any = null;\r\n  team_by_group: any = [];\r\n  team_id = null;\r\n  groups: any = [];\r\n  AppConfig = AppConfig;\r\n  form = new FormGroup({});\r\n  model = {};\r\n  fields: FormlyFieldConfig[] = [\r\n    {\r\n      key: 'group_name',\r\n      type: 'input',\r\n      props: {\r\n        label: '',\r\n        placeholder: this._trans.instant('Group Name'),\r\n        required: true,\r\n        maxLength: 20,\r\n      },\r\n      // validate message\r\n      validation: {\r\n        messages: {\r\n          required: this._trans.instant('Group Name is required'),\r\n        },\r\n      },\r\n\r\n    },\r\n  ];\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _tournamentService: TournamentService,\r\n    public _teamService: TeamService,\r\n    public _stageService: StageService,\r\n    public _loadingService: LoadingService,\r\n    public _toastrService: ToastrService,\r\n    public _translateService: TranslateService,\r\n    public _commonsService: CommonsService,\r\n    public _modalService: NgbModal,\r\n    public _http: HttpClient,\r\n    private _sortByNamePipe: SortByNamePipe\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.stage_type = this.stage.type;\r\n    this.getTeamsByGroup();\r\n  }\r\n\r\n  getTeamByGroup(group_id) {\r\n    this._loadingService.show();\r\n    return this._teamService\r\n      .getTeamNotInStage(this.stage.id, group_id)\r\n      .toPromise();\r\n  }\r\n\r\n  removeTeamFromStage(stage_team, action) {\r\n    let params = new FormData();\r\n    params.append('action', action);\r\n    params.append('data[' + stage_team.id + '][stage_id]', stage_team.stage_id);\r\n    params.append('data[' + stage_team.id + '][team_id]', stage_team.team_id);\r\n    params.append('data[' + stage_team.id + '][group]', stage_team.group);\r\n    return this._stageService.removeTeamFromStage(params).toPromise();\r\n  }\r\n\r\n  removeTeam(stage_team, action) {\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        `\r\n        <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n\r\n        <p class=\"text-center\"> ` +\r\n        this._translateService.instant(\r\n          'You will not be able to recover this team'\r\n        ) +\r\n        `      \r\n        </p>\r\n      </div>\r\n        `,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true,\r\n      showLoaderOnConfirm: true,\r\n      preConfirm: () => {\r\n        return new Promise((resolve) => {\r\n          this.removeTeamFromStage(stage_team, action).then((res: any) => {\r\n            if (res) {\r\n              this.getTeamsInStage();\r\n              Swal.fire({\r\n                title: this._translateService.instant('Success'),\r\n                text: this._translateService.instant(\r\n                  'Team removed successfully'\r\n                ),\r\n                icon: 'success',\r\n                confirmButtonText: this._translateService.instant('OK'),\r\n                confirmButtonColor: '#3085d6',\r\n              }).then((result) => {\r\n                if (result.isConfirmed) {\r\n                }\r\n              });\r\n            } else {\r\n              Swal.fire({\r\n                title: this._translateService.instant('Error'),\r\n                text: this._translateService.instant('Team not removed'),\r\n                icon: 'error',\r\n                confirmButtonText: this._translateService.instant('OK'),\r\n                confirmButtonColor: '#3085d6',\r\n              });\r\n            }\r\n          });\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  changeTeam(stage_team, action) {\r\n    this.getTeamByGroup(this.tournament.group_id).then((res: any) => {\r\n      res = res.data;\r\n      if (res) {\r\n        // sort by name\r\n        res.sort((a, b) => {\r\n          if (a.name < b.name) {\r\n            return -1;\r\n          }\r\n          if (a.name > b.name) {\r\n            return 1;\r\n          }\r\n          return 0;\r\n        });\r\n\r\n        let options = '';\r\n        res.forEach((team) => {\r\n          options += `<option value=\"${team.id}\">${team.name}</option>`;\r\n        });\r\n        // pop up to select team\r\n        Swal.fire({\r\n          title: this._translateService.instant('Select team to change'),\r\n          html: `<select class=\"form-control\" id=\"team_id\">\r\n        ${options}\r\n        </select>`,\r\n          showCancelButton: true,\r\n          confirmButtonText: this._translateService.instant('Change'),\r\n          cancelButtonText: this._translateService.instant('Cancel'),\r\n          reverseButtons: true,\r\n          showLoaderOnConfirm: true,\r\n          preConfirm: () => {\r\n            return new Promise((resolve) => {\r\n              let team_id = (<HTMLInputElement>(\r\n                document.getElementById('team_id')\r\n              )).value;\r\n              let params = new FormData();\r\n              params.append('action', action);\r\n              params.append(\r\n                'data[' + stage_team.id + '][stage_id]',\r\n                stage_team.stage_id\r\n              );\r\n              params.append('data[' + stage_team.id + '][team_id]', team_id);\r\n\r\n              if (stage_team.group) {\r\n                params.append(\r\n                  'data[' + stage_team.id + '][group]',\r\n                  stage_team.group\r\n                );\r\n              }\r\n\r\n              this._stageService.changeTeamInStage(params).subscribe(\r\n                (res: any) => {\r\n                  this.getTeamsByGroup();\r\n                  if (res) {\r\n                    Swal.fire({\r\n                      title: this._translateService.instant('Success'),\r\n                      html:\r\n                        `\r\n                      <div class=\"text-center\">\r\n                      <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n              \r\n                      <p class=\"text-center\"> ` +\r\n                        this._translateService.instant(\r\n                          'Team changed successfully'\r\n                        ) +\r\n                        `      \r\n                      </p>\r\n                    </div>\r\n                      `,\r\n                      confirmButtonText: this._translateService.instant('OK'),\r\n                      confirmButtonColor: '#3085d6',\r\n                    }).then((result) => {\r\n                      this.getTeamsInStage();\r\n                    });\r\n                  } else {\r\n                    Swal.fire({\r\n                      title: 'Error',\r\n                      text: 'Team not changed',\r\n                      icon: 'error',\r\n                      confirmButtonText: this._translateService.instant('OK'),\r\n                      confirmButtonColor: '#3085d6',\r\n                    });\r\n                  }\r\n                },\r\n                (error) => {\r\n                  console.log('error', error);\r\n                  Swal.fire({\r\n                    title: 'Error',\r\n                    text: 'No team available for change',\r\n                    icon: 'error',\r\n                    confirmButtonText: this._translateService.instant('OK'),\r\n                    confirmButtonColor: '#3085d6',\r\n                  });\r\n                }\r\n              );\r\n            });\r\n          },\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  addGroupTeam() {\r\n    if (this.allowEditTeam == false) {\r\n      Swal.fire({\r\n        title: this._translateService.instant('Error'),\r\n        text: this._translateService.instant(\r\n          'You must remove all schedule before add team'\r\n        ),\r\n        icon: 'error',\r\n        confirmButtonText: this._translateService.instant('OK'),\r\n        confirmButtonColor: '#3085d6',\r\n      });\r\n\r\n      return;\r\n    }\r\n    this.openModalAddTeam();\r\n  }\r\n\r\n  getTeamsByGroup() {\r\n    if (!this.tournament.group_id) return;\r\n    this.getTeamByGroup(this.tournament.group_id).then((res: any) => {\r\n      res = res.data;\r\n      if (res) {\r\n        this.team_by_group = res;\r\n      }\r\n    });\r\n  }\r\n\r\n  openModalAddTeam() {\r\n    if (this.team_by_group.length == 0) {\r\n      Swal.fire({\r\n        title: this._translateService.instant('No team to add'),\r\n        icon: 'info',\r\n        confirmButtonText: this._translateService.instant('OK'),\r\n        confirmButtonColor: '#3085d6',\r\n      });\r\n      return;\r\n    }\r\n\r\n    const modalRef = this._modalService.open(ModalAddGroupTeamComponent, {\r\n      size: 'lg',\r\n      backdrop: 'static',\r\n      keyboard: false,\r\n      centered: true,\r\n      windowClass: 'modal-add-group-team',\r\n    });\r\n    if (this.team_by_group) {\r\n      const sortTeam = this._sortByNamePipe.transform(this.team_by_group, 'name');\r\n      modalRef.componentInstance.teams = sortTeam;\r\n      modalRef.componentInstance.stage_type = this.stage_type;\r\n\r\n      // get current group\r\n      let current_group = [];\r\n      this.group_stages.forEach((group) => {\r\n        current_group.push(group[0].group);\r\n      });\r\n\r\n      modalRef.componentInstance.current_group = current_group;\r\n      modalRef.result.then((result) => {\r\n        if (result) {\r\n          let group_name =\r\n            this.stage_type == 'Groups' ? result.group_name : null;\r\n          let selected_team: any = result.selected_team;\r\n          selected_team = selected_team.join(',');\r\n          let params = new FormData();\r\n          params.append('stage_id', this.stage.id);\r\n          if (this.stage_type == 'Groups') {\r\n            params.append('group', group_name);\r\n          }\r\n          params.append('teams', selected_team);\r\n\r\n          this._stageService\r\n            .createTeamMultiple(params)\r\n            .subscribe((res: any) => {\r\n              if (res) {\r\n                this.getTeamsByGroup();\r\n                Swal.fire({\r\n                  title: this._translateService.instant('Success'),\r\n                  html:\r\n                    `\r\n                  <div class=\"text-center\">\r\n                    <img src=\"assets/images/ai/done.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n                    <p class=\"text-center\"> ` +\r\n                    this._translateService.instant('Teams added successfully') +\r\n                    `      \r\n                    </p>\r\n                  </div>`,\r\n                  confirmButtonText: this._translateService.instant('OK'),\r\n                  confirmButtonColor: '#3085d6',\r\n                }).then((result) => {\r\n                  this.getTeamsInStage();\r\n                });\r\n              } else {\r\n                Swal.fire({\r\n                  title: 'Error',\r\n                  text: 'Team not added',\r\n                  icon: 'error',\r\n                  confirmButtonText: this._translateService.instant('OK'),\r\n                  confirmButtonColor: '#3085d6',\r\n                });\r\n              }\r\n            });\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  removeGroup(group_stage) {\r\n    console.log('removeGroup');\r\n    let teams: any = [];\r\n    group_stage.forEach((group) => {\r\n      teams.push(group.team_id);\r\n    });\r\n\r\n    // implode teams\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        ` \r\n      <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n        <p class=\"text-center\"> ` +\r\n        this._translateService.instant(\r\n          'You will not be able to recover this group'\r\n        ) +\r\n        `\r\n        </p>\r\n      </div>\r\n      \r\n      `,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true,\r\n    }).then((result) => {\r\n      let stage_id = group_stage[0].stage_id;\r\n      let group = group_stage[0].group;\r\n      teams = teams.join(',');\r\n\r\n      let params = new FormData();\r\n      // stage_id: 139\r\n      // group: A\r\n      // teams: 7, 8, 9\r\n      params.append('stage_id', stage_id);\r\n      params.append('group', group);\r\n      params.append('teams', teams);\r\n      if (result.value) {\r\n        this._stageService.removeTeamMultiple(params).subscribe((res: any) => {\r\n          if (res) {\r\n            this._loadingService.show();\r\n            this.getTeamsInStage();\r\n            this.getTeamsByGroup();\r\n\r\n            Swal.fire({\r\n              title: this._translateService.instant('Success'),\r\n              text: this._translateService.instant(\r\n                'Group removed successfully'\r\n              ),\r\n              icon: 'success',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          } else {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: this._translateService.instant('Group not removed'),\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  removeAllTeam(group_stage) {\r\n    let teams: any = [];\r\n    group_stage.forEach((group) => {\r\n      teams.push(group.team_id);\r\n    });\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Are you sure?'),\r\n      html:\r\n        `\r\n      <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/warning.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n\r\n        <p class=\"text-center\"> ` +\r\n        this._translateService.instant(\r\n          'You are about to remove all teams in this group'\r\n        ) +\r\n        `      \r\n        </p>\r\n      </div>`,\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Yes'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      reverseButtons: true,\r\n    }).then((result) => {\r\n      let stage_id = group_stage[0].stage_id;\r\n      teams = teams.join(',');\r\n\r\n      let params = new FormData();\r\n      // stage_id: 139\r\n      // teams: 7, 8, 9\r\n      params.append('stage_id', stage_id);\r\n      params.append('teams', teams);\r\n\r\n      if (result.value) {\r\n        this._stageService.removeTeamMultiple(params).subscribe((res: any) => {\r\n          if (res) {\r\n            Swal.fire({\r\n              title: this._translateService.instant('Success'),\r\n              text: this._translateService.instant('Team removed successfully'),\r\n              icon: 'success',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            }).then((result) => {\r\n              // if (result.isConfirmed) {\r\n              this._loadingService.show();\r\n              setTimeout(() => {\r\n                this.getTeamsByGroup();\r\n                this.getTeamsInStage();\r\n              }, 400);\r\n              // }\r\n            });\r\n          } else {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: 'Teams not removed',\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n              confirmButtonColor: '#3085d6',\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n  public getTeamsInStage() {\r\n    this._stageService.getTeamsInStage(this.stage.id).subscribe((res: any) => {\r\n      this.group_stages = res.data;\r\n      this.onDataChange.emit(this.group_stages);\r\n    });\r\n    this._loadingService.dismiss();\r\n  }\r\n\r\n  public rowActions: EZBtnActions[] = [\r\n    {\r\n      type: 'collection',\r\n      buttons: [\r\n        {\r\n          label: 'Change team',\r\n\r\n          icon: 'fa-regular fa-pen-to-square',\r\n        },\r\n        {\r\n          label: 'Delete',\r\n\r\n          icon: 'fa-regular fa-trash',\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  checkTeamAlreadyInStage(teams) {\r\n    return new Promise((resolve, reject) => {\r\n      // check if teams are already in stage\r\n      this._teamService\r\n        .getTeamListInStage(this.stage.id)\r\n        .subscribe((res: any) => {\r\n          let teams_in_stage: any = res.data;\r\n          teams_in_stage.sort((a, b) => a.id - b.id);\r\n          teams_in_stage = teams_in_stage.map((team) => team.id);\r\n          let list_teams_in_stage = teams_in_stage.join(',');\r\n          let list_teams = teams.join(',');\r\n          if (list_teams == list_teams_in_stage) {\r\n            console.log('teams already imported');\r\n            resolve(false);\r\n          } else {\r\n            // find the teams that are not in stage\r\n            let teams_not_in_stage = teams.filter(\r\n              (team) => !teams_in_stage.includes(team)\r\n            );\r\n            resolve(teams_not_in_stage);\r\n          }\r\n        });\r\n    });\r\n  }\r\n\r\n  sortBy(stage_teams) {\r\n    stage_teams.sort((a, b) => a.team.name.localeCompare(b.team.name));\r\n    return stage_teams;\r\n  }\r\n\r\n  importTeams() {\r\n    this._stageService\r\n      .getDataByTournament(this.tournament.id)\r\n      .subscribe((res: any) => {\r\n        let stages = res.data;\r\n        stages = stages.filter((stage) => stage.type == 'Groups');\r\n\r\n        Swal.fire({\r\n          title:\r\n            this._translateService.instant('Do you want to import teams from') +\r\n            ' ' +\r\n            this.tournament.name +\r\n            ' ' +\r\n            stages[0].name +\r\n            '? ',\r\n          showCancelButton: true,\r\n          cancelButtonText: this._translateService.instant('No'),\r\n          confirmButtonText: this._translateService.instant('Yes'),\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            let stage_id = stages[0].id;\r\n            // get teams from stages\r\n            this._teamService\r\n              .getTeamListInStage(stage_id)\r\n              .subscribe((res: any) => {\r\n                let teams: any = res.data;\r\n                teams.sort((a, b) => a.id - b.id);\r\n                teams = teams.map((team) => team.id);\r\n\r\n                // check if teams are already in stage\r\n                this.checkTeamAlreadyInStage(teams).then((res) => {\r\n                  if (res) {\r\n                    teams = res;\r\n                    // sort teams by name\r\n                    let params = new FormData();\r\n                    teams = teams.join(',');\r\n                    params.append('stage_id', this.stage.id);\r\n                    params.append('teams', teams);\r\n                    this._stageService\r\n                      .createTeamMultiple(params)\r\n                      .subscribe((res: any) => {\r\n                        if (res) {\r\n                          Swal.fire({\r\n                            title: 'Success',\r\n                            text: this._translateService.instant('Teams imported successfully'),\r\n                            icon: 'success',\r\n                            confirmButtonText:\r\n                              this._translateService.instant('OK'),\r\n                            confirmButtonColor: '#3085d6',\r\n                          }).then((result) => {\r\n                            this.getTeamsByGroup();\r\n\r\n                            this._loadingService.show();\r\n                            setTimeout(() => {\r\n                              this._stageService\r\n                                .getTeamsInStage(this.stage.id)\r\n                                .subscribe((res: any) => {\r\n                                  this.group_stages = res.data;\r\n\r\n                                  this._loadingService.dismiss();\r\n                                });\r\n                            }, 400);\r\n                          });\r\n                        } else {\r\n                          Swal.fire({\r\n                            title: 'Error',\r\n                            text: this._translateService.instant('Teams not imported'),\r\n                            icon: 'error',\r\n                            confirmButtonText:\r\n                              this._translateService.instant('OK'),\r\n                            confirmButtonColor: '#3085d6',\r\n                          });\r\n                        }\r\n                      });\r\n                  } else {\r\n                    Swal.fire({\r\n                      title: 'Error',\r\n                      text: this._translateService.instant('No teams available to add'),\r\n                      icon: 'error',\r\n                      confirmButtonText: this._translateService.instant('OK'),\r\n                      confirmButtonColor: '#3085d6',\r\n                    });\r\n                  }\r\n                });\r\n              });\r\n          }\r\n        });\r\n      });\r\n  }\r\n\r\n  groupStage;\r\n  openModalEdit(content, groupStage) {\r\n    this.groupStage = groupStage;\r\n    if (this.groupStage.group) {\r\n      this.model = {\r\n        group_name: this.groupStage.group,\r\n      };\r\n    }\r\n    this._modalService\r\n      .open(content, {\r\n        ariaLabelledBy: 'modal-basic-title',\r\n      })\r\n      .result.then(\r\n        (result) => {\r\n          this.groupStage = null;\r\n        },\r\n        (reason) => {\r\n          this.groupStage = null;\r\n        }\r\n      );\r\n  }\r\n\r\n  onEdit(model) {\r\n    this.groupStage;\r\n    \r\n    if (this.form.invalid || !this.groupStage) return;\r\n    let params = new FormData();\r\n    params.append('stage_id', this.groupStage.stage_id);\r\n    params.append('old_group', this.groupStage.group.toString());\r\n    params.append('group', model.group_name);\r\n    params.append('all_teams_in_groups', 'true');\r\n\r\n    this._loadingService.show();\r\n    this.editGroup(params).then((res) => {\r\n      this.getTeamsInStage();\r\n      this._modalService.dismissAll();\r\n      },\r\n      (error) => {  \r\n        if (error.errors){\r\n          this.form.controls[\"group_name\"].setErrors({\r\n            serverError: error.message,\r\n          });\r\n        }\r\n      }\r\n    )\r\n  }\r\n\r\n  private editGroup(params) {\r\n\r\n     return this._stageService.editGroup(params).toPromise().then((res: any) => {\r\n      if (res) {\r\n        return res;\r\n      }\r\n    }\r\n  )\r\n  }\r\n}\r\n", "<div class=\"card\">\r\n  <div class=\"card-header\">\r\n    <h3 class=\"card-title\">{{ 'Stage Teams' | translate }}</h3>\r\n    <div></div>\r\n    <button class=\"btn btn-primary float-right\" *ngIf=\"\r\n        stage_type == AppConfig.TOURNAMENT_TYPES.league ||\r\n        stage_type == AppConfig.TOURNAMENT_TYPES.groups\r\n      \" (click)=\"addGroupTeam()\">\r\n      <i class=\"fa fa-plus\"></i>\r\n      <!-- space -->\r\n      {{\r\n      stage_type == AppConfig.TOURNAMENT_TYPES.league\r\n      ? _translateService.instant('Add Team')\r\n      : stage_type == AppConfig.TOURNAMENT_TYPES.groups\r\n      ? _translateService.instant('Add Team & Group')\r\n      : _translateService.instant('Add Team')\r\n      }}\r\n      <ng-container *ngIf=\"allowEditTeam == false\">\r\n        <span class=\"ml-1\" [ngbTooltip]=\"'Please remove match before add team'\">\r\n          <i class=\"fa fa-info-circle\"></i>\r\n        </span>\r\n      </ng-container>\r\n    </button>\r\n\r\n    <div class=\"btn-group mt-1 float-right\" *ngIf=\"stage_type == 'Knockout' && tournament.type_knockout == AppConfig.KNOCKOUT_TYPES.type4\" [disabled]=\"allowEditTeam == false\">\r\n      <button class=\"btn btn-primary\" (click)=\"addGroupTeam()\">\r\n        <i class=\"fa fa-plus\"></i>\r\n        <!-- space -->\r\n        {{ 'Add Team' | translate }}\r\n        <ng-container *ngIf=\"allowEditTeam == false\">\r\n          <span class=\"ml-1\" [ngbTooltip]=\"'Please remove match before add team'\">\r\n            <i class=\"fa fa-info-circle\"></i>\r\n          </span>\r\n        </ng-container>\r\n      </button>\r\n\r\n      <!-- button import group -->\r\n      <button class=\"btn btn-warning\" (click)=\"importTeams()\" [disabled]=\"allowEditTeam == false\">\r\n        <i class=\"fa-solid fa-arrow-up-from-line\"></i>\r\n        <!-- space -->\r\n        {{ 'Import From Groups' | translate }}\r\n        <ng-container *ngIf=\"allowEditTeam == false\">\r\n          <span class=\"ml-1\" [ngbTooltip]=\"'Please remove match before add team'\">\r\n            <i class=\"fa fa-info-circle\"></i>\r\n          </span>\r\n        </ng-container>\r\n      </button>\r\n    </div>\r\n    <!-- <span *ngIf=\"allowEditTeam==false\" ngbTooltip=\"Please delete the schedule before adding or removing teams\" placement=\"left\"\r\n    [ngbTooltip]=\"TooltipTemplate\" container=\"body\"></span> -->\r\n  </div>\r\n  <div class=\"card-body\">\r\n    <!-- center the container -->\r\n    <div class=\"row\">\r\n      <!-- group stages -->\r\n      <div style=\"width: 100%; padding: 10px\" class=\"col-12 bg-light card p-1\"\r\n        *ngFor=\"let group_stage of group_stages; let i = index\">\r\n        <span *ngIf=\"stage_type == AppConfig.TOURNAMENT_TYPES.groups\" class=\"text-primary\">{{ 'group' | translate }} {{\r\n          group_stage[0].group }}\r\n          <i *ngIf=\"allowEditTeam == true\" style=\"z-index: 1; position: relative\"\r\n            class=\"fa fa-trash float-right text-danger p-1\" (click)=\"removeGroup(group_stage)\" placement=\"left\"\r\n            ngbTooltip=\"{{'Remove Group'|translate}}\"></i>\r\n          <i style=\"z-index: 1; position: relative\" class=\"fa fa-edit float-right text-warning p-1\" placement=\"left\"\r\n            ngbTooltip=\"{{'Edit group'|translate}}\" (click)=\"openModalEdit(content,group_stage[0])\"></i>\r\n        </span>\r\n\r\n        <!-- if stagetype !=Group -->\r\n        <span *ngIf=\"stage_type != AppConfig.TOURNAMENT_TYPES.groups\" class=\"text-primary\">\r\n          <i *ngIf=\"allowEditTeam == true\" style=\"z-index: 1; position: relative\"\r\n            class=\"fa fa-trash fa-lg float-right text-danger\" (click)=\"removeAllTeam(group_stage)\" placement=\"left\"\r\n            [disabled]=\"allowEditTeam == false\" ngbTooltip=\"Remove all teams\"></i>\r\n        </span>\r\n\r\n        <div class=\"row d-flex justify-content-left\">\r\n          <div class=\"col-lg-3 col-md-6 col-sm-12 mt-1\" *ngFor=\"let stage of sortBy(group_stage); let j = index\">\r\n            <div class=\"rounded bg-white\">\r\n              <div class=\"row p-1 align-items-center\">\r\n                <div class=\"col-auto\">\r\n                  <img class=\"avatar avatar-sm bg-white rounded-0\" src=\"{{ stage.team.club.logo }}\" alt=\"logo\" width=\"60px\"\r\n                    height=\"60px\" />\r\n                </div>\r\n\r\n                <div class=\"col\">\r\n                  <h4 [innerHTML]=\"stage.team.name\" class=\"text-nowrap\"></h4>\r\n                </div>\r\n\r\n                <div class=\"col text-right\">\r\n                  <div class=\"col-auto p-0 m-0\" ngbDropdown container=\"body\">\r\n                    <button type=\"button\" class=\"btn hide-arrow p-0 text-secondary\" [style]=\"btnStyle\" ngbDropdownToggle\r\n                      data-toggle=\"dropdown\">\r\n                      <i class=\"fa-regular fa-ellipsis-vertical\"></i>\r\n                    </button>\r\n                    <div ngbDropdownMenu>\r\n                      <ng-container>\r\n                        <a ngbDropdownItem (click)=\"changeTeam(stage, 'edit')\" [style]=\"btnStyle\">\r\n                          <i class=\"fa-light fa-arrows-repeat fa-lg mr-1\" placement=\"left\"\r\n                            ngbTooltip=\"Change another team\"></i>\r\n                          <span>{{ 'Change Team' | translate }}</span>\r\n                        </a>\r\n                      </ng-container>\r\n\r\n                      <ng-container>\r\n                        <a *ngIf=\"allowEditTeam == true\" ngbDropdownItem (click)=\"removeTeam(stage, 'remove')\"\r\n                          [style]=\"btnStyle\">\r\n                          <i id=\"trash\" class=\"fal fa-trash fa-lg mr-1\" (click)=\"removeTeam(stage, 'remove')\"\r\n                            placement=\"left\" ngbTooltip=\"Remove this team\"></i>\r\n                          <span>{{ 'Remove Team' | translate }}</span>\r\n                        </a>\r\n                      </ng-container>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- remove group -->\r\n      </div>\r\n      <!-- repeat the above div two more times for each row -->\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<ng-template #content let-modal>\r\n  <form [formGroup]=\"form\" (ngSubmit)=\"onEdit(model)\">\r\n    <div class=\"modal-header\">\r\n      <h4 class=\"modal-title\" id=\"modal-basic-title\">{{'Edit group' |translate }}</h4>\r\n      <button type=\"button\" class=\"close\" (click)=\"modal.dismiss('Cross click')\" aria-label=\"Close\">\r\n        <span aria-hidden=\"true\">&times;</span>\r\n      </button>\r\n    </div>\r\n    <div class=\"modal-body\">\r\n      <div class=\"text-center\">\r\n        <img src=\"assets/images/ai/Frame.svg\" alt=\"Frame\" width=\"200px\" height=\"149px\">\r\n        <p>\r\n          {{'Enter group name' | translate}}\r\n        </p>\r\n      </div>\r\n      <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n    </div>\r\n    <div class=\"modal-footer\">\r\n      <button type=\"submit\" class=\"btn btn-success\">{{'Save' | translate}}</button>\r\n    </div>\r\n  </form>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}