{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { AppConfig } from 'app/app-config';\nimport { DataTableDirective } from 'angular-datatables';\nimport { Subject } from 'rxjs';\nimport { environment } from 'environments/environment';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/stage.service\";\nimport * as i2 from \"app/services/user.service\";\nimport * as i3 from \"app/services/auth.service\";\nimport * as i4 from \"app/services/commons.service\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"@core/components/core-sidebar/core-sidebar.service\";\nfunction StageTablesComponent_div_2_th_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 16);\n    i0.ɵɵtext(1, \" Action \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 25);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 26)(1, \"div\", 27)(2, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const stageTeam_r11 = i0.ɵɵnextContext().$implicit;\n      const group_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.updateOrder(group_r5.name, stageTeam_r11.team_id, \"up\"));\n    });\n    i0.ɵɵelement(3, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const stageTeam_r11 = i0.ɵɵnextContext().$implicit;\n      const group_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.updateOrder(group_r5.name, stageTeam_r11.team_id, \"down\"));\n    });\n    i0.ɵɵelement(5, \"i\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    const i_r12 = ctx_r23.index;\n    const stageTeam_r11 = ctx_r23.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r15.showButton(i_r12, stageTeam_r11.team_id, \"up\") ? \"visible\" : \"invisible\")(\"disabled\", !ctx_r15.canUpdateLeaderboard);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r15.showButton(i_r12, stageTeam_r11.team_id, \"down\") ? \"visible\" : \"invisible\")(\"disabled\", !ctx_r15.canUpdateLeaderboard);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 18)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, StageTablesComponent_div_2_ng_container_32_tr_1_i_5_Template, 1, 0, \"i\", 19);\n    i0.ɵɵtemplate(6, StageTablesComponent_div_2_ng_container_32_tr_1_i_6_Template, 1, 0, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelement(8, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 22);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 11);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 11);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 22);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, StageTablesComponent_div_2_ng_container_32_tr_1_td_25_Template, 6, 4, \"td\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stageTeam_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const group_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i_r12 + 1, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) && (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) < 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) && (ctx_r10.orderChange.get(group_r5.name) ? ctx_r10.orderChange.get(group_r5.name).get(stageTeam_r11.team_id) : null) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", stageTeam_r11.team.club.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.team.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.no_matches);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", stageTeam_r11.no_wins, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", stageTeam_r11.no_draws, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", stageTeam_r11.no_losses, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.goals_for);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.goals_against);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r11.points);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.showUpdateButton);\n  }\n}\nfunction StageTablesComponent_div_2_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StageTablesComponent_div_2_ng_container_32_tr_1_Template, 26, 13, \"tr\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const group_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.groupData[group_r5.name]);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    group: a0\n  };\n};\nfunction StageTablesComponent_div_2_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 32);\n  }\n  if (rf & 2) {\n    const group_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, group_r5));\n  }\n}\nfunction StageTablesComponent_div_2_div_34_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1, \" You cannot update the leaderboard once the knockout stage has started. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StageTablesComponent_div_2_div_34_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function StageTablesComponent_div_2_div_34_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const group_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.submitOrder(group_r5.name));\n    });\n    i0.ɵɵtext(1, \" Submit \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r28.canUpdateLeaderboard);\n  }\n}\nfunction StageTablesComponent_div_2_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, StageTablesComponent_div_2_div_34_p_1_Template, 2, 0, \"p\", 34);\n    i0.ɵɵtemplate(2, StageTablesComponent_div_2_div_34_button_2_Template, 2, 1, \"button\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.canUpdateLeaderboard === false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.stage && ctx_r9.stage.type !== ctx_r9.AppConfig.TOURNAMENT_TYPES.knockout);\n  }\n}\nfunction StageTablesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 5)(2, \"div\", 6)(3, \"h4\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"div\", 8);\n    i0.ɵɵelementStart(6, \"div\", 9)(7, \"table\", 10)(8, \"thead\")(9, \"tr\")(10, \"th\");\n    i0.ɵɵtext(11, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Logo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"P\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 11);\n    i0.ɵɵtext(19, \"W\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 11);\n    i0.ɵɵtext(21, \"D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 11);\n    i0.ɵɵtext(23, \"L\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\");\n    i0.ɵɵtext(27, \"A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\");\n    i0.ɵɵtext(29, \"Pts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, StageTablesComponent_div_2_th_30_Template, 2, 0, \"th\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"tbody\");\n    i0.ɵɵtemplate(32, StageTablesComponent_div_2_ng_container_32_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵtemplate(33, StageTablesComponent_div_2_ng_container_33_Template, 1, 4, \"ng-container\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(34, StageTablesComponent_div_2_div_34_Template, 3, 2, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"col  \", ctx_r0.tableData.length > 1 ? \"col-lg-6\" : \"\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(group_r5.name);\n    i0.ɵɵadvance(26);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showUpdateButton && ctx_r0.stage && ctx_r0.stage.type !== ctx_r0.AppConfig.TOURNAMENT_TYPES.knockout);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.stage && ctx_r0.stage.type !== ctx_r0.AppConfig.TOURNAMENT_TYPES.knockout);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.stage || ctx_r0.stage && ctx_r0.stage.type === ctx_r0.AppConfig.TOURNAMENT_TYPES.knockout);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showUpdateButton);\n  }\n}\nfunction StageTablesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 5)(2, \"div\", 6)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"table\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 3, \"Point Adjustment\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"dtOptions\", ctx_r1.dtOptions)(\"dtTrigger\", ctx_r1.dtTrigger);\n  }\n}\nfunction StageTablesComponent_core_sidebar_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"core-sidebar\", 40)(1, \"app-editor-sidebar\", 41);\n    i0.ɵɵlistener(\"onSuccess\", function StageTablesComponent_core_sidebar_4_Template_app_editor_sidebar_onSuccess_1_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onSuccess($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"name\", ctx_r2.table_name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"table\", ctx_r2.dtElement)(\"fields\", ctx_r2.fields)(\"params\", ctx_r2.params)(\"paramsToPost\", ctx_r2.paramsToPost)(\"fields_subject\", ctx_r2.fields_subject);\n  }\n}\nfunction StageTablesComponent_ng_template_5_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 11);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 22);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stageTeam_r36 = ctx.$implicit;\n    const i_r37 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r37 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", stageTeam_r36.team.club.logo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.team.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_matches);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_wins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_draws);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.no_losses);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.goals_for);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.goals_against);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stageTeam_r36.points);\n  }\n}\nfunction StageTablesComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, StageTablesComponent_ng_template_5_tr_0_Template, 21, 10, \"tr\", 17);\n  }\n  if (rf & 2) {\n    const group_r34 = ctx.group;\n    i0.ɵɵproperty(\"ngForOf\", group_r34.teams);\n  }\n}\nexport class StageTablesComponent {\n  set tableData(value) {\n    this._tableData = value;\n    // this.makeTeamData();\n  }\n\n  get tableData() {\n    return this._tableData;\n  }\n  constructor(stageService, _userService, _authService, _commonsService, _http, _translateService, _coreSidebarService) {\n    this.stageService = stageService;\n    this._userService = _userService;\n    this._authService = _authService;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this.AppConfig = AppConfig;\n    this.dtElement = DataTableDirective;\n    this.onDataChange = new EventEmitter();\n    this.showUpdateButton = true;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.table_name = 'adjust-point-table';\n    this.fields_subject = new Subject();\n    this.paramsToPost = {};\n    this.fields = [{\n      key: 'stage_team_id',\n      type: 'select',\n      props: {\n        label: this._translateService.instant('Team'),\n        placeholder: this._translateService.instant('Select team'),\n        required: true,\n        options: []\n      }\n    }, {\n      key: 'user_id',\n      type: 'input',\n      props: {\n        required: true,\n        type: 'hidden'\n      },\n      defaultValue: this._authService.currentUserValue.id\n    }, {\n      key: 'points',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Points'),\n        placeholder: this._translateService.instant('Enter points'),\n        required: true,\n        type: 'number',\n        max: 1000\n      }\n    }, {\n      key: 'reason',\n      type: 'textarea',\n      props: {\n        label: this._translateService.instant('Reason'),\n        placeholder: this._translateService.instant('Enter reason'),\n        required: true\n      }\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Add adjustment point'),\n        edit: this._translateService.instant('Edit point'),\n        remove: this._translateService.instant('Remove')\n      },\n      url: `${environment.apiUrl}/adjustment-points/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.mapEqual = new Map();\n    this.orderChange = new Map();\n    this.canUpdateLeaderboard = null;\n    this.groupData = [];\n    this.alert = alert;\n  }\n  ngOnInit() {\n    if (this.stage) {\n      if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n        this.initAdjustmentsPointTable();\n      }\n      this.stageService.checkCanUpdateLeaderboard(this.stage.id).subscribe(response => {\n        console.log(response);\n        this.canUpdateLeaderboard = response;\n      });\n      this.makeTeamData();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['showUpdateButton'] && !changes['showUpdateButton'].firstChange) {\n      console.log(\"🚀 ~ StageTablesComponent ~ ngOnChanges ~ changes['showUpdateButton'].currentValue:\", changes['showUpdateButton'].currentValue);\n      this.showUpdateButton = changes['showUpdateButton'].currentValue;\n    }\n    if (changes['tableData'] && !changes['tableData'].firstChange) {\n      if (this.showUpdateButton !== false) {\n        this.makeTeamData();\n      }\n    }\n  }\n  makeTeamData() {\n    if (!this.showUpdateButton) {\n      return;\n    }\n    if (!this.showUpdateButton) {\n      return;\n    }\n    if (this.tableData && this.stage.type !== AppConfig.TOURNAMENT_TYPES.knockout) {\n      this.tableData.forEach(group => {\n        this.groupData[group.name] = group.teams;\n      });\n    } else {\n      this.groupData = this.tableData;\n    }\n    this.mapTeamsEqualPoint();\n  }\n  showButton(index, teamId, buttonDirection) {\n    if (buttonDirection === 'up' && index === 0 || buttonDirection === 'down' && index === this.groupData.length - 1) {\n      return false;\n    }\n    const equalTeams = this.mapEqual.get(teamId);\n    if (equalTeams) {\n      return equalTeams[buttonDirection];\n    }\n    return false;\n  }\n  checkCanChangeOrder(equalPointTeams, team, rankingCriteria) {\n    if (!equalPointTeams?.length) return {\n      teamCanUp: null,\n      teamCanDown: null\n    };\n    let filteredTeams = [...equalPointTeams];\n    const currentTotalPoints = team.points;\n    const compareStep = (teams, key) => {\n      return teams.filter(t => t[key] === team[key]);\n    };\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.head_to_head) {\n      filteredTeams = compareStep(filteredTeams, '_h2h_points');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_difference');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\n    }\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.total) {\n      filteredTeams = compareStep(filteredTeams, 'goals_difference');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_points');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\n    }\n    const teamCanUp = filteredTeams.filter(t => t.order < team.order);\n    const teamCanDown = filteredTeams.filter(t => t.order > team.order);\n    return {\n      teamCanUp: teamCanUp.length > 0 ? teamCanUp[teamCanUp.length - 1] : null,\n      teamCanDown: teamCanDown.length > 0 ? teamCanDown[0] : null\n    };\n  }\n  mapTeamsEqualPoint() {\n    this.mapEqual.clear();\n    Object.values(this.groupData).forEach(group => {\n      group.forEach(team => {\n        const equalPointTeams = group.filter(t => t.points === team.points && t.no_matches === team.no_matches && t.no_wins === team.no_wins && t.no_draws === team.no_draws && t.no_losses === team.no_losses && t.goals_for === team.goals_for && t.goals_against === team.goals_against && t.goals_difference === team.goals_difference && t.team_id !== team.team_id);\n        const {\n          teamCanUp,\n          teamCanDown\n        } = this.checkCanChangeOrder(equalPointTeams, team, this.stage.ranking_criteria);\n        this.mapEqual.set(team.team_id, {\n          up: teamCanUp,\n          down: teamCanDown\n        });\n      });\n    });\n    console.log('this.mapEqual', this.mapEqual);\n    return this.mapEqual;\n  }\n  updateOrder(groupName, teamId, direction) {\n    const equalTeams = this.mapEqual.get(teamId);\n    if (equalTeams) {\n      const team = equalTeams[direction];\n      const teamIndex = this.groupData[groupName].findIndex(t => t.team_id === team.team_id);\n      const currentTeamIndex = this.groupData[groupName].findIndex(t => t.team_id === teamId);\n      if (teamIndex !== -1 && currentTeamIndex !== -1) {\n        this.updateOrderChange(groupName, this.groupData[groupName][currentTeamIndex].team_id, direction);\n        this.updateOrderChange(groupName, this.groupData[groupName][teamIndex].team_id, direction === 'up' ? 'down' : 'up');\n        // Swap the order of the two teams\n        const tempOrder = this.groupData[groupName][teamIndex].order;\n        this.groupData[groupName][teamIndex].order = this.groupData[groupName][currentTeamIndex].order;\n        this.groupData[groupName][currentTeamIndex].order = tempOrder;\n        this.groupData[groupName] = this.groupData[groupName].sort((a, b) => a.order - b.order);\n        // Update the map\n        this.mapTeamsEqualPoint();\n      }\n    }\n    ;\n  }\n  updateOrderChange(groupName, teamId, direction) {\n    if (!this.orderChange.get(groupName)) {\n      this.orderChange.set(groupName, new Map());\n    }\n    const currentTeamOrderChange = this.orderChange.get(groupName).get(teamId) || 0;\n    if (direction === 'up') {\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange + 1);\n    } else if (direction === 'down') {\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange - 1);\n    }\n  }\n  makeSubmitOrderData(groupName) {\n    return this.groupData[groupName].map(team => ({\n      team_id: team.team_id,\n      order: team.order,\n      stage_id: this.stage.id\n    }));\n  }\n  submitOrder(groupName) {\n    Swal.fire({\n      title: this._translateService.instant('Submit new Leaderboard'),\n      text: this._translateService.instant('Are you sure you want to submit the new Leaderboard?'),\n      showCancelButton: true,\n      confirmButtonText: this._translateService.instant('Submit'),\n      cancelButtonText: this._translateService.instant('No'),\n      icon: 'warning',\n      reverseButtons: true\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.stageService.submitTeamOrder(this.makeSubmitOrderData(groupName)).subscribe(response => {\n          console.log('Order submitted successfully:', response);\n          Swal.fire({\n            title: this._translateService.instant('Success'),\n            text: this._translateService.instant('The new Leaderboard has been submitted successfully.'),\n            icon: 'success',\n            confirmButtonText: this._translateService.instant('OK')\n          });\n          this.orderChange.get(groupName).clear();\n          this.stageService.checkMatchScore(this.stage.id).subscribe();\n        }, error => {\n          console.error('Error submitting order:', error);\n          Swal.fire({\n            title: this._translateService.instant('Error'),\n            text: this._translateService.instant(error.message),\n            icon: 'error',\n            confirmButtonText: this._translateService.instant('OK')\n          });\n        });\n      }\n    });\n  }\n  editor(action, row) {\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  onSuccess($event) {\n    this.onDataChange.emit($event);\n    this.makeTeamData();\n  }\n  initAdjustmentsPointTable() {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        // add season id\n        this._http.post(`${environment.apiUrl}/adjustment-points/all/${this.stage.id}`, dataTablesParameters).subscribe(resp => {\n          this.fields[0].props.options = resp.options.teams;\n          this.fields_subject.next(this.fields);\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [],\n      columns: [{\n        data: 'id',\n        visible: false\n      }, {\n        title: this._translateService.instant('Team'),\n        data: 'team.name'\n      }, {\n        title: this._translateService.instant('Points'),\n        data: 'points'\n      }, {\n        title: this._translateService.instant('Reason'),\n        data: 'reason'\n      }, {\n        title: this._translateService.instant('Updated by'),\n        data: 'user_id',\n        render: (data, type, row) => {\n          return this._userService.fullName(row.user);\n        }\n      }],\n      lengthMenu: [[25, 50, 100, -1], [25, 50, 100, 'All']],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant('Add')}`,\n          action: (e, dt, node, config) => {\n            this.editor('create');\n          }\n        }, {\n          text: `<i class=\"fa-solid fa-edit\"></i> ${this._translateService.instant('Edit')}`,\n          extend: 'selectedSingle',\n          action: (e, dt, node, config) => {\n            this.editor('edit');\n          }\n        }, {\n          text: `<i class=\"fa-solid fa-trash\"></i> ${this._translateService.instant('Remove')}`,\n          extend: 'selected',\n          action: (e, dt, node, config) => {\n            this.editor('remove');\n          }\n        }]\n      }\n    };\n  }\n  ngAfterViewInit() {\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n      setTimeout(() => {\n        this.dtTrigger.next(this.dtOptions);\n      }, 1000);\n    }\n  }\n  ngOnDestroy() {\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\n      this.dtTrigger.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function StageTablesComponent_Factory(t) {\n    return new (t || StageTablesComponent)(i0.ɵɵdirectiveInject(i1.StageService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CommonsService), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.CoreSidebarService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageTablesComponent,\n    selectors: [[\"stage-tables\"]],\n    viewQuery: function StageTablesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      showUpdateButton: \"showUpdateButton\",\n      tableData: \"tableData\",\n      stage: \"stage\"\n    },\n    outputs: {\n      onDataChange: \"onDataChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 7,\n    vars: 3,\n    consts: [[1, \"row\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-lg-6\", 4, \"ngIf\"], [\"class\", \"modal modal-slide-in sidebar-todo-modal fade\", \"overlayClass\", \"modal-backdrop\", 3, \"name\", 4, \"ngIf\"], [\"notLeagueTemplate\", \"\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-body\"], [1, \"table-responsive\"], [1, \"table\"], [1, \"d-none\", \"d-sm-table-cell\"], [\"class\", \"text-center\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", 4, \"ngIf\"], [\"class\", \"mt-1 mr-1 mb-3 d-flex flex-column align-items-end\", 4, \"ngIf\"], [1, \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", 2, \"gap\", \"4px\"], [\"class\", \"text-danger bi bi-caret-down-fill\", 4, \"ngIf\"], [\"class\", \"text-success bi bi-caret-up-fill\", 4, \"ngIf\"], [\"alt\", \"logo\", \"width\", \"30\", \"height\", \"30\", 3, \"src\"], [1, \"font-weight-bold\"], [\"class\", \"font-weight-bold text-center\", 4, \"ngIf\"], [1, \"text-danger\", \"bi\", \"bi-caret-down-fill\"], [1, \"text-success\", \"bi\", \"bi-caret-up-fill\"], [1, \"font-weight-bold\", \"text-center\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"gap\", \"4px\"], [1, \"btn\", \"btn-outline-success\", 2, \"padding\", \"4px\", 3, \"ngClass\", \"disabled\", \"click\"], [1, \"text-success\", \"bi\", \"bi-arrow-up\"], [1, \"btn\", \"btn-outline-danger\", 2, \"padding\", \"4px\", 3, \"ngClass\", \"disabled\", \"click\"], [1, \"text-danger\", \"bi\", \"bi-arrow-down\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"mt-1\", \"mr-1\", \"mb-3\", \"d-flex\", \"flex-column\", \"align-items-end\"], [\"class\", \"h5 mw-100 badge badge-light-warning px-2 py-1\", \"style\", \"font-size: 12px\", 4, \"ngIf\"], [\"class\", \"btn btn-primary mw-100\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"h5\", \"mw-100\", \"badge\", \"badge-light-warning\", \"px-2\", \"py-1\", 2, \"font-size\", \"12px\"], [1, \"btn\", \"btn-primary\", \"mw-100\", 3, \"disabled\", \"click\"], [1, \"col-lg-6\"], [\"datatable\", \"\", 1, \"table\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\", \"paramsToPost\", \"fields_subject\", \"onSuccess\"]],\n    template: function StageTablesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0);\n        i0.ɵɵtemplate(2, StageTablesComponent_div_2_Template, 35, 8, \"div\", 1);\n        i0.ɵɵtemplate(3, StageTablesComponent_div_3_Template, 7, 5, \"div\", 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(4, StageTablesComponent_core_sidebar_4_Template, 2, 6, \"core-sidebar\", 3);\n        i0.ɵɵtemplate(5, StageTablesComponent_ng_template_5_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tableData);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage && ctx.stage.type === ctx.AppConfig.TOURNAMENT_TYPES.league);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage && ctx.stage.type === ctx.AppConfig.TOURNAMENT_TYPES.league);\n      }\n    },\n    styles: [\".table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.72rem !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlLXRhYmxlcy9zdGFnZS10YWJsZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7O0VBRUUsMkJBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi50YWJsZSB0aCxcclxuLnRhYmxlIHRkIHtcclxuICBwYWRkaW5nOiAwLjcycmVtICFpbXBvcnRhbnQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAoE,eAAe;AAEnH,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,kBAAkB,QAAQ,oBAAoB;AAEvD,SAASC,OAAO,QAAQ,MAAM;AAI9B,SAASC,WAAW,QAAQ,0BAA0B;AAKtD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;ICWdC,8BAOC;IACCA,wBACF;IAAAA,iBAAK;;;;;IAeCA,wBAUK;;;;;IACLA,wBAUK;;;;;;IAyBTA,8BAGC;IAaKA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAC6BA,yEACjC,IAAI,CAAC;IAAA;IAGDA,wBAA2C;IAC7CA,iBAAS;IACTA,kCAYC;IAJCA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAC6BA,yEACnC,MAAM,CAAC;IAAA;IAGDA,wBAA4C;IAC9CA,iBAAS;;;;;;;IA5BPA,eAIC;IAJDA,0GAIC;IAWDA,eAIC;IAJDA,4GAIC;;;;;IAhFTA,0BAAgE;IAIxDA,YACF;IAAAA,iBAAO;IACPA,6FAUK;IACLA,6FAUK;IACPA,iBAAM;IAERA,0BAAI;IACFA,0BAKE;IACJA,iBAAK;IACLA,8BAA6B;IAAAA,aAAyB;IAAAA,iBAAK;IAC3DA,2BAAI;IAAAA,aAA0B;IAAAA,iBAAK;IACnCA,+BAAmC;IACjCA,aACF;IAAAA,iBAAK;IACLA,+BAAmC;IACjCA,aACF;IAAAA,iBAAK;IACLA,+BAAmC;IACjCA,aACF;IAAAA,iBAAK;IACLA,2BAAI;IAAAA,aAAyB;IAAAA,iBAAK;IAClCA,2BAAI;IAAAA,aAA6B;IAAAA,iBAAK;IACtCA,+BAA6B;IAAAA,aAAsB;IAAAA,iBAAK;IACxDA,iGAuCK;IACPA,iBAAK;;;;;;;IAxFGA,eACF;IADEA,0CACF;IAGGA,eAOL;IAPKA,qQAOL;IAIKA,eAOL;IAPKA,qQAOL;IAMEA,eAAgC;IAAhCA,oEAAgC;IAMPA,eAAyB;IAAzBA,6CAAyB;IAClDA,eAA0B;IAA1BA,8CAA0B;IAE5BA,eACF;IADEA,sDACF;IAEEA,eACF;IADEA,uDACF;IAEEA,eACF;IADEA,wDACF;IACIA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAA6B;IAA7BA,iDAA6B;IACJA,eAAsB;IAAtBA,0CAAsB;IAGhDA,eAAsB;IAAtBA,+CAAsB;;;;;IA3D7BA,6BAIC;IACCA,4FA4FK;IACPA,0BAAe;;;;;IA7FaA,eAA0B;IAA1BA,yDAA0B;;;;;;;;;;IA8FtDA,4BAOgB;;;;;;IAFdA,sCAAsC;;;;;IAW5CA,6BAIC;IACCA,wFAEF;IAAAA,iBAAI;;;;;;IACJA,kCAKC;IAHCA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAuB;IAAA,EAAC;IAIjCA,wBACF;IAAAA,iBAAS;;;;IAHPA,wDAAkC;;;;;IAhBtCA,+BAGC;IACCA,+EAOI;IACJA,yFAOS;IACXA,iBAAM;;;;IAfDA,eAAoC;IAApCA,4DAAoC;IAUpCA,eAAiE;IAAjEA,uGAAiE;;;;;IAlK1EA,2BAGC;IAG4BA,YAAgB;IAAAA,iBAAK;IAE9CA,yBAA6B;IAC7BA,8BAA8B;IAIlBA,kBAAC;IAAAA,iBAAK;IACVA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IACbA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IACbA,2BAAI;IAAAA,kBAAC;IAAAA,iBAAK;IACVA,+BAAmC;IAAAA,kBAAC;IAAAA,iBAAK;IACzCA,+BAAmC;IAAAA,kBAAC;IAAAA,iBAAK;IACzCA,+BAAmC;IAAAA,kBAAC;IAAAA,iBAAK;IACzCA,2BAAI;IAAAA,kBAAC;IAAAA,iBAAK;IACVA,2BAAI;IAAAA,kBAAC;IAAAA,iBAAK;IACVA,2BAAI;IAAAA,oBAAG;IAAAA,iBAAK;IACZA,4EASK;IACPA,iBAAK;IAEPA,8BAAO;IACLA,gGAkGe;IACfA,gGAOgB;IAClBA,iBAAQ;IAIZA,8EAoBM;IACRA,iBAAM;;;;;IAvKNA,qFAAyD;IAK9BA,eAAgB;IAAhBA,mCAAgB;IAmB9BA,gBAIF;IAJEA,kIAIF;IAQAA,eAEA;IAFAA,uGAEA;IAiGAA,eAGD;IAHCA,wHAGD;IAULA,eAAsB;IAAtBA,8CAAsB;;;;;IAsB7BA,+BAGC;IAIOA,YACF;;IAAAA,iBAAK;IAEPA,4BAKS;IACXA,iBAAM;;;;IATAA,eACF;IADEA,yEACF;IAIAA,eAAuB;IAAvBA,4CAAuB;;;;;;IASjCA,wCAKC;IAKGA;MAAAA;MAAA;MAAA,OAAaA,wCAAiB;IAAA,EAAC;IAIjCA,iBAAqB;;;;IAXrBA,wCAAmB;IAIjBA,eAAmB;IAAnBA,wCAAmB;;;;;IAWrBA,0BAAsD;IAChDA,YAAW;IAAAA,iBAAK;IACpBA,0BAAI;IACFA,0BAA0E;IAC5EA,iBAAK;IACLA,8BAA6B;IAAAA,YAAyB;IAAAA,iBAAK;IAC3DA,0BAAI;IAAAA,YAA0B;IAAAA,iBAAK;IACnCA,8BAAmC;IAAAA,aAAuB;IAAAA,iBAAK;IAC/DA,+BAAmC;IAAAA,aAAwB;IAAAA,iBAAK;IAChEA,+BAAmC;IAAAA,aAAyB;IAAAA,iBAAK;IACjEA,2BAAI;IAAAA,aAAyB;IAAAA,iBAAK;IAClCA,2BAAI;IAAAA,aAA6B;IAAAA,iBAAK;IACtCA,+BAA6B;IAAAA,aAAsB;IAAAA,iBAAK;;;;;IAXpDA,eAAW;IAAXA,+BAAW;IAERA,eAAgC;IAAhCA,oEAAgC;IAEVA,eAAyB;IAAzBA,6CAAyB;IAClDA,eAA0B;IAA1BA,8CAA0B;IACKA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAwB;IAAxBA,4CAAwB;IACxBA,eAAyB;IAAzBA,6CAAyB;IACxDA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAA6B;IAA7BA,iDAA6B;IACJA,eAAsB;IAAtBA,0CAAsB;;;;;IAZrDA,oFAaK;;;;IAbqBA,yCAAgB;;;AD9L5C,OAAM,MAAOC,oBAAoB;EAQ/B,IAAaC,SAAS,CAACC,KAAU;IAC/B,IAAI,CAACC,UAAU,GAAGD,KAAK;IAEvB;EAEF;;EAEA,IAAID,SAAS;IACX,OAAO,IAAI,CAACE,UAAU;EACxB;EAmEAC,YACUC,YAA0B,EAC3BC,YAAyB,EACzBC,YAAyB,EACzBC,eAA+B,EAC9BC,KAAiB,EACjBC,iBAAmC,EACpCC,mBAAuC;IANtC,iBAAY,GAAZN,YAAY;IACb,iBAAY,GAAZC,YAAY;IACZ,iBAAY,GAAZC,YAAY;IACZ,oBAAe,GAAfC,eAAe;IACd,UAAK,GAALC,KAAK;IACL,sBAAiB,GAAjBC,iBAAiB;IAClB,wBAAmB,GAAnBC,mBAAmB;IA1F5B,cAAS,GAAGjB,SAAS;IAErB,cAAS,GAAQC,kBAAkB;IACzB,iBAAY,GAAG,IAAIF,YAAY,EAAO;IAEvC,qBAAgB,GAAG,IAAI;IAehC,cAAS,GAAyB,IAAIG,OAAO,EAAe;IAC5D,cAAS,GAAQ,EAAE;IACZ,eAAU,GAAG,oBAAoB;IACjC,mBAAc,GAAG,IAAIA,OAAO,EAAO;IACnC,iBAAY,GAAG,EAAE;IACjB,WAAM,GAAU,CACrB;MACEgB,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACL,iBAAiB,CAACM,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAACP,iBAAiB,CAACM,OAAO,CAAC,aAAa,CAAC;QAC1DE,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;;KAEZ,EACD;MACEP,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLI,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;OACP;MACDO,YAAY,EAAE,IAAI,CAACb,YAAY,CAACc,gBAAgB,CAACC;KAClD,EACD;MACEV,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACL,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/CC,WAAW,EAAE,IAAI,CAACP,iBAAiB,CAACM,OAAO,CAAC,cAAc,CAAC;QAC3DE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE,QAAQ;QACdU,GAAG,EAAE;;KAER,EACD;MACEX,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAACL,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/CC,WAAW,EAAE,IAAI,CAACP,iBAAiB,CAACM,OAAO,CAAC,cAAc,CAAC;QAC3DE,QAAQ,EAAE;;KAEb,CACF;IACM,WAAM,GAAwB;MACnCM,SAAS,EAAE,IAAI,CAACC,UAAU;MAC1BC,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAACjB,iBAAiB,CAACM,OAAO,CAAC,sBAAsB,CAAC;QAC9DY,IAAI,EAAE,IAAI,CAAClB,iBAAiB,CAACM,OAAO,CAAC,YAAY,CAAC;QAClDa,MAAM,EAAE,IAAI,CAACnB,iBAAiB,CAACM,OAAO,CAAC,QAAQ;OAChD;MACDc,GAAG,EAAE,GAAGjC,WAAW,CAACkC,MAAM,2BAA2B;MACrDC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAED,aAAQ,GAAG,IAAIC,GAAG,EAAE;IACpB,gBAAW,GAAG,IAAIA,GAAG,EAAE;IAEvB,yBAAoB,GAAmB,IAAI;IAc3C,cAAS,GAAG,EAAE;IA4VK,UAAK,GAAGC,KAAK;EA/VhC;EAKAC,QAAQ;IACN,IAAI,IAAI,CAACC,KAAK,EAAE;MACd,IAAI,IAAI,CAACA,KAAK,CAACxB,IAAI,IAAInB,SAAS,CAAC4C,gBAAgB,CAACC,MAAM,EAAE;QACxD,IAAI,CAACC,yBAAyB,EAAE;;MAElC,IAAI,CAACnC,YAAY,CAACoC,yBAAyB,CAAC,IAAI,CAACJ,KAAK,CAACf,EAAE,CAAC,CAACoB,SAAS,CAAEC,QAAiB,IAAI;QACzFC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;QACrB,IAAI,CAACG,oBAAoB,GAAGH,QAAQ;MACtC,CAAC,CAAC;MAEF,IAAI,CAACI,YAAY,EAAE;;EAKvB;EAEAC,WAAW,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAACA,OAAO,CAAC,kBAAkB,CAAC,CAACC,WAAW,EAAE;MAC3EN,OAAO,CAACC,GAAG,CAAC,qFAAqF,EAAEI,OAAO,CAAC,kBAAkB,CAAC,CAACE,YAAY,CAAC;MAC5I,IAAI,CAACC,gBAAgB,GAAGH,OAAO,CAAC,kBAAkB,CAAC,CAACE,YAAY;;IAGlE,IAAIF,OAAO,CAAC,WAAW,CAAC,IAAI,CAACA,OAAO,CAAC,WAAW,CAAC,CAACC,WAAW,EAAE;MAC7D,IAAI,IAAI,CAACE,gBAAgB,KAAK,KAAK,EAAE;QACnC,IAAI,CAACL,YAAY,EAAE;;;EAGzB;EAEAA,YAAY;IAEV,IAAI,CAAC,IAAI,CAACK,gBAAgB,EAAE;MAC1B;;IAGF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B;;IAGF,IAAI,IAAI,CAACnD,SAAS,IAAI,IAAI,CAACoC,KAAK,CAACxB,IAAI,KAAKnB,SAAS,CAAC4C,gBAAgB,CAACe,QAAQ,EAAE;MAC7E,IAAI,CAACpD,SAAS,CAACqD,OAAO,CAAEC,KAAK,IAAI;QAC/B,IAAI,CAACC,SAAS,CAACD,KAAK,CAACE,IAAI,CAAC,GAAGF,KAAK,CAACG,KAAK;MAC1C,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACF,SAAS,GAAG,IAAI,CAACvD,SAAS;;IAGjC,IAAI,CAAC0D,kBAAkB,EAAE;EAC3B;EAEAC,UAAU,CAACC,KAAa,EAAEC,MAAc,EAAEC,eAA8B;IACtE,IAAKA,eAAe,KAAK,IAAI,IAAIF,KAAK,KAAK,CAAC,IAAME,eAAe,KAAK,MAAM,IAAIF,KAAK,KAAK,IAAI,CAACL,SAAS,CAACQ,MAAM,GAAG,CAAE,EAAE;MACpH,OAAO,KAAK;;IAGd,MAAMC,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACL,MAAM,CAAC;IAE5C,IAAIG,UAAU,EAAE;MACd,OAAOA,UAAU,CAACF,eAAe,CAAC;;IAGpC,OAAO,KAAK;EAEd;EAEAK,mBAAmB,CAACC,eAA2B,EAAEC,IAAc,EAAEC,eAAuB;IAItF,IAAI,CAACF,eAAe,EAAEL,MAAM,EAAE,OAAO;MAAEQ,SAAS,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAI,CAAE;IAE3E,IAAIC,aAAa,GAAe,CAAC,GAAGL,eAAe,CAAC;IAEpD,MAAMM,kBAAkB,GAAGL,IAAI,CAACM,MAAM;IAEtC,MAAMC,WAAW,GAAG,CAACnB,KAAiB,EAAE9C,GAAmB,KAAgB;MACzE,OAAO8C,KAAK,CAACoB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnE,GAAG,CAAC,KAAK0D,IAAI,CAAC1D,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,IAAI2D,eAAe,KAAK7E,SAAS,CAACsF,gBAAgB,CAACC,YAAY,EAAE;MAC/DP,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,aAAa,CAAC;MACzD,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,SAAS,CAAC;MACnF,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,YAAY,CAAC;MACtF,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,kBAAkB,CAAC;MAC5F,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,WAAW,CAAC;;IAGvF,IAAIH,eAAe,KAAK7E,SAAS,CAACsF,gBAAgB,CAACE,KAAK,EAAE;MACxDR,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,kBAAkB,CAAC;MAE9D,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,WAAW,CAAC;MACrF,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,aAAa,CAAC;MACvF,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,SAAS,CAAC;MACnF,IAAIA,aAAa,CAACV,MAAM,GAAG,CAAC,EAAEU,aAAa,GAAGG,WAAW,CAACH,aAAa,EAAE,YAAY,CAAC;;IAGxF,MAAMF,SAAS,GAAGE,aAAa,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACI,KAAK,GAAGb,IAAI,CAACa,KAAK,CAAC;IACjE,MAAMV,WAAW,GAAGC,aAAa,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACI,KAAK,GAAGb,IAAI,CAACa,KAAK,CAAC;IAEnE,OAAO;MACLX,SAAS,EAAEA,SAAS,CAACR,MAAM,GAAG,CAAC,GAAGQ,SAAS,CAACA,SAAS,CAACR,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;MACxES,WAAW,EAAEA,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGS,WAAW,CAAC,CAAC,CAAC,GAAG;KACxD;EACH;EAEAd,kBAAkB;IAEhB,IAAI,CAACO,QAAQ,CAACkB,KAAK,EAAE;IAGrBC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9B,SAAS,CAAC,CAACF,OAAO,CAACC,KAAK,IAAG;MAC5CA,KAAK,CAACD,OAAO,CAAEgB,IAAI,IAAI;QAErB,MAAMD,eAAe,GAAGd,KAAK,CAACuB,MAAM,CAACC,CAAC,IACpCA,CAAC,CAACH,MAAM,KAAKN,IAAI,CAACM,MAAM,IACxBG,CAAC,CAACQ,UAAU,KAAKjB,IAAI,CAACiB,UAAU,IAChCR,CAAC,CAACS,OAAO,KAAKlB,IAAI,CAACkB,OAAO,IAC1BT,CAAC,CAACU,QAAQ,KAAKnB,IAAI,CAACmB,QAAQ,IAC5BV,CAAC,CAACW,SAAS,KAAKpB,IAAI,CAACoB,SAAS,IAC9BX,CAAC,CAACY,SAAS,KAAKrB,IAAI,CAACqB,SAAS,IAC9BZ,CAAC,CAACa,aAAa,KAAKtB,IAAI,CAACsB,aAAa,IACtCb,CAAC,CAACc,gBAAgB,KAAKvB,IAAI,CAACuB,gBAAgB,IAC5Cd,CAAC,CAACe,OAAO,KAAKxB,IAAI,CAACwB,OAAO,CAC3B;QAED,MAAM;UACJtB,SAAS;UACTC;QAAW,CACZ,GAAG,IAAI,CAACL,mBAAmB,CAACC,eAAe,EAAEC,IAAI,EAAE,IAAI,CAACjC,KAAK,CAAC0D,gBAAgB,CAAC;QAEhF,IAAI,CAAC7B,QAAQ,CAAC8B,GAAG,CAAC1B,IAAI,CAACwB,OAAO,EAAE;UAC9BG,EAAE,EAAEzB,SAAS;UACb0B,IAAI,EAAEzB;SACP,CAAC;MACJ,CAAC,CAAC;IAGJ,CAAC,CAAC;IACF7B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACqB,QAAQ,CAAC;IAC3C,OAAO,IAAI,CAACA,QAAQ;EAEtB;EAEAiC,WAAW,CAACC,SAAiB,EAAEtC,MAAc,EAAEuC,SAAwB;IACrE,MAAMpC,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACL,MAAM,CAAC;IAE5C,IAAIG,UAAU,EAAE;MACd,MAAMK,IAAI,GAAGL,UAAU,CAACoC,SAAS,CAAC;MAElC,MAAMC,SAAS,GAAG,IAAI,CAAC9C,SAAS,CAAC4C,SAAS,CAAC,CAACG,SAAS,CAACxB,CAAC,IAAIA,CAAC,CAACe,OAAO,KAAKxB,IAAI,CAACwB,OAAO,CAAC;MACtF,MAAMU,gBAAgB,GAAG,IAAI,CAAChD,SAAS,CAAC4C,SAAS,CAAC,CAACG,SAAS,CAACxB,CAAC,IAAIA,CAAC,CAACe,OAAO,KAAKhC,MAAM,CAAC;MAEvF,IAAIwC,SAAS,KAAK,CAAC,CAAC,IAAIE,gBAAgB,KAAK,CAAC,CAAC,EAAE;QAE/C,IAAI,CAACC,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC5C,SAAS,CAAC4C,SAAS,CAAC,CAACI,gBAAgB,CAAC,CAACV,OAAO,EAAEO,SAAS,CAAC;QACjG,IAAI,CAACI,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC5C,SAAS,CAAC4C,SAAS,CAAC,CAACE,SAAS,CAAC,CAACR,OAAO,EAAEO,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QAEnH;QACA,MAAMK,SAAS,GAAG,IAAI,CAAClD,SAAS,CAAC4C,SAAS,CAAC,CAACE,SAAS,CAAC,CAACnB,KAAK;QAC5D,IAAI,CAAC3B,SAAS,CAAC4C,SAAS,CAAC,CAACE,SAAS,CAAC,CAACnB,KAAK,GAAG,IAAI,CAAC3B,SAAS,CAAC4C,SAAS,CAAC,CAACI,gBAAgB,CAAC,CAACrB,KAAK;QAC9F,IAAI,CAAC3B,SAAS,CAAC4C,SAAS,CAAC,CAACI,gBAAgB,CAAC,CAACrB,KAAK,GAAGuB,SAAS;QAE7D,IAAI,CAAClD,SAAS,CAAC4C,SAAS,CAAC,GAAG,IAAI,CAAC5C,SAAS,CAAC4C,SAAS,CAAC,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzB,KAAK,GAAG0B,CAAC,CAAC1B,KAAK,CAAC;QAEvF;QACA,IAAI,CAACxB,kBAAkB,EAAE;;;IAG7B;EAEF;EAEA8C,iBAAiB,CAACL,SAAiB,EAAEtC,MAAc,EAAEuC,SAAwB;IAG3E,IAAI,CAAC,IAAI,CAACS,WAAW,CAAC3C,GAAG,CAACiC,SAAS,CAAC,EAAE;MACpC,IAAI,CAACU,WAAW,CAACd,GAAG,CAACI,SAAS,EAAE,IAAIlE,GAAG,EAAE,CAAC;;IAG5C,MAAM6E,sBAAsB,GAAG,IAAI,CAACD,WAAW,CAAC3C,GAAG,CAACiC,SAAS,CAAC,CAACjC,GAAG,CAACL,MAAM,CAAC,IAAI,CAAC;IAE/E,IAAIuC,SAAS,KAAK,IAAI,EAAE;MACtB,IAAI,CAACS,WAAW,CAAC3C,GAAG,CAACiC,SAAS,CAAC,CAACJ,GAAG,CAAClC,MAAM,EAAEiD,sBAAsB,GAAG,CAAC,CAAC;KACxE,MAAM,IAAIV,SAAS,KAAK,MAAM,EAAE;MAC/B,IAAI,CAACS,WAAW,CAAC3C,GAAG,CAACiC,SAAS,CAAC,CAACJ,GAAG,CAAClC,MAAM,EAAEiD,sBAAsB,GAAG,CAAC,CAAC;;EAE3E;EAEAC,mBAAmB,CAACZ,SAAiB;IACnC,OAAO,IAAI,CAAC5C,SAAS,CAAC4C,SAAS,CAAC,CAACa,GAAG,CAAE3C,IAAI,KAAM;MAC9CwB,OAAO,EAAExB,IAAI,CAACwB,OAAO;MACrBX,KAAK,EAAEb,IAAI,CAACa,KAAK;MACjB+B,QAAQ,EAAE,IAAI,CAAC7E,KAAK,CAACf;KACtB,CAAC,CAAC;EACL;EAEA6F,WAAW,CAACf,SAAiB;IAE3BtG,IAAI,CAACsH,IAAI,CAAC;MACR1F,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,wBAAwB,CAAC;MAC/DqG,IAAI,EAAE,IAAI,CAAC3G,iBAAiB,CAACM,OAAO,CAAC,sDAAsD,CAAC;MAC5FsG,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI,CAAC7G,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;MAC3DwG,gBAAgB,EAAE,IAAI,CAAC9G,iBAAiB,CAACM,OAAO,CAAC,IAAI,CAAC;MACtDyG,IAAI,EAAE,SAAS;MACfC,cAAc,EAAE;KACjB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAACxH,YAAY,CAACyH,eAAe,CAAC,IAAI,CAACd,mBAAmB,CAACZ,SAAS,CAAC,CAAC,CAAC1D,SAAS,CAAEC,QAAQ,IAAI;UAC5FC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,QAAQ,CAAC;UACtD7C,IAAI,CAACsH,IAAI,CAAC;YACR1F,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,SAAS,CAAC;YAChDqG,IAAI,EAAE,IAAI,CAAC3G,iBAAiB,CAACM,OAAO,CAAC,sDAAsD,CAAC;YAC5FyG,IAAI,EAAE,SAAS;YACfF,iBAAiB,EAAE,IAAI,CAAC7G,iBAAiB,CAACM,OAAO,CAAC,IAAI;WACvD,CAAC;UACF,IAAI,CAAC8F,WAAW,CAAC3C,GAAG,CAACiC,SAAS,CAAC,CAAChB,KAAK,EAAE;UACvC,IAAI,CAAC/E,YAAY,CAAC0H,eAAe,CAAC,IAAI,CAAC1F,KAAK,CAACf,EAAE,CAAC,CAACoB,SAAS,EAAE;QAC9D,CAAC,EAAGsF,KAAK,IAAI;UACXpF,OAAO,CAACoF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/ClI,IAAI,CAACsH,IAAI,CAAC;YACR1F,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,OAAO,CAAC;YAC9CqG,IAAI,EAAE,IAAI,CAAC3G,iBAAiB,CAACM,OAAO,CAACgH,KAAK,CAACC,OAAO,CAAC;YACnDR,IAAI,EAAE,OAAO;YACbF,iBAAiB,EAAE,IAAI,CAAC7G,iBAAiB,CAACM,OAAO,CAAC,IAAI;WACvD,CAAC;QACJ,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAkH,MAAM,CAACjG,MAAM,EAAEkG,GAAI;IACjB,IAAI,CAACC,MAAM,CAACnG,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAACmG,MAAM,CAACD,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACxH,mBAAmB,CAAC0H,kBAAkB,CAAC,IAAI,CAAC5G,UAAU,CAAC,CAAC6G,UAAU,EAAE;EAC3E;EAEAC,SAAS,CAACC,MAAM;IACd,IAAI,CAACC,YAAY,CAACC,IAAI,CAACF,MAAM,CAAC;IAE9B,IAAI,CAACzF,YAAY,EAAE;EACrB;EAEAP,yBAAyB;IACvB,IAAI,CAACmG,SAAS,GAAG;MACfC,GAAG,EAAE,IAAI,CAACpI,eAAe,CAACqI,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAI,CAACzI,KAAK,CACP0I,IAAI,CACH,GAAGtJ,WAAW,CAACkC,MAAM,0BAA0B,IAAI,CAACM,KAAK,CAACf,EAAE,EAAE,EAC9D2H,oBAAoB,CACrB,CACAvG,SAAS,CAAE0G,IAAS,IAAI;UACvB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACvI,KAAK,CAACK,OAAO,GAAGiI,IAAI,CAACjI,OAAO,CAACuC,KAAK;UACjD,IAAI,CAAC4F,cAAc,CAACC,IAAI,CAAC,IAAI,CAACF,MAAM,CAAC;UACrCH,QAAQ,CAAC;YACPM,YAAY,EAAEJ,IAAI,CAACI,YAAY;YAC/BC,eAAe,EAAEL,IAAI,CAACK,eAAe;YACrCC,IAAI,EAAEN,IAAI,CAACM;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAACrJ,eAAe,CAACqI,iBAAiB,CAACiB,IAAI;MACrDC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,CACP;QAAEN,IAAI,EAAE,IAAI;QAAEO,OAAO,EAAE;MAAK,CAAE,EAC9B;QACEvI,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,MAAM,CAAC;QAC7C0I,IAAI,EAAE;OACP,EACD;QACEhI,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/C0I,IAAI,EAAE;OACP,EACD;QACEhI,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,QAAQ,CAAC;QAC/C0I,IAAI,EAAE;OACP,EACD;QACEhI,KAAK,EAAE,IAAI,CAAChB,iBAAiB,CAACM,OAAO,CAAC,YAAY,CAAC;QACnD0I,IAAI,EAAE,SAAS;QACfQ,MAAM,EAAE,CAACR,IAAI,EAAE7I,IAAI,EAAEsH,GAAG,KAAI;UAC1B,OAAO,IAAI,CAAC7H,YAAY,CAAC6J,QAAQ,CAAChC,GAAG,CAACiC,IAAI,CAAC;QAC7C;OACD,CACF;MACDC,UAAU,EAAE,CACV,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EACjB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CACrB;MACDC,OAAO,EAAE;QACP1B,GAAG,EAAE,IAAI,CAACpI,eAAe,CAACqI,iBAAiB,CAACyB,OAAO,CAAC1B,GAAG;QACvD0B,OAAO,EAAE,CACP;UACEjD,IAAI,EAAE,oCAAoC,IAAI,CAAC3G,iBAAiB,CAACM,OAAO,CACtE,KAAK,CACN,EAAE;UACHiB,MAAM,EAAE,CAACsI,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,IAAI,CAACxC,MAAM,CAAC,QAAQ,CAAC;UACvB;SACD,EACD;UACEb,IAAI,EAAE,oCAAoC,IAAI,CAAC3G,iBAAiB,CAACM,OAAO,CACtE,MAAM,CACP,EAAE;UACH2J,MAAM,EAAE,gBAAgB;UACxB1I,MAAM,EAAE,CAACsI,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,IAAI,CAACxC,MAAM,CAAC,MAAM,CAAC;UACrB;SACD,EACD;UACEb,IAAI,EAAE,qCAAqC,IAAI,CAAC3G,iBAAiB,CAACM,OAAO,CACvE,QAAQ,CACT,EAAE;UACH2J,MAAM,EAAE,UAAU;UAClB1I,MAAM,EAAE,CAACsI,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,KAAI;YAC9B,IAAI,CAACxC,MAAM,CAAC,QAAQ,CAAC;UACvB;SACD;;KAGN;EACH;EAEA0C,eAAe;IACb,IAAI,IAAI,CAACvI,KAAK,IAAI,IAAI,CAACA,KAAK,CAACxB,IAAI,IAAInB,SAAS,CAAC4C,gBAAgB,CAACC,MAAM,EAAE;MACtEsI,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,SAAS,CAACvB,IAAI,CAAC,IAAI,CAACZ,SAAS,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEAoC,WAAW;IACT,IAAI,IAAI,CAAC1I,KAAK,IAAI,IAAI,CAACA,KAAK,CAACxB,IAAI,IAAInB,SAAS,CAAC4C,gBAAgB,CAACC,MAAM,EAAE;MACtE,IAAI,CAACuI,SAAS,CAACE,WAAW,EAAE;;EAEhC;EAAC;qBAzbUhL,oBAAoB;EAAA;EAAA;UAApBA,oBAAoB;IAAAiL;IAAAC;MAAA;uBAEpBvL,kBAAkB;;;;;;;;;;;;;;;;;;;;;QCxB/BI,2BAAK;QAEDA,sEAyKM;QAENA,qEAiBM;QACRA,iBAAM;QAGRA,uFAee;QAEfA,sHAec;;;QA9NUA,eAAY;QAAZA,uCAAY;QA0K7BA,eAA+D;QAA/DA,4FAA+D;QAqBnEA,eAA+D;QAA/DA,4FAA+D", "names": ["EventEmitter", "AppConfig", "DataTableDirective", "Subject", "environment", "<PERSON><PERSON>", "i0", "StageTablesComponent", "tableData", "value", "_tableData", "constructor", "stageService", "_userService", "_authService", "_commonsService", "_http", "_translateService", "_coreSidebarService", "key", "type", "props", "label", "instant", "placeholder", "required", "options", "defaultValue", "currentUserValue", "id", "max", "editor_id", "table_name", "title", "create", "edit", "remove", "url", "apiUrl", "method", "action", "Map", "alert", "ngOnInit", "stage", "TOURNAMENT_TYPES", "league", "initAdjustmentsPointTable", "checkCanUpdateLeaderboard", "subscribe", "response", "console", "log", "canUpdateLeaderboard", "makeTeamData", "ngOnChanges", "changes", "firstChange", "currentValue", "showUpdateButton", "knockout", "for<PERSON>ach", "group", "groupData", "name", "teams", "mapTeamsEqualPoint", "showButton", "index", "teamId", "buttonDirection", "length", "equalTeams", "mapEqual", "get", "checkCanChangeOrder", "equalPointTeams", "team", "rankingCriteria", "teamCanUp", "teamCanDown", "filteredTeams", "currentTotalPoints", "points", "compareStep", "filter", "t", "RANKING_CRITERIA", "head_to_head", "total", "order", "clear", "Object", "values", "no_matches", "no_wins", "no_draws", "no_losses", "goals_for", "goals_against", "goals_difference", "team_id", "ranking_criteria", "set", "up", "down", "updateOrder", "groupName", "direction", "teamIndex", "findIndex", "currentTeamIndex", "updateOrderChange", "tempOrder", "sort", "a", "b", "orderChange", "currentTeamOrderChange", "makeSubmitOrderData", "map", "stage_id", "submitOrder", "fire", "text", "showCancelButton", "confirmButtonText", "cancelButtonText", "icon", "reverseButtons", "then", "result", "isConfirmed", "submitTeamOrder", "checkMatchScore", "error", "message", "editor", "row", "params", "getSidebarRegistry", "toggle<PERSON><PERSON>", "onSuccess", "$event", "onDataChange", "emit", "dtOptions", "dom", "dataTableDefaults", "select", "rowId", "ajax", "dataTablesParameters", "callback", "post", "resp", "fields", "fields_subject", "next", "recordsTotal", "recordsFiltered", "data", "responsive", "scrollX", "language", "lang", "columnDefs", "columns", "visible", "render", "fullName", "user", "lengthMenu", "buttons", "e", "dt", "node", "config", "extend", "ngAfterViewInit", "setTimeout", "dtTrigger", "ngOnDestroy", "unsubscribe", "selectors", "viewQuery"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-tables\\stage-tables.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-tables\\stage-tables.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { Subject } from 'rxjs';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from 'environments/environment';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AuthService } from 'app/services/auth.service';\r\nimport Swal from 'sweetalert2';\r\nimport { TeamData } from 'app/interfaces/stages';\r\n\r\n@Component({\r\n  selector: 'stage-tables',\r\n  templateUrl: './stage-tables.component.html',\r\n  styleUrls: ['./stage-tables.component.scss']\r\n})\r\nexport class StageTablesComponent implements OnInit, OnChanges {\r\n  AppConfig = AppConfig;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  @Output() onDataChange = new EventEmitter<any>();\r\n\r\n  @Input() showUpdateButton = true;\r\n\r\n  @Input() set tableData(value: any) {\r\n    this._tableData = value;\r\n\r\n    // this.makeTeamData();\r\n\r\n  }\r\n\r\n  get tableData(): any {\r\n    return this._tableData;\r\n  }\r\n\r\n  private _tableData: any;\r\n  @Input() stage: any;\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  dtOptions: any = {};\r\n  public table_name = 'adjust-point-table';\r\n  public fields_subject = new Subject<any>();\r\n  public paramsToPost = {};\r\n  public fields: any[] = [\r\n    {\r\n      key: 'stage_team_id',\r\n      type: 'select',\r\n      props: {\r\n        label: this._translateService.instant('Team'),\r\n        placeholder: this._translateService.instant('Select team'),\r\n        required: true,\r\n        options: []\r\n      }\r\n    },\r\n    {\r\n      key: 'user_id',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        type: 'hidden'\r\n      },\r\n      defaultValue: this._authService.currentUserValue.id\r\n    },\r\n    {\r\n      key: 'points',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Points'),\r\n        placeholder: this._translateService.instant('Enter points'),\r\n        required: true,\r\n        type: 'number',\r\n        max: 1000\r\n      }\r\n    },\r\n    {\r\n      key: 'reason',\r\n      type: 'textarea',\r\n      props: {\r\n        label: this._translateService.instant('Reason'),\r\n        placeholder: this._translateService.instant('Enter reason'),\r\n        required: true\r\n      }\r\n    }\r\n  ];\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Add adjustment point'),\r\n      edit: this._translateService.instant('Edit point'),\r\n      remove: this._translateService.instant('Remove')\r\n    },\r\n    url: `${environment.apiUrl}/adjustment-points/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n\r\n  mapEqual = new Map();\r\n  orderChange = new Map();\r\n\r\n  canUpdateLeaderboard: boolean | null = null;\r\n\r\n  constructor(\r\n    private stageService: StageService,\r\n    public _userService: UserService,\r\n    public _authService: AuthService,\r\n    public _commonsService: CommonsService,\r\n    private _http: HttpClient,\r\n    private _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService\r\n  ) {\r\n  }\r\n\r\n\r\n  groupData = [];\r\n\r\n  ngOnInit(): void {\r\n    if (this.stage) {\r\n      if (this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n        this.initAdjustmentsPointTable();\r\n      }\r\n      this.stageService.checkCanUpdateLeaderboard(this.stage.id).subscribe((response: boolean) => {\r\n        console.log(response);\r\n        this.canUpdateLeaderboard = response;\r\n      });\r\n\r\n      this.makeTeamData();\r\n    }\r\n\r\n\r\n\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['showUpdateButton'] && !changes['showUpdateButton'].firstChange) {\r\n      console.log(\"🚀 ~ StageTablesComponent ~ ngOnChanges ~ changes['showUpdateButton'].currentValue:\", changes['showUpdateButton'].currentValue)\r\n      this.showUpdateButton = changes['showUpdateButton'].currentValue;\r\n    }\r\n\r\n    if (changes['tableData'] && !changes['tableData'].firstChange) {\r\n      if (this.showUpdateButton !== false) {\r\n        this.makeTeamData();\r\n      }\r\n    }\r\n  }\r\n\r\n  makeTeamData() {\r\n\r\n    if (!this.showUpdateButton) {\r\n      return;\r\n    }\r\n\r\n    if (!this.showUpdateButton) {\r\n      return;\r\n    }\r\n\r\n    if (this.tableData && this.stage.type !== AppConfig.TOURNAMENT_TYPES.knockout) {\r\n      this.tableData.forEach((group) => {\r\n        this.groupData[group.name] = group.teams;\r\n      });\r\n    } else {\r\n      this.groupData = this.tableData;\r\n    }\r\n\r\n    this.mapTeamsEqualPoint();\r\n  }\r\n\r\n  showButton(index: number, teamId: number, buttonDirection: 'up' | 'down') {\r\n    if ((buttonDirection === 'up' && index === 0) || (buttonDirection === 'down' && index === this.groupData.length - 1)) {\r\n      return false;\r\n    }\r\n\r\n    const equalTeams = this.mapEqual.get(teamId);\r\n\r\n    if (equalTeams) {\r\n      return equalTeams[buttonDirection];\r\n    }\r\n\r\n    return false;\r\n\r\n  }\r\n\r\n  checkCanChangeOrder(equalPointTeams: TeamData[], team: TeamData, rankingCriteria: string): {\r\n    teamCanUp: TeamData | null;\r\n    teamCanDown: TeamData | null;\r\n  } {\r\n    if (!equalPointTeams?.length) return { teamCanUp: null, teamCanDown: null };\r\n\r\n    let filteredTeams: TeamData[] = [...equalPointTeams];\r\n\r\n    const currentTotalPoints = team.points;\r\n\r\n    const compareStep = (teams: TeamData[], key: keyof TeamData): TeamData[] => {\r\n      return teams.filter(t => t[key] === team[key]);\r\n    };\r\n\r\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.head_to_head) {\r\n      filteredTeams = compareStep(filteredTeams, '_h2h_points');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_difference');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\r\n    }\r\n\r\n    if (rankingCriteria === AppConfig.RANKING_CRITERIA.total) {\r\n      filteredTeams = compareStep(filteredTeams, 'goals_difference');\r\n\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, 'goals_for');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_points');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_gd');\r\n      if (filteredTeams.length > 1) filteredTeams = compareStep(filteredTeams, '_h2h_goals');\r\n    }\r\n\r\n    const teamCanUp = filteredTeams.filter(t => t.order < team.order);\r\n    const teamCanDown = filteredTeams.filter(t => t.order > team.order);\r\n\r\n    return {\r\n      teamCanUp: teamCanUp.length > 0 ? teamCanUp[teamCanUp.length - 1] : null,\r\n      teamCanDown: teamCanDown.length > 0 ? teamCanDown[0] : null\r\n    };\r\n  }\r\n\r\n  mapTeamsEqualPoint() {\r\n\r\n    this.mapEqual.clear();\r\n\r\n\r\n    Object.values(this.groupData).forEach(group => {\r\n      group.forEach((team) => {\r\n\r\n        const equalPointTeams = group.filter(t =>\r\n          t.points === team.points &&\r\n          t.no_matches === team.no_matches &&\r\n          t.no_wins === team.no_wins &&\r\n          t.no_draws === team.no_draws &&\r\n          t.no_losses === team.no_losses &&\r\n          t.goals_for === team.goals_for &&\r\n          t.goals_against === team.goals_against &&\r\n          t.goals_difference === team.goals_difference &&\r\n          t.team_id !== team.team_id\r\n        );\r\n\r\n        const {\r\n          teamCanUp,\r\n          teamCanDown\r\n        } = this.checkCanChangeOrder(equalPointTeams, team, this.stage.ranking_criteria);\r\n\r\n        this.mapEqual.set(team.team_id, {\r\n          up: teamCanUp,\r\n          down: teamCanDown\r\n        });\r\n      });\r\n\r\n\r\n    });\r\n    console.log('this.mapEqual', this.mapEqual);\r\n    return this.mapEqual;\r\n\r\n  }\r\n\r\n  updateOrder(groupName: string, teamId: number, direction: 'up' | 'down') {\r\n    const equalTeams = this.mapEqual.get(teamId);\r\n\r\n    if (equalTeams) {\r\n      const team = equalTeams[direction];\r\n\r\n      const teamIndex = this.groupData[groupName].findIndex(t => t.team_id === team.team_id);\r\n      const currentTeamIndex = this.groupData[groupName].findIndex(t => t.team_id === teamId);\r\n\r\n      if (teamIndex !== -1 && currentTeamIndex !== -1) {\r\n\r\n        this.updateOrderChange(groupName, this.groupData[groupName][currentTeamIndex].team_id, direction);\r\n        this.updateOrderChange(groupName, this.groupData[groupName][teamIndex].team_id, direction === 'up' ? 'down' : 'up');\r\n\r\n        // Swap the order of the two teams\r\n        const tempOrder = this.groupData[groupName][teamIndex].order;\r\n        this.groupData[groupName][teamIndex].order = this.groupData[groupName][currentTeamIndex].order;\r\n        this.groupData[groupName][currentTeamIndex].order = tempOrder;\r\n\r\n        this.groupData[groupName] = this.groupData[groupName].sort((a, b) => a.order - b.order);\r\n\r\n        // Update the map\r\n        this.mapTeamsEqualPoint();\r\n      }\r\n    }\r\n    ;\r\n\r\n  }\r\n\r\n  updateOrderChange(groupName: string, teamId: number, direction: 'up' | 'down') {\r\n\r\n\r\n    if (!this.orderChange.get(groupName)) {\r\n      this.orderChange.set(groupName, new Map());\r\n    }\r\n\r\n    const currentTeamOrderChange = this.orderChange.get(groupName).get(teamId) || 0;\r\n\r\n    if (direction === 'up') {\r\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange + 1);\r\n    } else if (direction === 'down') {\r\n      this.orderChange.get(groupName).set(teamId, currentTeamOrderChange - 1);\r\n    }\r\n  }\r\n\r\n  makeSubmitOrderData(groupName: string) {\r\n    return this.groupData[groupName].map((team) => ({\r\n      team_id: team.team_id,\r\n      order: team.order,\r\n      stage_id: this.stage.id\r\n    }));\r\n  }\r\n\r\n  submitOrder(groupName: string) {\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Submit new Leaderboard'),\r\n      text: this._translateService.instant('Are you sure you want to submit the new Leaderboard?'),\r\n      showCancelButton: true,\r\n      confirmButtonText: this._translateService.instant('Submit'),\r\n      cancelButtonText: this._translateService.instant('No'),\r\n      icon: 'warning',\r\n      reverseButtons: true\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.stageService.submitTeamOrder(this.makeSubmitOrderData(groupName)).subscribe((response) => {\r\n          console.log('Order submitted successfully:', response);\r\n          Swal.fire({\r\n            title: this._translateService.instant('Success'),\r\n            text: this._translateService.instant('The new Leaderboard has been submitted successfully.'),\r\n            icon: 'success',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n          this.orderChange.get(groupName).clear();\r\n          this.stageService.checkMatchScore(this.stage.id).subscribe();\r\n        }, (error) => {\r\n          console.error('Error submitting order:', error);\r\n          Swal.fire({\r\n            title: this._translateService.instant('Error'),\r\n            text: this._translateService.instant(error.message),\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK')\r\n          });\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  onSuccess($event) {\r\n    this.onDataChange.emit($event);\r\n\r\n    this.makeTeamData();\r\n  }\r\n\r\n  initAdjustmentsPointTable() {\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        // add season id\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/adjustment-points/all/${this.stage.id}`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            this.fields[0].props.options = resp.options.teams;\r\n            this.fields_subject.next(this.fields);\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [],\r\n      columns: [\r\n        { data: 'id', visible: false },\r\n        {\r\n          title: this._translateService.instant('Team'),\r\n          data: 'team.name'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Points'),\r\n          data: 'points'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Reason'),\r\n          data: 'reason'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Updated by'),\r\n          data: 'user_id',\r\n          render: (data, type, row) => {\r\n            return this._userService.fullName(row.user);\r\n          }\r\n        }\r\n      ],\r\n      lengthMenu: [\r\n        [25, 50, 100, -1],\r\n        [25, 50, 100, 'All']\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa-solid fa-plus\"></i> ${this._translateService.instant(\r\n              'Add'\r\n            )}`,\r\n            action: (e, dt, node, config) => {\r\n              this.editor('create');\r\n            }\r\n          },\r\n          {\r\n            text: `<i class=\"fa-solid fa-edit\"></i> ${this._translateService.instant(\r\n              'Edit'\r\n            )}`,\r\n            extend: 'selectedSingle',\r\n            action: (e, dt, node, config) => {\r\n              this.editor('edit');\r\n            }\r\n          },\r\n          {\r\n            text: `<i class=\"fa-solid fa-trash\"></i> ${this._translateService.instant(\r\n              'Remove'\r\n            )}`,\r\n            extend: 'selected',\r\n            action: (e, dt, node, config) => {\r\n              this.editor('remove');\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n      setTimeout(() => {\r\n        this.dtTrigger.next(this.dtOptions);\r\n      }, 1000);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.stage && this.stage.type == AppConfig.TOURNAMENT_TYPES.league) {\r\n      this.dtTrigger.unsubscribe();\r\n    }\r\n  }\r\n\r\n\r\n  protected readonly alert = alert;\r\n}\r\n", "<div>\r\n  <div class=\"row\">\r\n    <div\r\n      class=\"col  {{ tableData.length > 1 ? 'col-lg-6' : '' }}\"\r\n      *ngFor=\"let group of tableData\"\r\n    >\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h4 class=\"card-title\">{{ group.name }}</h4>\r\n        </div>\r\n        <div class=\"card-body\"></div>\r\n        <div class=\"table-responsive\">\r\n          <table class=\"table\">\r\n            <thead>\r\n              <tr>\r\n                <th>#</th>\r\n                <th>Logo</th>\r\n                <th>Team</th>\r\n                <th>P</th>\r\n                <th class=\"d-none d-sm-table-cell\">W</th>\r\n                <th class=\"d-none d-sm-table-cell\">D</th>\r\n                <th class=\"d-none d-sm-table-cell\">L</th>\r\n                <th>F</th>\r\n                <th>A</th>\r\n                <th>Pts</th>\r\n                <th\r\n                  class=\"text-center\"\r\n                  *ngIf=\"\r\n                    showUpdateButton &&\r\n                    stage &&\r\n                    stage.type !== AppConfig.TOURNAMENT_TYPES.knockout\r\n                  \"\r\n                >\r\n                  Action\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <ng-container\r\n                *ngIf=\"\r\n                  stage && stage.type !== AppConfig.TOURNAMENT_TYPES.knockout\r\n                \"\r\n              >\r\n                <tr *ngFor=\"let stageTeam of groupData[group.name]; index as i\">\r\n                  <td>\r\n                    <div class=\"d-flex align-items-center\" style=\"gap: 4px\">\r\n                      <span>\r\n                        {{ i + 1 }}\r\n                      </span>\r\n                      <i\r\n                        class=\"text-danger bi bi-caret-down-fill\"\r\n                        *ngIf=\"\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) &&\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) < 0\r\n                        \"\r\n                      ></i>\r\n                      <i\r\n                        class=\"text-success bi bi-caret-up-fill\"\r\n                        *ngIf=\"\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) &&\r\n                          (orderChange.get(group.name)\r\n                            ? orderChange.get(group.name).get(stageTeam.team_id)\r\n                            : null) > 0\r\n                        \"\r\n                      ></i>\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <img\r\n                      [src]=\"stageTeam.team.club.logo\"\r\n                      alt=\"logo\"\r\n                      width=\"30\"\r\n                      height=\"30\"\r\n                    />\r\n                  </td>\r\n                  <td class=\"font-weight-bold\">{{ stageTeam.team.name }}</td>\r\n                  <td>{{ stageTeam.no_matches }}</td>\r\n                  <td class=\"d-none d-sm-table-cell\">\r\n                    {{ stageTeam.no_wins }}\r\n                  </td>\r\n                  <td class=\"d-none d-sm-table-cell\">\r\n                    {{ stageTeam.no_draws }}\r\n                  </td>\r\n                  <td class=\"d-none d-sm-table-cell\">\r\n                    {{ stageTeam.no_losses }}\r\n                  </td>\r\n                  <td>{{ stageTeam.goals_for }}</td>\r\n                  <td>{{ stageTeam.goals_against }}</td>\r\n                  <td class=\"font-weight-bold\">{{ stageTeam.points }}</td>\r\n                  <td\r\n                    class=\"font-weight-bold text-center\"\r\n                    *ngIf=\"showUpdateButton\"\r\n                  >\r\n                    <div\r\n                      class=\"d-flex align-items-center justify-content-center\"\r\n                      style=\"gap: 4px\"\r\n                    >\r\n                      <button\r\n                        [ngClass]=\"\r\n                          showButton(i, stageTeam.team_id, 'up')\r\n                            ? 'visible'\r\n                            : 'invisible'\r\n                        \"\r\n                        class=\"btn btn-outline-success\"\r\n                        style=\"padding: 4px\"\r\n                        (click)=\"\r\n                          updateOrder(group.name, stageTeam.team_id, 'up')\r\n                        \"\r\n                        [disabled]=\"!canUpdateLeaderboard\"\r\n                      >\r\n                        <i class=\"text-success bi bi-arrow-up\"></i>\r\n                      </button>\r\n                      <button\r\n                        [ngClass]=\"\r\n                          showButton(i, stageTeam.team_id, 'down')\r\n                            ? 'visible'\r\n                            : 'invisible'\r\n                        \"\r\n                        class=\"btn btn-outline-danger\"\r\n                        style=\"padding: 4px\"\r\n                        (click)=\"\r\n                          updateOrder(group.name, stageTeam.team_id, 'down')\r\n                        \"\r\n                        [disabled]=\"!canUpdateLeaderboard\"\r\n                      >\r\n                        <i class=\"text-danger bi bi-arrow-down\"></i>\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </ng-container>\r\n              <ng-container\r\n                *ngIf=\"\r\n                  !stage ||\r\n                  (stage && stage.type === AppConfig.TOURNAMENT_TYPES.knockout)\r\n                \"\r\n                [ngTemplateOutlet]=\"notLeagueTemplate\"\r\n                [ngTemplateOutletContext]=\"{ group: group }\"\r\n              ></ng-container>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n\r\n        <div\r\n          class=\"mt-1 mr-1 mb-3 d-flex flex-column align-items-end\"\r\n          *ngIf=\"showUpdateButton\"\r\n        >\r\n          <p\r\n            *ngIf=\"canUpdateLeaderboard === false\"\r\n            class=\"h5 mw-100 badge badge-light-warning px-2 py-1\"\r\n            style=\"font-size: 12px\"\r\n          >\r\n            You cannot update the leaderboard once the knockout stage has\r\n            started.\r\n          </p>\r\n          <button\r\n            class=\"btn btn-primary mw-100\"\r\n            (click)=\"submitOrder(group.name)\"\r\n            *ngIf=\"stage && stage.type !== AppConfig.TOURNAMENT_TYPES.knockout\"\r\n            [disabled]=\"!canUpdateLeaderboard\"\r\n          >\r\n            Submit\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div\r\n      *ngIf=\"stage && stage.type === AppConfig.TOURNAMENT_TYPES.league\"\r\n      class=\"col-lg-6\"\r\n    >\r\n      <div class=\"card\">\r\n        <div class=\"card-header\">\r\n          <h4>\r\n            {{ 'Point Adjustment' | translate }}\r\n          </h4>\r\n        </div>\r\n        <table\r\n          datatable\r\n          [dtOptions]=\"dtOptions\"\r\n          [dtTrigger]=\"dtTrigger\"\r\n          class=\"table row-border hover\"\r\n        ></table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<core-sidebar\r\n  *ngIf=\"stage && stage.type === AppConfig.TOURNAMENT_TYPES.league\"\r\n  class=\"modal modal-slide-in sidebar-todo-modal fade\"\r\n  [name]=\"table_name\"\r\n  overlayClass=\"modal-backdrop\"\r\n>\r\n  <app-editor-sidebar\r\n    [table]=\"dtElement\"\r\n    [fields]=\"fields\"\r\n    [params]=\"params\"\r\n    (onSuccess)=\"onSuccess($event)\"\r\n    [paramsToPost]=\"paramsToPost\"\r\n    [fields_subject]=\"fields_subject\"\r\n  >\r\n  </app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #notLeagueTemplate let-group=\"group\">\r\n  <tr *ngFor=\"let stageTeam of group.teams; index as i\">\r\n    <td>{{ i + 1 }}</td>\r\n    <td>\r\n      <img [src]=\"stageTeam.team.club.logo\" alt=\"logo\" width=\"30\" height=\"30\" />\r\n    </td>\r\n    <td class=\"font-weight-bold\">{{ stageTeam.team.name }}</td>\r\n    <td>{{ stageTeam.no_matches }}</td>\r\n    <td class=\"d-none d-sm-table-cell\">{{ stageTeam.no_wins }}</td>\r\n    <td class=\"d-none d-sm-table-cell\">{{ stageTeam.no_draws }}</td>\r\n    <td class=\"d-none d-sm-table-cell\">{{ stageTeam.no_losses }}</td>\r\n    <td>{{ stageTeam.goals_for }}</td>\r\n    <td>{{ stageTeam.goals_against }}</td>\r\n    <td class=\"font-weight-bold\">{{ stageTeam.points }}</td>\r\n  </tr>\r\n</ng-template>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}