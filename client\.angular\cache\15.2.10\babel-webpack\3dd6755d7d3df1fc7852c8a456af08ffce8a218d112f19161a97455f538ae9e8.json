{"ast": null, "code": "import { environment } from 'environments/environment';\nimport { throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TournamentService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getAllTournaments() {\n    return this._http.get(`${environment.apiUrl}/tournaments/getAllTournaments`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getTournament(id) {\n    return this._http.get(`${environment.apiUrl}/tournaments/${id}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getTournamentMatches(id) {\n    return this._http.get(`${environment.apiUrl}/tournaments/${id}/matches`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getMatchesWithQuery(season_id, query = '') {\n    return this._http.get(`${environment.apiUrl}/tournaments/season/${season_id}/matches-user${query}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(() => err);\n    }));\n  }\n  getMatchById(id) {\n    return this._http.get(`${environment.apiUrl}/stage-matches/${id}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  updateMatch(data, typeAction = null) {\n    // console.log(data);\n    if (data.id) {\n      // new form data\n      const formData = new FormData();\n      for (const key in data) {\n        let value = data[key];\n        if (value === null) value = '';\n        formData.append(`data[${data.id}][${key}]`, value);\n      }\n      formData.append('action', 'edit');\n      if (typeAction) {\n        formData.append('type_action', typeAction);\n      }\n      return this._http.post(`${environment.apiUrl}/stage-matches/editor`, formData).pipe(map(res => {\n        return res;\n      }), catchError(err => {\n        return throwError(err);\n      }));\n    }\n  }\n  getMatchDetails(match_id) {\n    return this._http.get(`${environment.apiUrl}/stage-matches/${match_id}/details`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  updateMatchDetails(data) {\n    if (data.match_id) {\n      return this._http.put(`${environment.apiUrl}/stage-matches/${data.match_id}/details`, data).pipe(map(res => {\n        return res;\n      }), catchError(err => {\n        return throwError(err);\n      }));\n    }\n  }\n  deleteMatchDetails(match_detail_id) {\n    return this._http.delete(`${environment.apiUrl}/stage-matches/details/${match_detail_id}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  showMatchesFixturesBySeason(season_id, params = {}) {\n    // remove null params\n    for (const key in params) {\n      if (params[key] === null) {\n        delete params[key];\n      }\n    }\n    return this._http.get(`${environment.apiUrl}/tournaments/season/${season_id}/fixtures`, {\n      params\n    }).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  checkMatchExist(match_id) {\n    return this._http.get(`${environment.apiUrl}/stage-matches/${match_id}/exist`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getFixturesResultsByTournament(tournament_id, params = {}) {\n    // remove null params\n    for (const key in params) {\n      if (params[key] === null) {\n        delete params[key];\n      }\n    }\n    return this._http.get(`${environment.apiUrl}/tournaments/${tournament_id}/fixtures-results`, {\n      params\n    }).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  fixturesResultsByTeam(team_id, params = {}) {\n    // remove null params\n    for (const key in params) {\n      if (params[key] === null) {\n        delete params[key];\n      }\n    }\n    return this._http.get(`${environment.apiUrl}/tournaments/team/${team_id}/fixtures-results`, {\n      params\n    }).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  // update-score\n  updateScore(data) {\n    if (data.match_id) {\n      return this._http.post(`${environment.apiUrl}/stage-matches/update-score`, data).pipe(map(res => {\n        return res;\n      }), catchError(err => {\n        return throwError(err);\n      }));\n    }\n  }\n  // update-broadcast-status\n  updateBroadcastStatus(data) {\n    if (data.match_id) {\n      return this._http.post(`${environment.apiUrl}/stage-matches/update-broadcast-status`, data).pipe(map(res => {\n        return res;\n      }), catchError(err => {\n        return throwError(err);\n      }));\n    }\n  }\n  // updateBroadcastData\n  updateBroadcastData(data) {\n    if (data.match_id) {\n      return this._http.post(`${environment.apiUrl}/stage-matches/update-broadcast-data`, data).pipe(map(res => {\n        return res;\n      }), catchError(err => {\n        return throwError(err);\n      }));\n    }\n  }\n  // get user to assign to match\n  getUsersToAssign() {\n    return this._http.get(`${environment.apiUrl}/match-streaming/users`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  // assign user to match\n  assignUserToMatch(data) {\n    return this._http.post(`${environment.apiUrl}/match-streaming/assign`, data).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  // get assigned users\n  getAssignedUsers(match_id) {\n    return this._http.get(`${environment.apiUrl}/match-streaming/${match_id}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  // show user assigned to match\n  showUserAssignedToMatch(match_id) {\n    return this._http.get(`${environment.apiUrl}/match-streaming/${match_id}/show`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getTournamentToRelease(season_id) {\n    return this._http.post(`${environment.apiUrl}/tournaments/all-in-group-sort`, {\n      season_id: season_id\n    }).pipe(map(res => {\n      // const allOrdersZero = res.data.every(\n      //   (tournament) => tournament.order === 0\n      // );\n      // console.log(allOrdersZero, 'allOrdersZero');\n      // if (allOrdersZero) {\n      //   res.data.sort((a, b) => {\n      //     return a.name.localeCompare(b.name, undefined, {\n      //       numeric: true,\n      //       sensitivity: 'base',\n      //     });\n      //   });\n      // } else {\n      //   res.data.sort((a, b) => {\n      //     return a.order - b.order;\n      //   });\n      // }\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  updateTournamentOder(tournaments) {\n    return this._http.post(`${environment.apiUrl}/settings/update-tournaments-order`, {\n      tournaments\n    }).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  generateIcs(data) {\n    return this._http.post(`${environment.apiUrl}/locations/generateIcs`, data, {\n      responseType: 'blob'\n    }).pipe(catchError(err => {\n      return throwError(err);\n    }));\n  }\n  assignRoleToUser(data) {\n    return this._http.post(`${environment.apiUrl}/match-streaming/assign-role`, data).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  static #_ = this.ɵfac = function TournamentService_Factory(t) {\n    return new (t || TournamentService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TournamentService,\n    factory: TournamentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAMhD,OAAM,MAAOC,iBAAiB;EAC5BC,YAAmBC,KAAiB;IAAjB,UAAK,GAALA,KAAK;EACxB;EAEAC,iBAAiB;IACf,OAAO,IAAI,CAACD,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,gCAAgC,CAAC,CAC1DC,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAC,aAAa,CAACC,EAAE;IACd,OAAO,IAAI,CAACR,KAAK,CAACE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,gBAAgBK,EAAE,EAAE,CAAC,CAACJ,IAAI,CACnER,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACH;EAEAG,oBAAoB,CAACD,EAAE;IACrB,OAAO,IAAI,CAACR,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,gBAAgBK,EAAE,UAAU,CAAC,CACtDJ,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAI,mBAAmB,CAACC,SAAiB,EAAEC,KAAK,GAAG,EAAE;IAC/C,OAAO,IAAI,CAACZ,KAAK,CACdE,GAAG,CACF,GAAGR,WAAW,CAACS,MAAM,uBAAuBQ,SAAS,gBAAgBC,KAAK,EAAE,CAC7E,CACAR,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAAC,MAAMW,GAAG,CAAC;IAC9B,CAAC,CAAC,CACH;EACL;EAEAO,YAAY,CAACL,EAAE;IACb,OAAO,IAAI,CAACR,KAAK,CAACE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,kBAAkBK,EAAE,EAAE,CAAC,CAACJ,IAAI,CACrER,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACH;EAEAQ,WAAW,CAACC,IAAI,EAAEC,aAAiD,IAAI;IACrE;IACA,IAAID,IAAI,CAACP,EAAE,EAAE;MACX;MACA,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,KAAK,MAAMC,GAAG,IAAIJ,IAAI,EAAE;QACtB,IAAIK,KAAK,GAAGL,IAAI,CAACI,GAAG,CAAC;QACrB,IAAIC,KAAK,KAAK,IAAI,EAAEA,KAAK,GAAG,EAAE;QAC9BH,QAAQ,CAACI,MAAM,CAAC,QAAQN,IAAI,CAACP,EAAE,KAAKW,GAAG,GAAG,EAAEC,KAAK,CAAC;;MAEpDH,QAAQ,CAACI,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;MAEjC,IAAIL,UAAU,EAAE;QACdC,QAAQ,CAACI,MAAM,CAAC,aAAa,EAAEL,UAAU,CAAC;;MAE5C,OAAO,IAAI,CAAChB,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,uBAAuB,EAAEc,QAAQ,CAAC,CAC5Db,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;QACf,OAAOA,GAAG;MACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;QACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;MACxB,CAAC,CAAC,CACH;;EAEP;EAGAiB,eAAe,CAACC,QAAQ;IACtB,OAAO,IAAI,CAACxB,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,kBAAkBqB,QAAQ,UAAU,CAAC,CAC9DpB,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAmB,kBAAkB,CAACV,IAAI;IACrB,IAAIA,IAAI,CAACS,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACxB,KAAK,CACd0B,GAAG,CACF,GAAGhC,WAAW,CAACS,MAAM,kBAAkBY,IAAI,CAACS,QAAQ,UAAU,EAC9DT,IAAI,CACL,CACAX,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;QACf,OAAOA,GAAG;MACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;QACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;MACxB,CAAC,CAAC,CACH;;EAEP;EAEAqB,kBAAkB,CAACC,eAAe;IAChC,OAAO,IAAI,CAAC5B,KAAK,CACd6B,MAAM,CAAC,GAAGnC,WAAW,CAACS,MAAM,0BAA0ByB,eAAe,EAAE,CAAC,CACxExB,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAwB,2BAA2B,CAACnB,SAAS,EAAEoB,MAAM,GAAG,EAAE;IAChD;IACA,KAAK,MAAMZ,GAAG,IAAIY,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACZ,GAAG,CAAC,KAAK,IAAI,EAAE;QACxB,OAAOY,MAAM,CAACZ,GAAG,CAAC;;;IAGtB,OAAO,IAAI,CAACnB,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,uBAAuBQ,SAAS,WAAW,EAAE;MACrEoB;KACD,CAAC,CACD3B,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA0B,eAAe,CAACR,QAAQ;IACtB,OAAO,IAAI,CAACxB,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,kBAAkBqB,QAAQ,QAAQ,CAAC,CAC5DpB,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA2B,8BAA8B,CAACC,aAAa,EAAEH,MAAM,GAAG,EAAE;IACvD;IACA,KAAK,MAAMZ,GAAG,IAAIY,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACZ,GAAG,CAAC,KAAK,IAAI,EAAE;QACxB,OAAOY,MAAM,CAACZ,GAAG,CAAC;;;IAGtB,OAAO,IAAI,CAACnB,KAAK,CACdE,GAAG,CACF,GAAGR,WAAW,CAACS,MAAM,gBAAgB+B,aAAa,mBAAmB,EACrE;MAAEH;IAAM,CAAE,CACX,CACA3B,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA6B,qBAAqB,CAACC,OAAO,EAAEL,MAAM,GAAG,EAAE;IACxC;IACA,KAAK,MAAMZ,GAAG,IAAIY,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACZ,GAAG,CAAC,KAAK,IAAI,EAAE;QACxB,OAAOY,MAAM,CAACZ,GAAG,CAAC;;;IAGtB,OAAO,IAAI,CAACnB,KAAK,CACdE,GAAG,CACF,GAAGR,WAAW,CAACS,MAAM,qBAAqBiC,OAAO,mBAAmB,EACpE;MACEL;KACD,CACF,CACA3B,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA;EACA+B,WAAW,CAACtB,IAA0C;IACpD,IAAIA,IAAI,CAACS,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACxB,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,6BAA6B,EAAEY,IAAI,CAAC,CAC9DX,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;QACf,OAAOA,GAAG;MACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;QACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;MACxB,CAAC,CAAC,CACH;;EAEP;EAEA;EACAgC,qBAAqB,CAACvB,IAAoC;IACxD,IAAIA,IAAI,CAACS,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACxB,KAAK,CACdsB,IAAI,CACH,GAAG5B,WAAW,CAACS,MAAM,wCAAwC,EAC7DY,IAAI,CACL,CACAX,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;QACf,OAAOA,GAAG;MACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;QACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;MACxB,CAAC,CAAC,CACH;;EAEP;EAEA;EACAiC,mBAAmB,CAACxB,IAAkC;IACpD,IAAIA,IAAI,CAACS,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACxB,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,sCAAsC,EAAEY,IAAI,CAAC,CACvEX,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;QACf,OAAOA,GAAG;MACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;QACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;MACxB,CAAC,CAAC,CACH;;EAEP;EAEA;EACAkC,gBAAgB;IACd,OAAO,IAAI,CAACxC,KAAK,CAACE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,wBAAwB,CAAC,CAACC,IAAI,CACvER,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAmC,iBAAiB,CAAC1B,IAAI;IACpB,OAAO,IAAI,CAACf,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,yBAAyB,EAAEY,IAAI,CAAC,CAC1DX,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA;EACAoC,gBAAgB,CAAClB,QAAQ;IACvB,OAAO,IAAI,CAACxB,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,oBAAoBqB,QAAQ,EAAE,CAAC,CACxDpB,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA;EACAqC,uBAAuB,CAACnB,QAAQ;IAC9B,OAAO,IAAI,CAACxB,KAAK,CACdE,GAAG,CAAC,GAAGR,WAAW,CAACS,MAAM,oBAAoBqB,QAAQ,OAAO,CAAC,CAC7DpB,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAsC,sBAAsB,CAACjC,SAAS;IAC9B,OAAO,IAAI,CAACX,KAAK,CACdsB,IAAI,CACH,GAAG5B,WAAW,CAACS,MAAM,gCAAgC,EAAE;MAAEQ,SAAS,EAAEA;IAAS,CAAE,CAChF,CACAP,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAuC,oBAAoB,CAACC,WAAW;IAC9B,OAAO,IAAI,CAAC9C,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,oCAAoC,EAAE;MAC/D2C;KACD,CAAC,CACD1C,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAyC,WAAW,CAAChC,IAAI;IACd,OAAO,IAAI,CAACf,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,wBAAwB,EAAEY,IAAI,EAAE;MAAEiC,YAAY,EAAE;IAAM,CAAE,CAAC,CACnF5C,IAAI,CACHP,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA2C,gBAAgB,CAAClC,IAAI;IACnB,OAAO,IAAI,CAACf,KAAK,CACdsB,IAAI,CAAC,GAAG5B,WAAW,CAACS,MAAM,8BAA8B,EAAEY,IAAI,CAAC,CAC/DX,IAAI,CACHR,GAAG,CAAES,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFR,UAAU,CAAES,GAAG,IAAI;MACjB,OAAOX,UAAU,CAACW,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAAC;qBAxYUR,iBAAiB;EAAA;EAAA;WAAjBA,iBAAiB;IAAAoD,SAAjBpD,iBAAiB;IAAAqD,YAFhB;EAAM", "names": ["environment", "throwError", "map", "catchError", "TournamentService", "constructor", "_http", "getAllTournaments", "get", "apiUrl", "pipe", "res", "err", "getTournament", "id", "getTournamentMatches", "getMatchesWithQuery", "season_id", "query", "getMatchById", "updateMatch", "data", "typeAction", "formData", "FormData", "key", "value", "append", "post", "getMatchDetails", "match_id", "updateMatchDetails", "put", "deleteMatchDetails", "match_detail_id", "delete", "showMatchesFixturesBySeason", "params", "checkMatchExist", "getFixturesResultsByTournament", "tournament_id", "fixturesResultsByTeam", "team_id", "updateScore", "updateBroadcastStatus", "updateBroadcastData", "getUsersToAssign", "assignUserToMatch", "getAssignedUsers", "showUserAssignedToMatch", "getTournamentToRelease", "updateTournamentOder", "tournaments", "generateIcs", "responseType", "assignRoleToUser", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\services\\tournament.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\nimport { query } from '@angular/animations';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TournamentService {\r\n  constructor(public _http: HttpClient) {\r\n  }\r\n\r\n  getAllTournaments() {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/tournaments/getAllTournaments`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getTournament(id) {\r\n    return this._http.get(`${environment.apiUrl}/tournaments/${id}`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      }),\r\n      catchError((err) => {\r\n        return throwError(err);\r\n      })\r\n    );\r\n  }\r\n\r\n  getTournamentMatches(id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/tournaments/${id}/matches`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getMatchesWithQuery(season_id: number, query = '') {\r\n    return this._http\r\n      .get(\r\n        `${environment.apiUrl}/tournaments/season/${season_id}/matches-user${query}`\r\n      )\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(() => err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getMatchById(id) {\r\n    return this._http.get(`${environment.apiUrl}/stage-matches/${id}`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      }),\r\n      catchError((err) => {\r\n        return throwError(err);\r\n      })\r\n    );\r\n  }\r\n\r\n  updateMatch(data, typeAction: 'editMatch' | 'updateScore' | null = null) {\r\n    // console.log(data);\r\n    if (data.id) {\r\n      // new form data\r\n      const formData = new FormData();\r\n      for (const key in data) {\r\n        let value = data[key];\r\n        if (value === null) value = '';\r\n        formData.append(`data[${data.id}][${key}]`, value);\r\n      }\r\n      formData.append('action', 'edit');\r\n\r\n      if (typeAction) {\r\n        formData.append('type_action', typeAction);\r\n      }\r\n      return this._http\r\n        .post(`${environment.apiUrl}/stage-matches/editor`, formData)\r\n        .pipe(\r\n          map((res: any) => {\r\n            return res;\r\n          }),\r\n          catchError((err) => {\r\n            return throwError(err);\r\n          })\r\n        );\r\n    }\r\n  }\r\n\r\n\r\n  getMatchDetails(match_id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stage-matches/${match_id}/details`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  updateMatchDetails(data) {\r\n    if (data.match_id) {\r\n      return this._http\r\n        .put(\r\n          `${environment.apiUrl}/stage-matches/${data.match_id}/details`,\r\n          data\r\n        )\r\n        .pipe(\r\n          map((res: any) => {\r\n            return res;\r\n          }),\r\n          catchError((err) => {\r\n            return throwError(err);\r\n          })\r\n        );\r\n    }\r\n  }\r\n\r\n  deleteMatchDetails(match_detail_id) {\r\n    return this._http\r\n      .delete(`${environment.apiUrl}/stage-matches/details/${match_detail_id}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  showMatchesFixturesBySeason(season_id, params = {}) {\r\n    // remove null params\r\n    for (const key in params) {\r\n      if (params[key] === null) {\r\n        delete params[key];\r\n      }\r\n    }\r\n    return this._http\r\n      .get(`${environment.apiUrl}/tournaments/season/${season_id}/fixtures`, {\r\n        params\r\n      })\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  checkMatchExist(match_id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stage-matches/${match_id}/exist`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getFixturesResultsByTournament(tournament_id, params = {}) {\r\n    // remove null params\r\n    for (const key in params) {\r\n      if (params[key] === null) {\r\n        delete params[key];\r\n      }\r\n    }\r\n    return this._http\r\n      .get(\r\n        `${environment.apiUrl}/tournaments/${tournament_id}/fixtures-results`,\r\n        { params }\r\n      )\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  fixturesResultsByTeam(team_id, params = {}) {\r\n    // remove null params\r\n    for (const key in params) {\r\n      if (params[key] === null) {\r\n        delete params[key];\r\n      }\r\n    }\r\n    return this._http\r\n      .get(\r\n        `${environment.apiUrl}/tournaments/team/${team_id}/fixtures-results`,\r\n        {\r\n          params\r\n        }\r\n      )\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  // update-score\r\n  updateScore(data: { match_id; home_score; away_score }) {\r\n    if (data.match_id) {\r\n      return this._http\r\n        .post(`${environment.apiUrl}/stage-matches/update-score`, data)\r\n        .pipe(\r\n          map((res: any) => {\r\n            return res;\r\n          }),\r\n          catchError((err) => {\r\n            return throwError(err);\r\n          })\r\n        );\r\n    }\r\n  }\r\n\r\n  // update-broadcast-status\r\n  updateBroadcastStatus(data: { match_id; broadcast_status }) {\r\n    if (data.match_id) {\r\n      return this._http\r\n        .post(\r\n          `${environment.apiUrl}/stage-matches/update-broadcast-status`,\r\n          data\r\n        )\r\n        .pipe(\r\n          map((res: any) => {\r\n            return res;\r\n          }),\r\n          catchError((err) => {\r\n            return throwError(err);\r\n          })\r\n        );\r\n    }\r\n  }\r\n\r\n  // updateBroadcastData\r\n  updateBroadcastData(data: { match_id; broadcast_data }) {\r\n    if (data.match_id) {\r\n      return this._http\r\n        .post(`${environment.apiUrl}/stage-matches/update-broadcast-data`, data)\r\n        .pipe(\r\n          map((res: any) => {\r\n            return res;\r\n          }),\r\n          catchError((err) => {\r\n            return throwError(err);\r\n          })\r\n        );\r\n    }\r\n  }\r\n\r\n  // get user to assign to match\r\n  getUsersToAssign() {\r\n    return this._http.get(`${environment.apiUrl}/match-streaming/users`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      }),\r\n      catchError((err) => {\r\n        return throwError(err);\r\n      })\r\n    );\r\n  }\r\n\r\n  // assign user to match\r\n  assignUserToMatch(data) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/match-streaming/assign`, data)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  // get assigned users\r\n  getAssignedUsers(match_id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/match-streaming/${match_id}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  // show user assigned to match\r\n  showUserAssignedToMatch(match_id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/match-streaming/${match_id}/show`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getTournamentToRelease(season_id) {\r\n    return this._http\r\n      .post(\r\n        `${environment.apiUrl}/tournaments/all-in-group-sort`, { season_id: season_id }\r\n      )\r\n      .pipe(\r\n        map((res: any) => {\r\n          // const allOrdersZero = res.data.every(\r\n          //   (tournament) => tournament.order === 0\r\n          // );\r\n          // console.log(allOrdersZero, 'allOrdersZero');\r\n          // if (allOrdersZero) {\r\n          //   res.data.sort((a, b) => {\r\n          //     return a.name.localeCompare(b.name, undefined, {\r\n          //       numeric: true,\r\n          //       sensitivity: 'base',\r\n          //     });\r\n          //   });\r\n          // } else {\r\n          //   res.data.sort((a, b) => {\r\n          //     return a.order - b.order;\r\n          //   });\r\n          // }\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  updateTournamentOder(tournaments) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/settings/update-tournaments-order`, {\r\n        tournaments\r\n      })\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  generateIcs(data): Observable<Blob> {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/locations/generateIcs`, data, { responseType: 'blob' })\r\n      .pipe(\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  assignRoleToUser(data) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/match-streaming/assign-role`, data)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}