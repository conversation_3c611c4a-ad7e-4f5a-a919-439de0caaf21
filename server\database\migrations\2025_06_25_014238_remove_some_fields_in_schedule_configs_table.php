<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('schedule_configs', function (Blueprint $table) {
            $table->dropColumn('group_names');
            $table->dropColumn('nums_of_referees');
            $table->dropColumn('referee_ids');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('schedule_configs', function (Blueprint $table) {
            $table->json('group_names');
            $table->unsignedInteger('nums_of_referees');
            $table->json('referee_ids');
        });
    }
};
