{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"app/services/tournament.service\";\nimport * as i3 from \"app/services/season.service\";\nimport * as i4 from \"app/services/stage.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"@core/services/config.service\";\nimport * as i8 from \"@angular/platform-browser\";\nimport * as i9 from \"app/services/navigation.service\";\nfunction FixturesResultsComponent_app_content_header_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-content-header\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"contentHeader\", ctx_r0.contentHeader);\n  }\n}\nfunction FixturesResultsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tab-fixtures\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matches\", ctx_r2.matches.fixtures);\n  }\n}\nfunction FixturesResultsComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tab-fixtures\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matches\", ctx_r3.matches.results);\n  }\n}\nfunction FixturesResultsComponent_ng_template_23_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"h4\", 19);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"No data\"), \" \");\n  }\n}\nfunction FixturesResultsComponent_ng_template_23_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"stage-tables\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"tableData\", ctx_r7.tableData)(\"showUpdateButton\", false);\n  }\n}\nfunction FixturesResultsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FixturesResultsComponent_ng_template_23_ng_container_0_Template, 5, 3, \"ng-container\", 16);\n    i0.ɵɵtemplate(1, FixturesResultsComponent_ng_template_23_ng_template_1_Template, 1, 2, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(2);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.tableData.length === 0)(\"ngIfElse\", _r6);\n  }\n}\nexport class FixturesResultsComponent {\n  constructor(_trans, _tourService, _seasonService, _stageService, _route, _router, _loading, _coreConfigService, _titleService, _navigationService, cdr) {\n    this._trans = _trans;\n    this._tourService = _tourService;\n    this._seasonService = _seasonService;\n    this._stageService = _stageService;\n    this._route = _route;\n    this._router = _router;\n    this._loading = _loading;\n    this._coreConfigService = _coreConfigService;\n    this._titleService = _titleService;\n    this._navigationService = _navigationService;\n    this.cdr = cdr;\n    this.matches = {\n      fixtures: {},\n      results: {}\n    };\n    this.params = {\n      season_id: null,\n      group_id: null,\n      tournament_id: null,\n      is_results: 0\n    };\n    this.activeId = 1;\n    this.regexTest = /^\\/[^\\/]+\\/matches\\/\\d+\\/details$/;\n    this.params.tournament_id = this._route.snapshot.paramMap.get('tournament_id');\n    // this.params.group_id = parseInt(\n    //   this._route.snapshot.queryParamMap.get('group_id')\n    // );\n    // this.params.tournament_id = parseInt(\n    //   this._route.snapshot.queryParamMap.get('tournament_id')\n    // );\n    // this.getCurrentSeason();\n  }\n\n  ngOnInit() {\n    this.showMatches();\n  }\n  showMatches() {\n    this._loading.show();\n    // this._router.navigate([], { queryParams: this.params });\n    // get fixtures\n    this.getFixtureResult(this.params);\n    // get results\n    let prm = JSON.parse(JSON.stringify(this.params));\n    prm.is_results = 1;\n    this.getFixtureResult(prm, 'results');\n  }\n  getFixtureResult(params, type = 'fixtures') {\n    console.log('trigger here');\n    this._tourService.getFixturesResultsByTournament(this.params.tournament_id, params).subscribe(res => {\n      let tournament_name = res.tournament.name;\n      this._titleService.setTitle(tournament_name);\n      this.contentHeader = {\n        headerTitle: tournament_name,\n        actionButton: false,\n        breadcrumb: {\n          type: '',\n          links: [{\n            name: this._trans.instant('Competitions'),\n            isLink: true,\n            link: '/leagues/select-tournament'\n          }, {\n            name: tournament_name,\n            isLink: false\n          }]\n        }\n      };\n      this.matches[type] = res.matches;\n      // Handle tab switching\n      if (type === 'fixtures') {\n        if (res.matches.length === 0) {\n          setTimeout(() => this.activeId = 2);\n        } else {\n          if (this.regexTest.test(this._navigationService.getPreviousUrl())) {\n            setTimeout(() => this.activeId = 2);\n          } else {\n            setTimeout(() => this.activeId = 1);\n          }\n        }\n      } else if (type === 'results') {\n        if (res.matches.length === 0) {\n          setTimeout(() => this.activeId = 1);\n        }\n      }\n      // Handle stage_id\n      if (res.stage_id) {\n        this.stage_id = res.stage_id;\n        this.getTableData(res.stage_id);\n      }\n    });\n  }\n  getTableData(stage_id) {\n    let stageIDs = [];\n    if (!stage_id) return;\n    for (let key in stage_id) {\n      switch (stage_id[key]) {\n        case AppConfig.TOURNAMENT_TYPES.league:\n        case AppConfig.TOURNAMENT_TYPES.groups:\n        case AppConfig.TOURNAMENT_TYPES.knockout:\n          stageIDs.push(key);\n          break;\n      }\n    }\n    if (stageIDs.length === 0 || this.tableData) return;\n    stageIDs.forEach(stageID => {\n      console.log('stageID:', stageID);\n      this._stageService.getTableData(stageID).subscribe(data => {\n        this.tableData = this.tableData ? [...this.tableData, ...data.filter(newItem => !this.tableData.some(existingItem => existingItem.id === newItem.id))] : data;\n        // this.tableData = this.tableData\n        //   ? Array.from(\n        //       new Set(this.tableData.map((item) => JSON.stringify(item)))\n        //     ).map((item) => JSON.parse(item as string))\n        //   : data;\n      }, error => console.error(error));\n    });\n  }\n  static #_ = this.ɵfac = function FixturesResultsComponent_Factory(t) {\n    return new (t || FixturesResultsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.TournamentService), i0.ɵɵdirectiveInject(i3.SeasonService), i0.ɵɵdirectiveInject(i4.StageService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.CoreConfigService), i0.ɵɵdirectiveInject(i8.Title), i0.ɵɵdirectiveInject(i9.NavigationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FixturesResultsComponent,\n    selectors: [[\"app-fixtures-results\"]],\n    decls: 25,\n    vars: 15,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\", 4, \"ngIf\"], [\"id\", \"fixtures-results\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\", 3, \"activeId\", \"activeIdChange\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\", 3, \"ngbNavItem\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [3, \"contentHeader\"], [\"type\", \"fixtures\", 3, \"matches\"], [\"type\", \"results\", 3, \"matches\"], [4, \"ngIf\", \"ngIfElse\"], [\"tableContent\", \"\"], [1, \"text-center\"], [1, \"text-muted\"], [3, \"tableData\", \"showUpdateButton\"]],\n    template: function FixturesResultsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, FixturesResultsComponent_app_content_header_2_Template, 1, 1, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8);\n        i0.ɵɵlistener(\"activeIdChange\", function FixturesResultsComponent_Template_ul_activeIdChange_7_listener($event) {\n          return ctx.activeId = $event;\n        });\n        i0.ɵɵelementStart(9, \"li\", 9)(10, \"a\", 10);\n        i0.ɵɵtext(11);\n        i0.ɵɵpipe(12, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, FixturesResultsComponent_ng_template_13_Template, 1, 1, \"ng-template\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"li\", 9)(15, \"a\", 10);\n        i0.ɵɵtext(16);\n        i0.ɵɵpipe(17, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, FixturesResultsComponent_ng_template_18_Template, 1, 1, \"ng-template\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"li\", 9)(20, \"a\", 10);\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(23, FixturesResultsComponent_ng_template_23_Template, 3, 2, \"ng-template\", 11);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(24, \"div\", 12);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.contentHeader);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"activeId\", ctx.activeId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngbNavItem\", 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 9, \"Fixtures\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavItem\", 2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 11, \"Results\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavItem\", 3);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 13, \"Tables\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r1);\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SAASA,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;ICLtCC,yCAGsB;;;;IAFpBA,oDAA+B;;;;;IAmBnBA,mCACe;;;;IADeA,iDAA4B;;;;;IAS1DA,mCACe;;;;IADcA,gDAA2B;;;;;IAOxDA,6BAEC;IACCA,+BAAyB;IAErBA,YACF;;IAAAA,iBAAK;IAETA,0BAAe;;;IAHTA,eACF;IADEA,gEACF;;;;;IAIFA,mCAGgB;;;;IAFdA,4CAAuB;;;;;IAX3BA,2GAQe;IACfA,0IAKc;;;;;IAbXA,oDAA8B;;;ADrBnD,OAAM,MAAOC,wBAAwB;EAoBnCC,YACSC,MAAwB,EACxBC,YAA+B,EAC/BC,cAA6B,EAC7BC,aAA2B,EAC3BC,MAAsB,EACtBC,OAAe,EACfC,QAAwB,EACvBC,kBAAqC,EACtCC,aAAoB,EACnBC,kBAAqC,EACrCC,GAAsB;IAVvB,WAAM,GAANV,MAAM;IACN,iBAAY,GAAZC,YAAY;IACZ,mBAAc,GAAdC,cAAc;IACd,kBAAa,GAAbC,aAAa;IACb,WAAM,GAANC,MAAM;IACN,YAAO,GAAPC,OAAO;IACP,aAAQ,GAARC,QAAQ;IACP,uBAAkB,GAAlBC,kBAAkB;IACnB,kBAAa,GAAbC,aAAa;IACZ,uBAAkB,GAAlBC,kBAAkB;IAClB,QAAG,GAAHC,GAAG;IA5Bb,YAAO,GAAG;MACRC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;KACV;IACD,WAAM,GAAG;MACPC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE;KACb;IAGD,aAAQ,GAAG,CAAC;IAGZ,cAAS,GAAG,mCAAmC;IAgB7C,IAAI,CAACC,MAAM,CAACF,aAAa,GACvB,IAAI,CAACX,MAAM,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC;IACpD;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEAC,QAAQ;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAW;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB;IACA;IACA,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACP,MAAM,CAAC;IAClC;IACA,IAAIQ,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACX,MAAM,CAAC,CAAC;IACjDQ,GAAG,CAACT,UAAU,GAAG,CAAC;IAClB,IAAI,CAACQ,gBAAgB,CAACC,GAAG,EAAE,SAAS,CAAC;EACvC;EAEAD,gBAAgB,CAACP,MAAM,EAAEY,IAAI,GAAG,UAAU;IACxCC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAE3B,IAAI,CAAC9B,YAAY,CACd+B,8BAA8B,CAAC,IAAI,CAACf,MAAM,CAACF,aAAa,EAAEE,MAAM,CAAC,CACjEgB,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAIC,eAAe,GAAGD,GAAG,CAACE,UAAU,CAACC,IAAI;MACzC,IAAI,CAAC7B,aAAa,CAAC8B,QAAQ,CAACH,eAAe,CAAC;MAE5C,IAAI,CAACI,aAAa,GAAG;QACnBC,WAAW,EAAEL,eAAe;QAC5BM,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;UACVb,IAAI,EAAE,EAAE;UACRc,KAAK,EAAE,CACL;YACEN,IAAI,EAAE,IAAI,CAACrC,MAAM,CAAC4C,OAAO,CAAC,cAAc,CAAC;YACzCC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;WACP,EACD;YACET,IAAI,EAAEF,eAAe;YACrBU,MAAM,EAAE;WACT;;OAGN;MACD,IAAI,CAACE,OAAO,CAAClB,IAAI,CAAC,GAAGK,GAAG,CAACa,OAAO;MAEhC;MACA,IAAIlB,IAAI,KAAK,UAAU,EAAE;QACvB,IAAIK,GAAG,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;UAC5BC,UAAU,CAAC,MAAM,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;SACpC,MAAM;UACL,IAAI,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC3C,kBAAkB,CAAC4C,cAAc,EAAE,CAAC,EAAE;YACjEJ,UAAU,CAAC,MAAM,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;WACpC,MAAM;YACLD,UAAU,CAAC,MAAM,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;;;OAGxC,MAAM,IAAIrB,IAAI,KAAK,SAAS,EAAE;QAC7B,IAAIK,GAAG,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;UAC5BC,UAAU,CAAC,MAAM,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;;;MAIvC;MACA,IAAIhB,GAAG,CAACoB,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAGpB,GAAG,CAACoB,QAAQ;QAC5B,IAAI,CAACC,YAAY,CAACrB,GAAG,CAACoB,QAAQ,CAAC;;IAEnC,CAAC,CAAC;EACN;EAGAC,YAAY,CAACD,QAAQ;IACnB,IAAIE,QAAQ,GAAG,EAAE;IACjB,IAAI,CAACF,QAAQ,EAAE;IAEf,KAAK,IAAIG,GAAG,IAAIH,QAAQ,EAAE;MACxB,QAAQA,QAAQ,CAACG,GAAG,CAAC;QACnB,KAAK7D,SAAS,CAAC8D,gBAAgB,CAACC,MAAM;QACtC,KAAK/D,SAAS,CAAC8D,gBAAgB,CAACE,MAAM;QACtC,KAAKhE,SAAS,CAAC8D,gBAAgB,CAACG,QAAQ;UACtCL,QAAQ,CAACM,IAAI,CAACL,GAAG,CAAC;UAClB;MAAM;;IAIZ,IAAID,QAAQ,CAACR,MAAM,KAAK,CAAC,IAAI,IAAI,CAACe,SAAS,EAAE;IAE7CP,QAAQ,CAACQ,OAAO,CAAEC,OAAO,IAAI;MAC3BnC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkC,OAAO,CAAC;MAEhC,IAAI,CAAC9D,aAAa,CAACoD,YAAY,CAACU,OAAO,CAAC,CAAChC,SAAS,CAC/CiC,IAAS,IAAI;QAEZ,IAAI,CAACH,SAAS,GAAG,IAAI,CAACA,SAAS,GAC3B,CACA,GAAG,IAAI,CAACA,SAAS,EACjB,GAAGG,IAAI,CAACC,MAAM,CACZC,OAAO,IAAI,CAAC,IAAI,CAACL,SAAS,CAACM,IAAI,CAACC,YAAY,IAAIA,YAAY,CAACC,EAAE,KAAKH,OAAO,CAACG,EAAE,CAAC,CAChF,CACF,GACCL,IAAI;QAER;QACA;QACA;QACA;QACA;MACF,CAAC,EACAM,KAAK,IAAK1C,OAAO,CAAC0C,KAAK,CAACA,KAAK,CAAC,CAChC;IACH,CAAC,CAAC;EACJ;EAAC;qBA3JU1E,wBAAwB;EAAA;EAAA;UAAxBA,wBAAwB;IAAA2E;IAAAC;IAAAC;IAAAC;IAAAC;MAAA;QClBrChF,8BAA+C;QAE3CA,uGAGsB;QAEtBA,kCAA+B;QAMrBA;UAAA;QAAA,EAAuB;QAIvBA,6BAAgC;QAE5BA,aACF;;QAAAA,iBAAI;QACJA,4FAGc;QAChBA,iBAAK;QACLA,8BAAgC;QAE5BA,aACF;;QAAAA,iBAAI;QACJA,4FAGc;QAChBA,iBAAK;QACLA,8BAAgC;QAChBA,aAA0B;;QAAAA,iBAAI;QAC5CA,4FAgBc;QAChBA,iBAAK;QAGTA,2BAA6C;QAC/CA,iBAAM;;;;QAtDPA,eAAmB;QAAnBA,wCAAmB;QASZA,eAAuB;QAAvBA,uCAAuB;QAIRA,eAAgB;QAAhBA,8BAAgB;QAE3BA,eACF;QADEA,kEACF;QAMaA,eAAgB;QAAhBA,8BAAgB;QAE3BA,eACF;QADEA,kEACF;QAMaA,eAAgB;QAAhBA,8BAAgB;QACfA,eAA0B;QAA1BA,sDAA0B;QAqBzCA,eAAoB;QAApBA,kCAAoB", "names": ["AppConfig", "i0", "FixturesResultsComponent", "constructor", "_trans", "_tourService", "_seasonService", "_stageService", "_route", "_router", "_loading", "_coreConfigService", "_titleService", "_navigationService", "cdr", "fixtures", "results", "season_id", "group_id", "tournament_id", "is_results", "params", "snapshot", "paramMap", "get", "ngOnInit", "showMatches", "show", "getFixtureResult", "prm", "JSON", "parse", "stringify", "type", "console", "log", "getFixturesResultsByTournament", "subscribe", "res", "tournament_name", "tournament", "name", "setTitle", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "instant", "isLink", "link", "matches", "length", "setTimeout", "activeId", "regexTest", "test", "getPreviousUrl", "stage_id", "getTableData", "stageIDs", "key", "TOURNAMENT_TYPES", "league", "groups", "knockout", "push", "tableData", "for<PERSON>ach", "stageID", "data", "filter", "newItem", "some", "existingItem", "id", "error", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\fixtures-results.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\fixtures-results.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { CoreConfigService } from '@core/services/config.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NavigationService } from 'app/services/navigation.service';\r\n\r\n@Component({\r\n  selector: 'app-fixtures-results',\r\n  templateUrl: './fixtures-results.component.html',\r\n  styleUrls: ['./fixtures-results.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class FixturesResultsComponent implements OnInit {\r\n  stage_id: any;\r\n  contentHeader: object;\r\n  matches = {\r\n    fixtures: {},\r\n    results: {},\r\n  };\r\n  params = {\r\n    season_id: null,\r\n    group_id: null,\r\n    tournament_id: null,\r\n    is_results: 0,\r\n  };\r\n  tableData: any;\r\n  selectedTournament: any;\r\n  activeId = 1;\r\n  season_id: any;\r\n\r\n  regexTest = /^\\/[^\\/]+\\/matches\\/\\d+\\/details$/;\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _tourService: TournamentService,\r\n    public _seasonService: SeasonService,\r\n    public _stageService: StageService,\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _loading: LoadingService,\r\n    private _coreConfigService: CoreConfigService,\r\n    public _titleService: Title,\r\n    private _navigationService: NavigationService,\r\n    private cdr: ChangeDetectorRef,\r\n\r\n  ) {\r\n    this.params.tournament_id =\r\n      this._route.snapshot.paramMap.get('tournament_id');\r\n    // this.params.group_id = parseInt(\r\n    //   this._route.snapshot.queryParamMap.get('group_id')\r\n    // );\r\n    // this.params.tournament_id = parseInt(\r\n    //   this._route.snapshot.queryParamMap.get('tournament_id')\r\n    // );\r\n    // this.getCurrentSeason();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.showMatches();\r\n  }\r\n\r\n  showMatches() {\r\n    this._loading.show();\r\n    // this._router.navigate([], { queryParams: this.params });\r\n    // get fixtures\r\n    this.getFixtureResult(this.params);\r\n    // get results\r\n    let prm = JSON.parse(JSON.stringify(this.params));\r\n    prm.is_results = 1;\r\n    this.getFixtureResult(prm, 'results');\r\n  }\r\n\r\n  getFixtureResult(params, type = 'fixtures') {\r\n    console.log('trigger here');\r\n\r\n    this._tourService\r\n      .getFixturesResultsByTournament(this.params.tournament_id, params)\r\n      .subscribe((res) => {\r\n        let tournament_name = res.tournament.name;\r\n        this._titleService.setTitle(tournament_name);\r\n\r\n        this.contentHeader = {\r\n          headerTitle: tournament_name,\r\n          actionButton: false,\r\n          breadcrumb: {\r\n            type: '',\r\n            links: [\r\n              {\r\n                name: this._trans.instant('Competitions'),\r\n                isLink: true,\r\n                link: '/leagues/select-tournament',\r\n              },\r\n              {\r\n                name: tournament_name,\r\n                isLink: false,\r\n              },\r\n            ],\r\n          },\r\n        };\r\n        this.matches[type] = res.matches;\r\n\r\n        // Handle tab switching\r\n        if (type === 'fixtures') {\r\n          if (res.matches.length === 0) {\r\n            setTimeout(() => this.activeId = 2);\r\n          } else {\r\n            if (this.regexTest.test(this._navigationService.getPreviousUrl())) {\r\n              setTimeout(() => this.activeId = 2);\r\n            } else {\r\n              setTimeout(() => this.activeId = 1);\r\n            }\r\n          }\r\n        } else if (type === 'results') {\r\n          if (res.matches.length === 0) {\r\n            setTimeout(() => this.activeId = 1);\r\n          }\r\n        }\r\n\r\n        // Handle stage_id\r\n        if (res.stage_id) {\r\n          this.stage_id = res.stage_id;\r\n          this.getTableData(res.stage_id);\r\n        }\r\n      });\r\n  }\r\n\r\n\r\n  getTableData(stage_id) {\r\n    let stageIDs = [];\r\n    if (!stage_id) return;\r\n\r\n    for (let key in stage_id) {\r\n      switch (stage_id[key]) {\r\n        case AppConfig.TOURNAMENT_TYPES.league:\r\n        case AppConfig.TOURNAMENT_TYPES.groups:\r\n        case AppConfig.TOURNAMENT_TYPES.knockout:\r\n          stageIDs.push(key);\r\n          break;\r\n      }\r\n    }\r\n\r\n    if (stageIDs.length === 0 || this.tableData) return;\r\n\r\n    stageIDs.forEach((stageID) => {\r\n      console.log('stageID:', stageID);\r\n\r\n      this._stageService.getTableData(stageID).subscribe(\r\n        (data: any) => {\r\n\r\n          this.tableData = this.tableData\r\n            ? [\r\n              ...this.tableData,\r\n              ...data.filter(\r\n                newItem => !this.tableData.some(existingItem => existingItem.id === newItem.id)\r\n              )\r\n            ]\r\n            : data;\r\n\r\n          // this.tableData = this.tableData\r\n          //   ? Array.from(\r\n          //       new Set(this.tableData.map((item) => JSON.stringify(item)))\r\n          //     ).map((item) => JSON.parse(item as string))\r\n          //   : data;\r\n        },\r\n        (error) => console.error(error)\r\n      );\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <app-content-header\r\n      [contentHeader]=\"contentHeader\"\r\n      *ngIf=\"contentHeader\"\r\n    ></app-content-header>\r\n\r\n    <section id=\"fixtures-results\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <ul\r\n              ngbNav\r\n              [(activeId)]=\"activeId\"\r\n              #nav=\"ngbNav\"\r\n              class=\"nav-tabs m-0\"\r\n            >\r\n              <li ngbNavItem [ngbNavItem]=\"1\">\r\n                <a ngbNavLink>\r\n                  {{ 'Fixtures' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <tab-fixtures type=\"fixtures\" [matches]=\"matches.fixtures\">\r\n                  </tab-fixtures>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem [ngbNavItem]=\"2\">\r\n                <a ngbNavLink>\r\n                  {{ 'Results' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <tab-fixtures type=\"results\" [matches]=\"matches.results\">\r\n                  </tab-fixtures>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem [ngbNavItem]=\"3\">\r\n                <a ngbNavLink>{{ 'Tables' | translate }}</a>\r\n                <ng-template ngbNavContent>\r\n                  <ng-container\r\n                    *ngIf=\"tableData.length === 0; else tableContent\"\r\n                  >\r\n                    <div class=\"text-center\">\r\n                      <h4 class=\"text-muted\">\r\n                        {{ 'No data' | translate }}\r\n                      </h4>\r\n                    </div>\r\n                  </ng-container>\r\n                  <ng-template #tableContent>\r\n                    <stage-tables\r\n                      [tableData]=\"tableData\"\r\n                      [showUpdateButton]=\"false\"\r\n                    ></stage-tables>\r\n                  </ng-template>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}