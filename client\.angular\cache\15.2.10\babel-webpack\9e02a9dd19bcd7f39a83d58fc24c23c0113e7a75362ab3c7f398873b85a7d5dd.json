{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { AppConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"app/services/stage.service\";\nimport * as i3 from \"ngx-toastr\";\nexport class StageDetailsComponent {\n  constructor(_trans, _stageService, _translateService, toastService) {\n    this._trans = _trans;\n    this._stageService = _stageService;\n    this._translateService = _translateService;\n    this.toastService = toastService;\n    this.onDataChange = new EventEmitter();\n    this.form = new FormGroup({});\n    this.model = {};\n  }\n  ngOnInit() {\n    // console.log(this.stage);\n    this.model = this.stage;\n    switch (this.stage.type) {\n      case AppConfig.TOURNAMENT_TYPES.groups:\n      // break;\n      case AppConfig.TOURNAMENT_TYPES.league:\n        this.fields = [{\n          key: 'name',\n          type: 'input',\n          props: {\n            maxLength: 30,\n            label: this._trans.instant('Stage name'),\n            placeholder: this._trans.instant('Enter stage name'),\n            pattern: /[\\S]/,\n            required: true\n          }\n        }, {\n          key: 'is_released',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Release'),\n            required: true\n          }\n        }, {\n          key: 'is_display_tbd',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Display TBD'),\n            required: true\n          }\n        }, {\n          key: 'is_display_results',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Display Results'),\n            required: true\n          }\n        }, {\n          key: 'is_display_table',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Display Table'),\n            required: true\n          }\n        }, {\n          key: 'no_encounters',\n          type: 'core-touchspin',\n          props: {\n            type: 'number',\n            min: 1,\n            max: 3,\n            label: this._trans.instant('Number of encounters'),\n            required: true\n          }\n        }, {\n          key: 'points_win',\n          type: 'core-touchspin',\n          props: {\n            type: 'number',\n            min: 0,\n            max: 100,\n            label: this._trans.instant('Points for win'),\n            required: true\n          }\n        }, {\n          key: 'points_draw',\n          type: 'core-touchspin',\n          props: {\n            type: 'number',\n            min: 0,\n            max: 100,\n            label: this._trans.instant('Points for draw'),\n            required: true\n          }\n        }, {\n          key: 'points_loss',\n          type: 'core-touchspin',\n          props: {\n            type: 'number',\n            min: 0,\n            max: 100,\n            label: this._trans.instant('Points for loss'),\n            required: true\n          }\n        }, {\n          key: 'ranking_criteria',\n          type: 'radio',\n          wrappers: ['details', 'form-field'],\n          props: {\n            label: this._trans.instant('Tie break order'),\n            required: true,\n            options: [{\n              value: AppConfig.RANKING_CRITERIA.total,\n              label: this._trans.instant(AppConfig.RANKING_CRITERIA.total),\n              description: `<div class=\"text-secondary text-12px\">\n                  1. ${this._trans.instant('Points')} - ${this._trans.instant('Total')}<br>\n                  2. ${this._trans.instant('Goal difference')} - ${this._trans.instant('Total')}<br>\n                  3. ${this._trans.instant('Goals scored')} - ${this._trans.instant('Total')}<br>\n                  4. ${this._trans.instant('Points')} - ${this._trans.instant('Head to Head')}<br>\n                  5. ${this._trans.instant('Goal difference')} - ${this._trans.instant('Head to Head')}<br>\n                  6. ${this._trans.instant('Goals scored')} - ${this._trans.instant('Head to Head')}</div>`\n            }, {\n              value: AppConfig.RANKING_CRITERIA.head_to_head,\n              label: this._trans.instant(AppConfig.RANKING_CRITERIA.head_to_head),\n              description: `<div class=\"text-secondary text-12px\">\n                  1. ${this._trans.instant('Points')} - ${this._trans.instant('Total')}<br> \n                  2. ${this._trans.instant('Points')} - ${this._trans.instant('Head to Head')}<br> \n                  3. ${this._trans.instant('Goal difference')} - ${this._trans.instant('Head to Head')}<br>\n                  4. ${this._trans.instant('Goals scored')} - ${this._trans.instant('Head to Head')}<br>\n                  5. ${this._trans.instant('Goal difference')} - ${this._trans.instant('Total')}<br>\n                  6. ${this._trans.instant('Goals scored')} - ${this._trans.instant('Total')}</div>`\n            }]\n          }\n        }, {\n          key: 'limit_update_score_time',\n          type: 'core-touchspin',\n          props: {\n            type: 'number',\n            min: 0,\n            max: 1000,\n            label: this._trans.instant('Limit update score time (hours)'),\n            required: false\n          }\n        }];\n        break;\n      case AppConfig.TOURNAMENT_TYPES.knockout:\n        this.fields = [{\n          key: 'name',\n          type: 'input',\n          props: {\n            maxLength: 15,\n            label: this._trans.instant('Stage name'),\n            placeholder: this._trans.instant('Enter stage name'),\n            required: true\n          }\n        }, {\n          key: 'is_released',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Release'),\n            required: true\n          }\n        }, {\n          key: 'is_display_tbd',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Display TBD'),\n            required: true\n          }\n        }, {\n          key: 'is_display_results',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Display Results'),\n            required: true\n          }\n        }, {\n          key: 'is_display_table',\n          type: 'checkbox',\n          props: {\n            label: this._trans.instant('Display Table'),\n            required: true\n          }\n        }\n        // {\n        //   key: 'no_encounters',\n        //   type: 'radio',\n        //   props: {\n        //     label: this._trans.instant('Mode'),\n        //     required: true,\n        //     options: [\n        //       {\n        //         value: AppConfig.KNOCKOUT_MODES.single.value,\n        //         label: AppConfig.KNOCKOUT_MODES.single.label,\n        //       },\n        //       {\n        //         value: AppConfig.KNOCKOUT_MODES.double.value,\n        //         label: AppConfig.KNOCKOUT_MODES.double.label,\n        //       },\n        //     ],\n        //   },\n        // },\n        ];\n\n        if (this.tournament?.type_knockout === AppConfig.KNOCKOUT_TYPES.type4) {\n          this.fields.push({\n            key: 'third_place',\n            type: 'checkbox',\n            props: {\n              label: `${this._trans.instant('Third place')}?`,\n              required: true\n            }\n          });\n        }\n        break;\n      default:\n        break;\n    }\n  }\n  onSubmit(model) {\n    if (this.form.invalid) {\n      return;\n    }\n    // create FormData object\n    const formData = new FormData();\n    formData.append('action', 'edit');\n    // foreach key in model add to formData object. e.g. formData.append('data[id][name]', 'value')\n    Object.keys(model).forEach(key => {\n      formData.append(`data[${this.stage.id}][${key}]`, model[key]);\n    });\n    // send form data to server\n    this._stageService.updateStage(formData).subscribe(res => {\n      // console.log(res);\n      this.toastService.success(this._trans.instant('Stage updated successfully'));\n      this.onDataChange.emit(res);\n    }, err => {\n      if (err.hasOwnProperty('fieldErrors')) {\n        err.fieldErrors.forEach(error => {\n          this.form.controls[error.name].setErrors({\n            serverError: error.status\n          });\n        });\n      } else {\n        Swal.fire({\n          title: 'Error',\n          text: err.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK'),\n          customClass: {\n            confirmButton: 'btn btn-primary'\n          }\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function StageDetailsComponent_Factory(t) {\n    return new (t || StageDetailsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.StageService), i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i3.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StageDetailsComponent,\n    selectors: [[\"stage-details\"]],\n    inputs: {\n      stage: \"stage\",\n      tournament: \"tournament\"\n    },\n    outputs: {\n      onDataChange: \"onDataChange\"\n    },\n    decls: 11,\n    vars: 10,\n    consts: [[1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-body\"], [3, \"formGroup\", \"ngSubmit\"], [3, \"form\", \"fields\", \"model\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"float-right\"]],\n    template: function StageDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function StageDetailsComponent_Template_form_ngSubmit_6_listener() {\n          return ctx.onSubmit(ctx.model);\n        });\n        i0.ɵɵelement(7, \"formly-form\", 5);\n        i0.ɵɵelementStart(8, \"button\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, \"Stage Details\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 8, \"Save\"));\n      }\n    },\n    styles: [\".text-10px {\\n  font-size: 10px;\\n}\\n\\n.text-12px {\\n  font-size: 12px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlLWRldGFpbHMvc3RhZ2UtZGV0YWlscy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGVBQUE7QUFDSjs7QUFDQTtFQUNJLGVBQUE7QUFFSiIsInNvdXJjZXNDb250ZW50IjpbIi50ZXh0LTEwcHh7XHJcbiAgICBmb250LXNpemU6IDEwcHg7XHJcbn1cclxuLnRleHQtMTJweHtcclxuICAgIGZvbnQtc2l6ZTogMTJweDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAEEA,YAAY,QAKP,eAAe;AACtB,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,OAAOC,IAAI,MAAM,aAAa;;;;;AAQ9B,OAAM,MAAOC,qBAAqB;EAIhCC,YACSC,MAAwB,EACxBC,aAA2B,EAC1BC,iBAAmC,EACpCC,YAA2B;IAH3B,WAAM,GAANH,MAAM;IACN,kBAAa,GAAbC,aAAa;IACZ,sBAAiB,GAAjBC,iBAAiB;IAClB,iBAAY,GAAZC,YAAY;IAPX,iBAAY,GAAG,IAAIT,YAAY,EAAO;IAShD,SAAI,GAAG,IAAIC,SAAS,CAAC,EAAE,CAAC;IACxB,UAAK,GAAQ,EAAE;EAFZ;EAIHS,QAAQ;IACN;IACA,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,KAAK;IACvB,QAAQ,IAAI,CAACA,KAAK,CAACC,IAAI;MACrB,KAAKX,SAAS,CAACY,gBAAgB,CAACC,MAAM;MACtC;MACA,KAAKb,SAAS,CAACY,gBAAgB,CAACE,MAAM;QACpC,IAAI,CAACC,MAAM,GAAG,CACZ;UACEC,GAAG,EAAE,MAAM;UACXL,IAAI,EAAE,OAAO;UACbM,KAAK,EAAE;YACLC,SAAS,EAAE,EAAE;YACbC,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,YAAY,CAAC;YACxCC,WAAW,EAAE,IAAI,CAACjB,MAAM,CAACgB,OAAO,CAAC,kBAAkB,CAAC;YACpDE,OAAO,EAAE,MAAM;YACfC,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,aAAa;UAClBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,SAAS,CAAC;YACrCG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,gBAAgB;UACrBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,aAAa,CAAC;YACzCG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,oBAAoB;UACzBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,iBAAiB,CAAC;YAC7CG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,kBAAkB;UACvBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,eAAe,CAAC;YAC3CG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,eAAe;UACpBL,IAAI,EAAE,gBAAgB;UACtBM,KAAK,EAAE;YACLN,IAAI,EAAE,QAAQ;YACda,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,CAAC;YACNN,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,sBAAsB,CAAC;YAClDG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,YAAY;UACjBL,IAAI,EAAE,gBAAgB;UACtBM,KAAK,EAAE;YACLN,IAAI,EAAE,QAAQ;YACda,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,GAAG;YACRN,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,gBAAgB,CAAC;YAC5CG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,aAAa;UAClBL,IAAI,EAAE,gBAAgB;UACtBM,KAAK,EAAE;YACLN,IAAI,EAAE,QAAQ;YACda,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,GAAG;YACRN,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,iBAAiB,CAAC;YAC7CG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,aAAa;UAClBL,IAAI,EAAE,gBAAgB;UACtBM,KAAK,EAAE;YACLN,IAAI,EAAE,QAAQ;YACda,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,GAAG;YACRN,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,iBAAiB,CAAC;YAC7CG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,kBAAkB;UACvBL,IAAI,EAAE,OAAO;UACbe,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;UACnCT,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,iBAAiB,CAAC;YAC7CG,QAAQ,EAAE,IAAI;YACdI,OAAO,EAAE,CACP;cACEC,KAAK,EAAE5B,SAAS,CAAC6B,gBAAgB,CAACC,KAAK;cACvCX,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAACpB,SAAS,CAAC6B,gBAAgB,CAACC,KAAK,CAAC;cAC5DC,WAAW,EAAE;uBACR,IAAI,CAAC3B,MAAM,CAACgB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACzD,OAAO,CACR;uBACI,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,iBAAiB,CAClB,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,OAAO,CAAC;uBAC9B,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,cAAc,CACf,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,OAAO,CAAC;uBAC9B,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACzD,cAAc,CACf;uBACI,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,iBAAiB,CAClB,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,cAAc,CAAC;uBACrC,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,cAAc,CACf,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,cAAc,CAAC;aAC3C,EACD;cACEQ,KAAK,EAAE5B,SAAS,CAAC6B,gBAAgB,CAACG,YAAY;cAC9Cb,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CACxBpB,SAAS,CAAC6B,gBAAgB,CAACG,YAAY,CACxC;cACDD,WAAW,EAAE;uBACR,IAAI,CAAC3B,MAAM,CAACgB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACzD,OAAO,CACR;uBACI,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACzD,cAAc,CACf;uBACI,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,iBAAiB,CAClB,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,cAAc,CAAC;uBACrC,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,cAAc,CACf,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,cAAc,CAAC;uBACrC,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,iBAAiB,CAClB,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,OAAO,CAAC;uBAC9B,IAAI,CAAChB,MAAM,CAACgB,OAAO,CACtB,cAAc,CACf,MAAM,IAAI,CAAChB,MAAM,CAACgB,OAAO,CAAC,OAAO,CAAC;aACpC;;SAGN,EACD;UACEJ,GAAG,EAAE,yBAAyB;UAC9BL,IAAI,EAAE,gBAAgB;UACtBM,KAAK,EAAE;YACLN,IAAI,EAAE,QAAQ;YACda,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,IAAI;YACTN,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,iCAAiC,CAAC;YAC7DG,QAAQ,EAAE;;SAEb,CACF;QACD;MACF,KAAKvB,SAAS,CAACY,gBAAgB,CAACqB,QAAQ;QACtC,IAAI,CAAClB,MAAM,GAAG,CACZ;UACEC,GAAG,EAAE,MAAM;UACXL,IAAI,EAAE,OAAO;UACbM,KAAK,EAAE;YACLC,SAAS,EAAE,EAAE;YACbC,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,YAAY,CAAC;YACxCC,WAAW,EAAE,IAAI,CAACjB,MAAM,CAACgB,OAAO,CAAC,kBAAkB,CAAC;YACpDG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,aAAa;UAClBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,SAAS,CAAC;YACrCG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,gBAAgB;UACrBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,aAAa,CAAC;YACzCG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,oBAAoB;UACzBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,iBAAiB,CAAC;YAC7CG,QAAQ,EAAE;;SAEb,EACD;UACEP,GAAG,EAAE,kBAAkB;UACvBL,IAAI,EAAE,UAAU;UAChBM,KAAK,EAAE;YACLE,KAAK,EAAE,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,eAAe,CAAC;YAC3CG,QAAQ,EAAE;;;QAGd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA,CACD;;QACD,IAAI,IAAI,CAACW,UAAU,EAAEC,aAAa,KAAKnC,SAAS,CAACoC,cAAc,CAACC,KAAK,EAAE;UACrE,IAAI,CAACtB,MAAM,CAACuB,IAAI,CAAC;YACftB,GAAG,EAAE,aAAa;YAClBL,IAAI,EAAE,UAAU;YAChBM,KAAK,EAAE;cACLE,KAAK,EAAE,GAAG,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,aAAa,CAAC,GAAG;cAC/CG,QAAQ,EAAE;;WAEb,CAAC;;QAEJ;MACF;QACE;IAAM;EAEZ;EAEAgB,QAAQ,CAAC9B,KAAK;IACZ,IAAI,IAAI,CAAC+B,IAAI,CAACC,OAAO,EAAE;MACrB;;IAEF;IACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;IACjC;IACAC,MAAM,CAACC,IAAI,CAACrC,KAAK,CAAC,CAACsC,OAAO,CAAE/B,GAAG,IAAI;MACjC0B,QAAQ,CAACE,MAAM,CAAC,QAAQ,IAAI,CAAClC,KAAK,CAACsC,EAAE,KAAKhC,GAAG,GAAG,EAAEP,KAAK,CAACO,GAAG,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEF;IACA,IAAI,CAACX,aAAa,CAAC4C,WAAW,CAACP,QAAQ,CAAC,CAACQ,SAAS,CAC/CC,GAAG,IAAI;MACN;MACA,IAAI,CAAC5C,YAAY,CAAC6C,OAAO,CACvB,IAAI,CAAChD,MAAM,CAACgB,OAAO,CAAC,4BAA4B,CAAC,CAClD;MACD,IAAI,CAACiC,YAAY,CAACC,IAAI,CAACH,GAAG,CAAC;IAC7B,CAAC,EACAI,GAAG,IAAI;MACN,IAAIA,GAAG,CAACC,cAAc,CAAC,aAAa,CAAC,EAAE;QACrCD,GAAG,CAACE,WAAW,CAACV,OAAO,CAAEW,KAAK,IAAI;UAChC,IAAI,CAAClB,IAAI,CAACmB,QAAQ,CAACD,KAAK,CAACE,IAAI,CAAC,CAACC,SAAS,CAAC;YACvCC,WAAW,EAAEJ,KAAK,CAACK;WACpB,CAAC;QACJ,CAAC,CAAC;OACH,MAAM;QACL9D,IAAI,CAAC+D,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEX,GAAG,CAACY,OAAO;UACjBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAC/D,iBAAiB,CAACc,OAAO,CAAC,IAAI,CAAC;UACvDkD,WAAW,EAAE;YACXC,aAAa,EAAE;;SAElB,CAAC;;IAEN,CAAC,CACF;EACH;EAAC;qBA5SUrE,qBAAqB;EAAA;EAAA;UAArBA,qBAAqB;IAAAsE;IAAAC;MAAA/D;MAAAwB;IAAA;IAAAwC;MAAArB;IAAA;IAAAsB;IAAAC;IAAAC;IAAAC;MAAA;QCtBlCC,8BAAkB;QAEWA,YAA6B;;QAAAA,iBAAK;QAE5DA,8BAAuB;QACGA;UAAA,OAAYC,uBAAe;QAAA,EAAC;QACjDD,iCAA2E;QAC3EA,iCAA0D;QAAAA,YAAqB;;QAAAA,iBAAS;;;QALnEA,eAA6B;QAA7BA,2DAA6B;QAGhDA,eAAkB;QAAlBA,oCAAkB;QACPA,eAAa;QAAbA,+BAAa;QACgCA,eAAqB;QAArBA,mDAAqB", "names": ["EventEmitter", "FormGroup", "AppConfig", "<PERSON><PERSON>", "StageDetailsComponent", "constructor", "_trans", "_stageService", "_translateService", "toastService", "ngOnInit", "model", "stage", "type", "TOURNAMENT_TYPES", "groups", "league", "fields", "key", "props", "max<PERSON><PERSON><PERSON>", "label", "instant", "placeholder", "pattern", "required", "min", "max", "wrappers", "options", "value", "RANKING_CRITERIA", "total", "description", "head_to_head", "knockout", "tournament", "type_knockout", "KNOCKOUT_TYPES", "type4", "push", "onSubmit", "form", "invalid", "formData", "FormData", "append", "Object", "keys", "for<PERSON>ach", "id", "updateStage", "subscribe", "res", "success", "onDataChange", "emit", "err", "hasOwnProperty", "fieldErrors", "error", "controls", "name", "setErrors", "serverError", "status", "fire", "title", "text", "message", "icon", "confirmButtonText", "customClass", "confirmButton", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-details\\stage-details.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-details\\stage-details.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'stage-details',\r\n  templateUrl: './stage-details.component.html',\r\n  styleUrls: ['./stage-details.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class StageDetailsComponent implements OnInit {\r\n  @Output() onDataChange = new EventEmitter<any>();\r\n  @Input() stage: any;\r\n  @Input() tournament: any;\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _stageService: StageService,\r\n    private _translateService: TranslateService,\r\n    public toastService: ToastrService\r\n  ) {}\r\n  form = new FormGroup({});\r\n  model: any = {};\r\n  fields: FormlyFieldConfig[];\r\n  ngOnInit(): void {\r\n    // console.log(this.stage);\r\n    this.model = this.stage;\r\n    switch (this.stage.type) {\r\n      case AppConfig.TOURNAMENT_TYPES.groups:\r\n      // break;\r\n      case AppConfig.TOURNAMENT_TYPES.league:\r\n        this.fields = [\r\n          {\r\n            key: 'name',\r\n            type: 'input',\r\n            props: {\r\n              maxLength: 30,\r\n              label: this._trans.instant('Stage name'),\r\n              placeholder: this._trans.instant('Enter stage name'),\r\n              pattern: /[\\S]/,\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_released',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Release'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_display_tbd',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Display TBD'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_display_results',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Display Results'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_display_table',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Display Table'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'no_encounters',\r\n            type: 'core-touchspin',\r\n            props: {\r\n              type: 'number',\r\n              min: 1,\r\n              max: 3,\r\n              label: this._trans.instant('Number of encounters'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'points_win',\r\n            type: 'core-touchspin',\r\n            props: {\r\n              type: 'number',\r\n              min: 0,\r\n              max: 100,\r\n              label: this._trans.instant('Points for win'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'points_draw',\r\n            type: 'core-touchspin',\r\n            props: {\r\n              type: 'number',\r\n              min: 0,\r\n              max: 100,\r\n              label: this._trans.instant('Points for draw'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'points_loss',\r\n            type: 'core-touchspin',\r\n            props: {\r\n              type: 'number',\r\n              min: 0,\r\n              max: 100,\r\n              label: this._trans.instant('Points for loss'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'ranking_criteria',\r\n            type: 'radio',\r\n            wrappers: ['details', 'form-field'],\r\n            props: {\r\n              label: this._trans.instant('Tie break order'),\r\n              required: true,\r\n              options: [\r\n                {\r\n                  value: AppConfig.RANKING_CRITERIA.total,\r\n                  label: this._trans.instant(AppConfig.RANKING_CRITERIA.total),\r\n                  description: `<div class=\"text-secondary text-12px\">\r\n                  1. ${this._trans.instant('Points')} - ${this._trans.instant(\r\n                    'Total'\r\n                  )}<br>\r\n                  2. ${this._trans.instant(\r\n                    'Goal difference'\r\n                  )} - ${this._trans.instant('Total')}<br>\r\n                  3. ${this._trans.instant(\r\n                    'Goals scored'\r\n                  )} - ${this._trans.instant('Total')}<br>\r\n                  4. ${this._trans.instant('Points')} - ${this._trans.instant(\r\n                    'Head to Head'\r\n                  )}<br>\r\n                  5. ${this._trans.instant(\r\n                    'Goal difference'\r\n                  )} - ${this._trans.instant('Head to Head')}<br>\r\n                  6. ${this._trans.instant(\r\n                    'Goals scored'\r\n                  )} - ${this._trans.instant('Head to Head')}</div>`,\r\n                },\r\n                {\r\n                  value: AppConfig.RANKING_CRITERIA.head_to_head,\r\n                  label: this._trans.instant(\r\n                    AppConfig.RANKING_CRITERIA.head_to_head\r\n                  ),\r\n                  description: `<div class=\"text-secondary text-12px\">\r\n                  1. ${this._trans.instant('Points')} - ${this._trans.instant(\r\n                    'Total'\r\n                  )}<br> \r\n                  2. ${this._trans.instant('Points')} - ${this._trans.instant(\r\n                    'Head to Head'\r\n                  )}<br> \r\n                  3. ${this._trans.instant(\r\n                    'Goal difference'\r\n                  )} - ${this._trans.instant('Head to Head')}<br>\r\n                  4. ${this._trans.instant(\r\n                    'Goals scored'\r\n                  )} - ${this._trans.instant('Head to Head')}<br>\r\n                  5. ${this._trans.instant(\r\n                    'Goal difference'\r\n                  )} - ${this._trans.instant('Total')}<br>\r\n                  6. ${this._trans.instant(\r\n                    'Goals scored'\r\n                  )} - ${this._trans.instant('Total')}</div>`,\r\n                },\r\n              ],\r\n            },\r\n          },\r\n          {\r\n            key: 'limit_update_score_time',\r\n            type: 'core-touchspin',\r\n            props: {\r\n              type: 'number',\r\n              min: 0,\r\n              max: 1000,\r\n              label: this._trans.instant('Limit update score time (hours)'),\r\n              required: false,\r\n            },\r\n          },\r\n        ];\r\n        break;\r\n      case AppConfig.TOURNAMENT_TYPES.knockout:\r\n        this.fields = [\r\n          {\r\n            key: 'name',\r\n            type: 'input',\r\n            props: {\r\n              maxLength: 15,\r\n              label: this._trans.instant('Stage name'),\r\n              placeholder: this._trans.instant('Enter stage name'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_released',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Release'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_display_tbd',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Display TBD'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_display_results',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Display Results'),\r\n              required: true,\r\n            },\r\n          },\r\n          {\r\n            key: 'is_display_table',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: this._trans.instant('Display Table'),\r\n              required: true,\r\n            },\r\n          },\r\n          // {\r\n          //   key: 'no_encounters',\r\n          //   type: 'radio',\r\n          //   props: {\r\n          //     label: this._trans.instant('Mode'),\r\n          //     required: true,\r\n          //     options: [\r\n          //       {\r\n          //         value: AppConfig.KNOCKOUT_MODES.single.value,\r\n          //         label: AppConfig.KNOCKOUT_MODES.single.label,\r\n          //       },\r\n          //       {\r\n          //         value: AppConfig.KNOCKOUT_MODES.double.value,\r\n          //         label: AppConfig.KNOCKOUT_MODES.double.label,\r\n          //       },\r\n          //     ],\r\n          //   },\r\n          // },\r\n        ];\r\n        if (this.tournament?.type_knockout === AppConfig.KNOCKOUT_TYPES.type4) {\r\n          this.fields.push({\r\n            key: 'third_place',\r\n            type: 'checkbox',\r\n            props: {\r\n              label: `${this._trans.instant('Third place')}?`,\r\n              required: true,\r\n            },\r\n          });\r\n        }\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  onSubmit(model) {\r\n    if (this.form.invalid) {\r\n      return;\r\n    }\r\n    // create FormData object\r\n    const formData = new FormData();\r\n    formData.append('action', 'edit');\r\n    // foreach key in model add to formData object. e.g. formData.append('data[id][name]', 'value')\r\n    Object.keys(model).forEach((key) => {\r\n      formData.append(`data[${this.stage.id}][${key}]`, model[key]);\r\n    });\r\n\r\n    // send form data to server\r\n    this._stageService.updateStage(formData).subscribe(\r\n      (res) => {\r\n        // console.log(res);\r\n        this.toastService.success(\r\n          this._trans.instant('Stage updated successfully')\r\n        );\r\n        this.onDataChange.emit(res);\r\n      },\r\n      (err) => {\r\n        if (err.hasOwnProperty('fieldErrors')) {\r\n          err.fieldErrors.forEach((error) => {\r\n            this.form.controls[error.name].setErrors({\r\n              serverError: error.status,\r\n            });\r\n          });\r\n        } else {\r\n          Swal.fire({\r\n            title: 'Error',\r\n            text: err.message,\r\n            icon: 'error',\r\n            confirmButtonText: this._translateService.instant('OK'),\r\n            customClass: {\r\n              confirmButton: 'btn btn-primary',\r\n            },\r\n          });\r\n        }\r\n      }\r\n    );\r\n  }\r\n}\r\n", "<div class=\"card\">\r\n   <div class=\"card-header\">\r\n      <h3 class=\"card-title\">{{'Stage Details'|translate}}</h3>\r\n   </div>\r\n   <div class=\"card-body\">\r\n    <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit(model)\">\r\n        <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n        <button type=\"submit\" class=\"btn btn-primary float-right\">{{'Save'|translate }}</button>\r\n      </form>\r\n   </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}