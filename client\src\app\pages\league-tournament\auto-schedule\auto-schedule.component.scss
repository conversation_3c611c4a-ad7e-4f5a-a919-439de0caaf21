$column-width: 26rem;
$drag-item-height: 10rem;
$conflict-border: 1px solid rgba(255, 0, 0, 0.5) !important;

.home-team,
.away-team {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

img.team-logo {
  width: 2.5rem;
  height: 2.5rem;
}

.swap-button {
  color: rgba(45, 103, 241, 1);
  background: rgba(45, 103, 241, 0.2);
  border-width: 0;
  padding: 0.25rem 1rem;
  border-radius: 0.5rem;
}

.top-header {
  position: sticky;
  top: 0;
  z-index: 15;
  background-color: white;
  padding: 1rem;
  margin: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  min-width: 100%;

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
    width: 100%;
    min-width: 100%;
  }
}

.dnd-zone {
  background: #fff;
  min-height: $drag-item-height;
}

.dnd-item {
  user-select: none;
  border-top: 1px solid rgba(168, 170, 174, 0.25);
  padding: 1rem 0.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
  height: $drag-item-height;
  cursor: grab;
  position: relative;
  //transition: transform 0.1s;
  background: #fff;

  .conflict-tooltip {
    padding: 0;
    position: absolute;
    top: 4px;
    right: 4px;
    color: red;
  }

  &:hover {
    background-color: rgba(45, 103, 241, 0.05);
  }

  &:active {
    cursor: grabbing;
  }

  &.cdk-drag-preview {
    z-index: 999;
    border-radius: 8px;
    border: 1px solid #a8aaae;
    scale: 0.95;
    // Hide dropdown during drag
    .item-dropdown {
      display: none !important;
    }

    &.conflict-border {
      border: $conflict-border;
    }
  }

  &.location-match-row {
    padding: 1.5rem 2rem;
  }

  .match-info-header {
    text-align: center;
    color: rgba(75, 70, 92, 1);
  }

  .item-dropdown {
    position: absolute;
    top: 0;
    left: 100%;
    margin-left: -2rem;
    z-index: 1000;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(168, 170, 174, 0.25);
    min-width: 180px;
    overflow: hidden;
    display: none;

    &.visible {
      display: block;
    }

    .dropdown-content {
      .dropdown-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.75rem 1rem;
        border: none;
        background: none;
        text-align: left;
        color: rgba(75, 70, 92, 1);
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.2s ease;
        white-space: nowrap;

        &:hover {
          background-color: rgba(45, 103, 241, 0.1);
          color: rgba(45, 103, 241, 1);
        }

        &:focus {
          outline: none;
          background-color: rgba(45, 103, 241, 0.1);
          color: rgba(45, 103, 241, 1);
        }

        i {
          width: 16px;
          text-align: center;
          margin-right: 0.5rem;
        }
      }
    }
  }

  .team-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .team-name {
    margin: 0;
  }

  .match-date {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    color: rgba(75, 70, 92, 1);
  }

  .referees-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    color: rgba(75, 70, 92, 1);
  }

  .referee-names {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .referee-name {
    display: flex;
    gap: 0.25rem;
    align-items: center;
  }

  .break-info-header {
    text-align: center;
    color: rgba(16, 15, 15, 1);
  }

  .break-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    color: rgba(75, 70, 92, 1);
  }
}

// Ensure proper positioning context for sticky header
.content-wrapper {
  overflow: visible !important;
  position: relative;
}

.content-body {
  position: relative;
  overflow: visible;
  width: 100%;
}

// Horizontal scrolling container for location zones
#listLocationZone {
  position: relative;
  width: 100%;
  overflow-x: auto;
  padding: 0 !important;
  margin: 1rem 0;

  .horizontal-scroll-container {
    padding-bottom: 1rem;
    scroll-behavior: smooth;
    width: 100%;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(45, 103, 241, 0.5);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(45, 103, 241, 0.7);
    }

    .location-columns-wrapper {
      display: flex;
      gap: 1rem;
      min-width: max-content;
      width: max-content;
      position: relative;

      // Add 32rem (512px) of additional scrollable space at the end
      &::after {
        content: '';
        display: block;
        min-width: 28rem; // Additional 32rem scrollable space beyond last column
        flex-shrink: 0; // Prevent the extra space from shrinking
        height: 1px; // Minimal height to ensure it takes up space
      }
    }
  }
}

#listUnScheduleZone {
  // width: $column-width;
  width: 100%;
  position: absolute;
  top: calc(10rem - 2px);
  right: 0;
  height: 100%;
  z-index: 10;
  margin: 1rem 0;

  // Fixed positioning for unscheduled zone
  .unschedule-container {
    position: sticky !important;
    top: 8rem;
    //align-self: flex-start;
    height: max-content;
    z-index: 10;
    width: 100%;
    right: 0;
    margin: 1rem 0;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    @media (max-width: 1200px) {
      width: 100%;
      max-width: 100%;
      right: 0;
      margin: 1rem 0;
    }

    #unScheduleZone {
      z-index: 100;
      max-height: 50vh;
      overflow-y: auto;
    }

    .unplanned-matches-container {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      height: max-content;
      max-height: calc(100vh - 15rem);

      &.location-column {
        width: 100%;
      }

      .location-header {
        background-color: white !important;
        border-bottom: 1px solid rgba(168, 170, 174, 0.25);
      }
    }
  }
}

.location-column {
  width: $column-width;
  height: max-content;
  overflow: visible;
  border: 1px solid #eee;

  &.conflict {
    .location-header {
      background: rgba(255, 0, 0, 0.1);

      .location-name {
        color: rgba(255, 0, 0, 1);
      }

      .location-stage-name {
        color: rgba(255, 0, 0, 1);
      }

      .btn.btn-link {
        color: rgba(255, 0, 0, 1);
      }
    }
  }

  .stage-conflict-tooltip {
    padding: 0;
    position: absolute;
    bottom: 1rem;
    right: 0;
    color: red;
  }

  .location-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem 0.5rem;
    background: rgba(45, 103, 241, 0.25);
    justify-content: space-between;
    height: 8rem;
    position: relative;

    .btn.btn-link {
      padding: 0 1rem;
    }

    .dropdown-toggle {
      border: none;

      &::after {
        width: 0;
        background-image: none;
      }
    }

    .location-name {
      font-weight: 600;
      margin: 0;
      width: 90%;
      color: rgba(45, 103, 241, 1);
    }

    .location-stage-name {
      margin: 0;
      color: rgba(45, 103, 241, 1);
    }
  }

  .location-footer {
    border-top: 1px solid rgba(168, 170, 174, 0.25);
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .top-header {
    padding: 1rem;

    .row {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }
  }

  #listUnScheduleZone {
    position: relative !important;
    left: auto;
    right: auto;
    top: auto;
    width: 100%;
    max-width: 100%;
    margin-top: 2rem;
  }

  .location-column {
    min-width: 250px;
  }
}

@media (max-width: 576px) {
  .location-column {
    min-width: 200px;
  }

  .top-header {
    padding: 0.75rem;

    .row {
      padding-left: 0.25rem;
      padding-right: 0.25rem;
    }
  }
}

@media (max-width: 768px) {
  .dnd-item .item-dropdown {
    // On mobile, position dropdown below the item instead of to the right
    left: 0;
    top: 100%;
    margin-left: 0;
    margin-top: 5px;
    min-width: 200px;
    max-width: 90vw;
  }
}

.horizontal-scroll-container {
  // Ensure dropdown positioning works with horizontal scroll
  overflow: visible;
}

.conflict-border {
  border: $conflict-border;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-disabled {
  background: rgba(220, 220, 220, 0.25);
  cursor: default;

  &:hover {
    background: rgba(220, 220, 220, 0.25);
  }
}

#fetchingState,
#noSchedule {
  width: 75%;
}

#notHaveMatches {
  width: 100%;
  min-height: $drag-item-height;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.dropdown-menu {
  z-index: 1000;
}
