{"ast": null, "code": "import { environment } from 'environments/environment';\nimport { throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class StageService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getStage(id) {\n    return this._http.get(`${environment.apiUrl}/stages/${id}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getDataByTournament(tournament_id) {\n    return this._http.post(`${environment.apiUrl}/stages/all-in-tournament/${tournament_id}`, {}).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getTeamsInStage(id) {\n    // POST /stage-teams/all-in-stage/1\n    return this._http.post(`${environment.apiUrl}/stage-teams/all-in-stage/${id}`, {}).pipe(map(res => {\n      return this.decodeHtmlEntities(res);\n    }));\n  }\n  addTeamToStage(data) {\n    return this._http.post(`${environment.apiUrl}/stage-teams/editor`, data).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  swapTeams(stageMatch) {\n    console.log('stageMatch', stageMatch);\n    const body = {\n      stage_id: stageMatch.id,\n      home_team_id: stageMatch.home_team_id,\n      away_team_id: stageMatch.away_team_id,\n      home_score: stageMatch.home_score,\n      away_score: stageMatch.away_score,\n      home_penalty: stageMatch.home_penalty ?? null,\n      away_penalty: stageMatch.away_penalty ?? null\n    };\n    return this._http.post(`${environment.apiUrl}/stage-matches/swap`, body).pipe(map(res => res), catchError(err => throwError(err)));\n  }\n  createTeamMultiple(params) {\n    return this._http.post(`${environment.apiUrl}/stage-teams/create`, params).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  editGroup(params) {\n    return this._http.post(`${environment.apiUrl}/stage-teams/edit-group`, params).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  removeTeamMultiple(params) {\n    return this._http.post(`${environment.apiUrl}/stage-teams/delete`, params).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  removeTeamFromStage(params) {\n    return this._http.post(`${environment.apiUrl}/stage-teams/editor`, params).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  changeTeamInStage(params) {\n    // stage-team/editor\n    return this._http.post(`${environment.apiUrl}/stage-teams/editor`, params).pipe(map(res => {\n      return res;\n    }));\n  }\n  getTableData(id, query = '') {\n    return this._http.get(`${environment.apiUrl}/stages/${id}/table${query}`).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  updateStage(data) {\n    return this._http.post(`${environment.apiUrl}/stages/editor`, data).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  autoGenerateMatches(stage_id) {\n    const data = new FormData();\n    data.append('stage_id', stage_id.toString());\n    return this._http.post(`${environment.apiUrl}/stages/auto-generate`, data).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  resetScore(stage_match_ids) {\n    const data = new FormData();\n    data.append('stage_match_ids', stage_match_ids.toString());\n    return this._http.post(`${environment.apiUrl}/stage-matches/resetScore`, data).pipe(map(res => {\n      return res;\n    }), catchError(err => {\n      return throwError(err);\n    }));\n  }\n  getMatchesInStage(id) {\n    return this._http.post(`${environment.apiUrl}/stage-matches/all-in-stage/${id}`, {}).pipe(map(res => {\n      return res;\n    }));\n  }\n  hasMatches(id) {\n    return this._http.get(`${environment.apiUrl}/stages/has-matches/${id}`, {}).pipe(map(res => {\n      return res;\n    }));\n  }\n  deleteMatchesInStage(ids, stage_id) {\n    let formData = new FormData();\n    formData.append('action', 'remove');\n    ids.forEach(id => {\n      formData.append(`data[${id}][stage_id]`, stage_id.toString());\n    });\n    return this._http.post(`${environment.apiUrl}/stage-matches/editor`, formData).pipe(map(res => {\n      return res;\n    }));\n  }\n  getLiveMatchesAvailable() {\n    return this._http.get(`${environment.apiUrl}/stage-matches/live-matches`).pipe(map(res => {\n      return res;\n    }));\n  }\n  getStreamingMatches(status = null) {\n    return this._http.get(`${environment.apiUrl}/stage-matches/streaming/${status}`).pipe(map(res => {\n      return res;\n    }));\n  }\n  getUserStreamingMatches(user_id) {\n    return this._http.get(`${environment.apiUrl}/match-streaming/match-user/${user_id}`).pipe(map(res => {\n      return res;\n    }));\n  }\n  updateBroadcastId(match_id, broadcast_id, broadcast_status) {\n    return this._http.post(`${environment.apiUrl}/stage-matches/update-broadcast`, {\n      broadcast_id: broadcast_id,\n      broadcast_status: broadcast_status,\n      match_id: match_id\n    }).pipe(map(res => {\n      return res;\n    }));\n  }\n  // decoding &amp; into the character &\n  decodeHtmlEntities(data) {\n    if (typeof data === 'string') {\n      const txt = document.createElement('textarea');\n      txt.innerHTML = data;\n      return txt.value;\n    } else if (Array.isArray(data)) {\n      return data.map(item => this.decodeHtmlEntities(item));\n    } else if (typeof data === 'object' && data !== null) {\n      const decodedData = {};\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          decodedData[key] = this.decodeHtmlEntities(data[key]);\n        }\n      }\n      return decodedData;\n    }\n    return data;\n  }\n  getRankLabe(stage_id) {\n    return this._http.get(`${environment.apiUrl}/stage-matches/rankings/${stage_id}`).pipe(map(res => {\n      return res;\n    }));\n  }\n  checkMatchScore(stage_id) {\n    return this._http.post(`${environment.apiUrl}/stages/check-scores`, {\n      stage_id: stage_id\n    }).pipe(map(res => {\n      return res;\n    }));\n  }\n  assignReferees(listMatches, listReferees) {\n    return this._http.post(`${environment.apiUrl}/stage-matches/assign-referees`, {\n      list_match_ids: listMatches,\n      list_referees: listReferees\n    }).pipe(map(res => {\n      return res;\n    }));\n  }\n  getListRefereesByStageId(stageId) {\n    return this._http.get(`${environment.apiUrl}/stages/${stageId}/referees`);\n  }\n  getListStageMatches(tournament_id) {\n    return this._http.get(`${environment.apiUrl}/stage-matches/tournament/${tournament_id}/matches`);\n  }\n  submitTeamOrder(newStageTeamOrder) {\n    return this._http.put(`${environment.apiUrl}/stages/submit-order`, {\n      newStageTeamOrder\n    });\n  }\n  checkCanUpdateLeaderboard(stageId) {\n    return this._http.get(`${environment.apiUrl}/stages/${stageId}/can-update-leaderboard`);\n  }\n  static #_ = this.ɵfac = function StageService_Factory(t) {\n    return new (t || StageService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: StageService,\n    factory: StageService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,WAAW,QAAQ,0BAA0B;AACtD,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;AAKhD,OAAM,MAAOC,YAAY;EACvBC,YAAmBC,KAAiB;IAAjB,UAAK,GAALA,KAAK;EACxB;EAEAC,QAAQ,CAACC,EAAE;IACT,OAAO,IAAI,CAACF,KAAK,CAACG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,WAAWF,EAAE,EAAE,CAAC,CAACG,IAAI,CAC9DT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACH;EAEAC,mBAAmB,CAACC,aAAa;IAC/B,OAAO,IAAI,CAACT,KAAK,CACdU,IAAI,CACH,GAAGhB,WAAW,CAACU,MAAM,6BAA6BK,aAAa,EAAE,EACjE,EAAE,CACH,CACAJ,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAI,eAAe,CAACT,EAAE;IAChB;IACA,OAAO,IAAI,CAACF,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,6BAA6BF,EAAE,EAAE,EAAE,EAAE,CAAC,CAChEG,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAO,IAAI,CAACM,kBAAkB,CAACN,GAAG,CAAC;IACrC,CAAC,CAAC,CACH;EACL;EAEAO,cAAc,CAACC,IAAI;IACjB,OAAO,IAAI,CAACd,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,qBAAqB,EAAEU,IAAI,CAAC,CACtDT,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAQ,SAAS,CAACC,UAAsB;IAC9BC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,UAAU,CAAC;IAErC,MAAMG,IAAI,GAAG;MACXC,QAAQ,EAAEJ,UAAU,CAACd,EAAE;MACvBmB,YAAY,EAAEL,UAAU,CAACK,YAAY;MACrCC,YAAY,EAAEN,UAAU,CAACM,YAAY;MACrCC,UAAU,EAAEP,UAAU,CAACO,UAAU;MACjCC,UAAU,EAAER,UAAU,CAACQ,UAAU;MACjCC,YAAY,EAAET,UAAU,CAACS,YAAY,IAAI,IAAI;MAC7CC,YAAY,EAAEV,UAAU,CAACU,YAAY,IAAI;KAC1C;IAED,OAAO,IAAI,CAAC1B,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,qBAAqB,EAAEe,IAAI,CAAC,CACtDd,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAKA,GAAG,CAAC,EACtBT,UAAU,CAAEU,GAAG,IAAKZ,UAAU,CAACY,GAAG,CAAC,CAAC,CACrC;EACL;EAEAoB,kBAAkB,CAACC,MAAgB;IACjC,OAAO,IAAI,CAAC5B,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,qBAAqB,EAAEwB,MAAM,CAAC,CACxDvB,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAsB,SAAS,CAACD,MAAgB;IACxB,OAAO,IAAI,CAAC5B,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,yBAAyB,EAAEwB,MAAM,CAAC,CAC5DvB,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAuB,kBAAkB,CAACF,MAAgB;IACjC,OAAO,IAAI,CAAC5B,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,qBAAqB,EAAEwB,MAAM,CAAC,CACxDvB,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAwB,mBAAmB,CAACH,MAAgB;IAClC,OAAO,IAAI,CAAC5B,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,qBAAqB,EAAEwB,MAAM,CAAC,CACxDvB,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAyB,iBAAiB,CAACJ,MAAgB;IAChC;IAEA,OAAO,IAAI,CAAC5B,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,qBAAqB,EAAEwB,MAAM,CAAC,CACxDvB,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA2B,YAAY,CAAC/B,EAAE,EAAEgC,KAAK,GAAG,EAAE;IACzB,OAAO,IAAI,CAAClC,KAAK,CACdG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,WAAWF,EAAE,SAASgC,KAAK,EAAE,CAAC,CACvD7B,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEA4B,WAAW,CAACrB,IAAc;IACxB,OAAO,IAAI,CAACd,KAAK,CAACU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,gBAAgB,EAAEU,IAAI,CAAC,CAACT,IAAI,CACtET,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACH;EAEA6B,mBAAmB,CAAChB,QAAgB;IAClC,MAAMN,IAAI,GAAG,IAAIuB,QAAQ,EAAE;IAC3BvB,IAAI,CAACwB,MAAM,CAAC,UAAU,EAAElB,QAAQ,CAACmB,QAAQ,EAAE,CAAC;IAC5C,OAAO,IAAI,CAACvC,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,uBAAuB,EAAEU,IAAI,CAAC,CACxDT,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAiC,UAAU,CAACC,eAAuB;IAChC,MAAM3B,IAAI,GAAG,IAAIuB,QAAQ,EAAE;IAC3BvB,IAAI,CAACwB,MAAM,CAAC,iBAAiB,EAAEG,eAAe,CAACF,QAAQ,EAAE,CAAC;IAC1D,OAAO,IAAI,CAACvC,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,2BAA2B,EAAEU,IAAI,CAAC,CAC5DT,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFT,UAAU,CAAEU,GAAG,IAAI;MACjB,OAAOZ,UAAU,CAACY,GAAG,CAAC;IACxB,CAAC,CAAC,CACH;EACL;EAEAmC,iBAAiB,CAACxC,EAAE;IAClB,OAAO,IAAI,CAACF,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,+BAA+BF,EAAE,EAAE,EAAE,EAAE,CAAC,CAClEG,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEAqC,UAAU,CAACzC,EAAE;IACX,OAAO,IAAI,CAACF,KAAK,CACdG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,uBAAuBF,EAAE,EAAE,EAAE,EAAE,CAAC,CACzDG,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEAsC,oBAAoB,CAACC,GAAa,EAAEzB,QAAgB;IAClD,IAAI0B,QAAQ,GAAG,IAAIT,QAAQ,EAAE;IAC7BS,QAAQ,CAACR,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACnCO,GAAG,CAACE,OAAO,CAAE7C,EAAE,IAAI;MACjB4C,QAAQ,CAACR,MAAM,CAAC,QAAQpC,EAAE,aAAa,EAAEkB,QAAQ,CAACmB,QAAQ,EAAE,CAAC;IAC/D,CAAC,CAAC;IAEF,OAAO,IAAI,CAACvC,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,uBAAuB,EAAE0C,QAAQ,CAAC,CAC5DzC,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA0C,uBAAuB;IACrB,OAAO,IAAI,CAAChD,KAAK,CACdG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,6BAA6B,CAAC,CACvDC,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA2C,mBAAmB,CAACC,SAAiB,IAAI;IACvC,OAAO,IAAI,CAAClD,KAAK,CACdG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,4BAA4B8C,MAAM,EAAE,CAAC,CAC9D7C,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA6C,uBAAuB,CAACC,OAAO;IAC7B,OAAO,IAAI,CAACpD,KAAK,CACdG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,+BAA+BgD,OAAO,EAAE,CAAC,CAClE/C,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA+C,iBAAiB,CACfC,QAAgB,EAChBC,YAAoB,EACpBC,gBAAwB;IAExB,OAAO,IAAI,CAACxD,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,iCAAiC,EAAE;MAC5DmD,YAAY,EAAEA,YAAY;MAC1BC,gBAAgB,EAAEA,gBAAgB;MAClCF,QAAQ,EAAEA;KACX,CAAC,CACDjD,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA;EACAM,kBAAkB,CAACE,IAAS;IAC1B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM2C,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MAC9CF,GAAG,CAACG,SAAS,GAAG9C,IAAI;MACpB,OAAO2C,GAAG,CAACI,KAAK;KACjB,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACjD,IAAI,CAAC,EAAE;MAC9B,OAAOA,IAAI,CAAClB,GAAG,CAAEoE,IAAI,IAAK,IAAI,CAACpD,kBAAkB,CAACoD,IAAI,CAAC,CAAC;KACzD,MAAM,IAAI,OAAOlD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MACpD,MAAMmD,WAAW,GAAG,EAAE;MACtB,KAAK,MAAMC,GAAG,IAAIpD,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACqD,cAAc,CAACD,GAAG,CAAC,EAAE;UAC5BD,WAAW,CAACC,GAAG,CAAC,GAAG,IAAI,CAACtD,kBAAkB,CAACE,IAAI,CAACoD,GAAG,CAAC,CAAC;;;MAGzD,OAAOD,WAAW;;IAEpB,OAAOnD,IAAI;EACb;EAEAsD,WAAW,CAAChD,QAAgB;IAC1B,OAAO,IAAI,CAACpB,KAAK,CACdG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,2BAA2BgB,QAAQ,EAAE,CAAC,CAC/Df,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEA+D,eAAe,CAACjD,QAAgB;IAC9B,OAAO,IAAI,CAACpB,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,sBAAsB,EAAE;MACjDgB,QAAQ,EAAEA;KACX,CAAC,CACDf,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEAgE,cAAc,CAACC,WAAW,EAAEC,YAAY;IACtC,OAAO,IAAI,CAACxE,KAAK,CACdU,IAAI,CAAC,GAAGhB,WAAW,CAACU,MAAM,gCAAgC,EAAE;MAC3DqE,cAAc,EAAEF,WAAW;MAC3BG,aAAa,EAAEF;KAChB,CAAC,CACDnE,IAAI,CACHT,GAAG,CAAEU,GAAQ,IAAI;MACf,OAAOA,GAAG;IACZ,CAAC,CAAC,CACH;EACL;EAEAqE,wBAAwB,CAACC,OAAO;IAC9B,OAAO,IAAI,CAAC5E,KAAK,CAACG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,WAAWwE,OAAO,WAAW,CAAC;EAC3E;EAEAC,mBAAmB,CAACpE,aAA8B;IAChD,OAAO,IAAI,CAACT,KAAK,CAACG,GAAG,CACnB,GAAGT,WAAW,CAACU,MAAM,6BAA6BK,aAAa,UAAU,CAC1E;EACH;EAGAqE,eAAe,CAACC,iBAAiB;IAC/B,OAAO,IAAI,CAAC/E,KAAK,CAACgF,GAAG,CAAC,GAAGtF,WAAW,CAACU,MAAM,sBAAsB,EAAE;MACjE2E;KACD,CAAC;EACJ;EAEAE,yBAAyB,CAACL,OAAe;IACvC,OAAO,IAAI,CAAC5E,KAAK,CAACG,GAAG,CAAC,GAAGT,WAAW,CAACU,MAAM,WAAWwE,OAAO,yBAAyB,CAAC;EACzF;EAAC;qBAhWU9E,YAAY;EAAA;EAAA;WAAZA,YAAY;IAAAoF,SAAZpF,YAAY;IAAAqF,YAFX;EAAM", "names": ["environment", "throwError", "map", "catchError", "StageService", "constructor", "_http", "getStage", "id", "get", "apiUrl", "pipe", "res", "err", "getDataByTournament", "tournament_id", "post", "getTeamsInStage", "decodeHtmlEntities", "addTeamToStage", "data", "swapTeams", "stageMatch", "console", "log", "body", "stage_id", "home_team_id", "away_team_id", "home_score", "away_score", "home_penalty", "away_penalty", "createTeamMultiple", "params", "editGroup", "removeTeamMultiple", "removeTeamFromStage", "changeTeamInStage", "getTableData", "query", "updateStage", "autoGenerateMatches", "FormData", "append", "toString", "resetScore", "stage_match_ids", "getMatchesInStage", "hasMatches", "deleteMatchesInStage", "ids", "formData", "for<PERSON>ach", "getLiveMatchesAvailable", "getStreamingMatches", "status", "getUserStreamingMatches", "user_id", "updateBroadcastId", "match_id", "broadcast_id", "broadcast_status", "txt", "document", "createElement", "innerHTML", "value", "Array", "isArray", "item", "decodedData", "key", "hasOwnProperty", "getRankLabe", "checkMatchScore", "assignReferees", "listMatches", "listReferees", "list_match_ids", "list_referees", "getListRefereesByStageId", "stageId", "getListStageMatches", "submitTeamOrder", "newStageTeamOrder", "put", "checkCanUpdateLeaderboard", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\services\\stage.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { StageMatch } from 'app/interfaces/stages';\r\nimport { environment } from 'environments/environment';\r\nimport { throwError } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class StageService {\r\n  constructor(public _http: HttpClient) {\r\n  }\r\n\r\n  getStage(id) {\r\n    return this._http.get(`${environment.apiUrl}/stages/${id}`).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      }),\r\n      catchError((err) => {\r\n        return throwError(err);\r\n      })\r\n    );\r\n  }\r\n\r\n  getDataByTournament(tournament_id) {\r\n    return this._http\r\n      .post(\r\n        `${environment.apiUrl}/stages/all-in-tournament/${tournament_id}`,\r\n        {}\r\n      )\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getTeamsInStage(id) {\r\n    // POST /stage-teams/all-in-stage/1\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/all-in-stage/${id}`, {})\r\n      .pipe(\r\n        map((res: any) => {\r\n          return this.decodeHtmlEntities(res);\r\n        })\r\n      );\r\n  }\r\n\r\n  addTeamToStage(data) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/editor`, data)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  swapTeams(stageMatch: StageMatch) {\r\n    console.log('stageMatch', stageMatch);\r\n\r\n    const body = {\r\n      stage_id: stageMatch.id,\r\n      home_team_id: stageMatch.home_team_id,\r\n      away_team_id: stageMatch.away_team_id,\r\n      home_score: stageMatch.home_score,\r\n      away_score: stageMatch.away_score,\r\n      home_penalty: stageMatch.home_penalty ?? null,\r\n      away_penalty: stageMatch.away_penalty ?? null\r\n    };\r\n\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-matches/swap`, body)\r\n      .pipe(\r\n        map((res: any) => res),\r\n        catchError((err) => throwError(err))\r\n      );\r\n  }\r\n\r\n  createTeamMultiple(params: FormData) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/create`, params)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  editGroup(params: FormData) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/edit-group`, params)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  removeTeamMultiple(params: FormData) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/delete`, params)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  removeTeamFromStage(params: FormData) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/editor`, params)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  changeTeamInStage(params: FormData) {\r\n    // stage-team/editor\r\n\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-teams/editor`, params)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getTableData(id, query = '') {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stages/${id}/table${query}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  updateStage(data: FormData) {\r\n    return this._http.post(`${environment.apiUrl}/stages/editor`, data).pipe(\r\n      map((res: any) => {\r\n        return res;\r\n      }),\r\n      catchError((err) => {\r\n        return throwError(err);\r\n      })\r\n    );\r\n  }\r\n\r\n  autoGenerateMatches(stage_id: number) {\r\n    const data = new FormData();\r\n    data.append('stage_id', stage_id.toString());\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stages/auto-generate`, data)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  resetScore(stage_match_ids: number) {\r\n    const data = new FormData();\r\n    data.append('stage_match_ids', stage_match_ids.toString());\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-matches/resetScore`, data)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        }),\r\n        catchError((err) => {\r\n          return throwError(err);\r\n        })\r\n      );\r\n  }\r\n\r\n  getMatchesInStage(id) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-matches/all-in-stage/${id}`, {})\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  hasMatches(id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stages/has-matches/${id}`, {})\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  deleteMatchesInStage(ids: number[], stage_id: number) {\r\n    let formData = new FormData();\r\n    formData.append('action', 'remove');\r\n    ids.forEach((id) => {\r\n      formData.append(`data[${id}][stage_id]`, stage_id.toString());\r\n    });\r\n\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-matches/editor`, formData)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getLiveMatchesAvailable() {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stage-matches/live-matches`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getStreamingMatches(status: string = null) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stage-matches/streaming/${status}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getUserStreamingMatches(user_id) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/match-streaming/match-user/${user_id}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  updateBroadcastId(\r\n    match_id: number,\r\n    broadcast_id: string,\r\n    broadcast_status: string\r\n  ) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-matches/update-broadcast`, {\r\n        broadcast_id: broadcast_id,\r\n        broadcast_status: broadcast_status,\r\n        match_id: match_id\r\n      })\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  // decoding &amp; into the character &\r\n  decodeHtmlEntities(data: any): any {\r\n    if (typeof data === 'string') {\r\n      const txt = document.createElement('textarea');\r\n      txt.innerHTML = data;\r\n      return txt.value;\r\n    } else if (Array.isArray(data)) {\r\n      return data.map((item) => this.decodeHtmlEntities(item));\r\n    } else if (typeof data === 'object' && data !== null) {\r\n      const decodedData = {};\r\n      for (const key in data) {\r\n        if (data.hasOwnProperty(key)) {\r\n          decodedData[key] = this.decodeHtmlEntities(data[key]);\r\n        }\r\n      }\r\n      return decodedData;\r\n    }\r\n    return data;\r\n  }\r\n\r\n  getRankLabe(stage_id: number) {\r\n    return this._http\r\n      .get(`${environment.apiUrl}/stage-matches/rankings/${stage_id}`)\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  checkMatchScore(stage_id: number) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stages/check-scores`, {\r\n        stage_id: stage_id\r\n      })\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  assignReferees(listMatches, listReferees) {\r\n    return this._http\r\n      .post(`${environment.apiUrl}/stage-matches/assign-referees`, {\r\n        list_match_ids: listMatches,\r\n        list_referees: listReferees\r\n      })\r\n      .pipe(\r\n        map((res: any) => {\r\n          return res;\r\n        })\r\n      );\r\n  }\r\n\r\n  getListRefereesByStageId(stageId) {\r\n    return this._http.get(`${environment.apiUrl}/stages/${stageId}/referees`);\r\n  }\r\n\r\n  getListStageMatches(tournament_id: string | number) {\r\n    return this._http.get<any>(\r\n      `${environment.apiUrl}/stage-matches/tournament/${tournament_id}/matches`\r\n    );\r\n  }\r\n\r\n\r\n  submitTeamOrder(newStageTeamOrder) {\r\n    return this._http.put(`${environment.apiUrl}/stages/submit-order`, {\r\n      newStageTeamOrder\r\n    });\r\n  }\r\n\r\n  checkCanUpdateLeaderboard(stageId: number) {\r\n    return this._http.get(`${environment.apiUrl}/stages/${stageId}/can-update-leaderboard`);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}