{"ast": null, "code": "import { FormsModule } from \"@angular/forms\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { BtnDropdownActionComponent } from \"./btn-dropdown-action.component\";\nimport { NgbDropdownModule } from \"@ng-bootstrap/ng-bootstrap\";\nimport { CoreCommonModule } from \"@core/common.module\";\nimport * as i0 from \"@angular/core\";\nexport class BtnDropdownActionModule {\n  static #_ = this.ɵfac = function BtnDropdownActionModule_Factory(t) {\n    return new (t || BtnDropdownActionModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BtnDropdownActionModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, TranslateModule, NgbDropdownModule, CoreCommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BtnDropdownActionModule, {\n    declarations: [BtnDropdownActionComponent],\n    imports: [CommonModule, FormsModule, TranslateModule, NgbDropdownModule, CoreCommonModule],\n    exports: [BtnDropdownActionComponent]\n  });\n})();", "map": {"version": 3, "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,qBAAqB;;AAOtD,OAAM,MAAOC,uBAAuB;EAAA;qBAAvBA,uBAAuB;EAAA;EAAA;UAAvBA;EAAuB;EAAA;cAHxBJ,YAAY,EAAEF,WAAW,EAAEC,eAAe,EAAEG,iBAAiB,EAACC,gBAAgB;EAAA;;;2EAG7EC,uBAAuB;IAAAC,eAJnBJ,0BAA0B;IAAAK,UAC/BN,YAAY,EAAEF,WAAW,EAAEC,eAAe,EAAEG,iBAAiB,EAACC,gBAAgB;IAAAI,UAC9EN,0BAA0B;EAAA;AAAA", "names": ["FormsModule", "TranslateModule", "CommonModule", "BtnDropdownActionComponent", "NgbDropdownModule", "CoreCommonModule", "BtnDropdownActionModule", "declarations", "imports", "exports"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\components\\btn-dropdown-action\\btn-dropdown-action.module.ts"], "sourcesContent": ["import { FormsModule } from \"@angular/forms\";\r\nimport { TranslateModule } from \"@ngx-translate/core\";\r\nimport { NgModule } from \"@angular/core\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { BtnDropdownActionComponent } from \"./btn-dropdown-action.component\";\r\nimport { NgbDropdownModule } from \"@ng-bootstrap/ng-bootstrap\";\r\nimport { CoreCommonModule } from \"@core/common.module\";\r\n\r\n@NgModule({\r\n  declarations: [BtnDropdownActionComponent],\r\n  imports: [CommonModule, FormsModule, TranslateModule, NgbDropdownModule,CoreCommonModule],\r\n  exports: [BtnDropdownActionComponent],\r\n})\r\nexport class BtnDropdownActionModule {}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}