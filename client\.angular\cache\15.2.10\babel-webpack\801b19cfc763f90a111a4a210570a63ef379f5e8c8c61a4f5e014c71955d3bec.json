{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from 'environments/environment';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PhotoGalleryService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  uploadPhoto(file, groupId, seasonId) {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('group_id', groupId.toString());\n    formData.append('season_id', seasonId.toString());\n    return this._http.post(`${environment.apiUrl}/photo-gallery/create`, formData).pipe(map(data => {\n      return data;\n    }));\n  }\n  deletePhotos(data) {\n    let params = new HttpParams();\n    params = params.append('ids', data);\n    return this._http.post(`${environment.apiUrl}/photo-gallery/delete`, params).pipe(map(data => {\n      return data;\n    }));\n  }\n  getAllPhotos() {\n    return this._http.post(`${environment.apiUrl}/photo-gallery/all`, {}).pipe(map(data => {\n      return data;\n    }));\n  }\n  static #_ = this.ɵfac = function PhotoGalleryService_Factory(t) {\n    return new (t || PhotoGalleryService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PhotoGalleryService,\n    factory: PhotoGalleryService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,SAAqBC,GAAG,QAAQ,gBAAgB;;;AAKhD,OAAM,MAAOC,mBAAmB;EAC9BC,YAAoBC,KAAiB;IAAjB,UAAK,GAALA,KAAK;EAAgB;EAEzCC,WAAW,CAACC,IAAU,EAAEC,OAAe,EAAEC,QAAgB;IACvD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;IAC7BG,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,OAAO,CAACK,QAAQ,EAAE,CAAC;IAC/CH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEH,QAAQ,CAACI,QAAQ,EAAE,CAAC;IAEjD,OAAO,IAAI,CAACR,KAAK,CACdS,IAAI,CAAM,GAAGb,WAAW,CAACc,MAAM,uBAAuB,EAAEL,QAAQ,CAAC,CACjEM,IAAI,CACHd,GAAG,CAAEe,IAAI,IAAI;MACX,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACL;EAEAC,YAAY,CAACD,IAAI;IACf,IAAIE,MAAM,GAAG,IAAInB,UAAU,EAAE;IAC7BmB,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC,KAAK,EAAEK,IAAI,CAAC;IAEnC,OAAO,IAAI,CAACZ,KAAK,CACdS,IAAI,CAAM,GAAGb,WAAW,CAACc,MAAM,uBAAuB,EAAEI,MAAM,CAAC,CAC/DH,IAAI,CACHd,GAAG,CAAEe,IAAI,IAAI;MACX,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACL;EAEAG,YAAY;IACV,OAAO,IAAI,CAACf,KAAK,CACdS,IAAI,CAAM,GAAGb,WAAW,CAACc,MAAM,oBAAoB,EAAE,EAAE,CAAC,CACxDC,IAAI,CACHd,GAAG,CAAEe,IAAI,IAAI;MACX,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACL;EAAC;qBAvCUd,mBAAmB;EAAA;EAAA;WAAnBA,mBAAmB;IAAAkB,SAAnBlB,mBAAmB;IAAAmB,YAFlB;EAAM", "names": ["HttpParams", "environment", "map", "PhotoGalleryService", "constructor", "_http", "uploadPhoto", "file", "groupId", "seasonId", "formData", "FormData", "append", "toString", "post", "apiUrl", "pipe", "data", "deletePhotos", "params", "getAllPhotos", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\services\\photo-gallery.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'environments/environment';\r\nimport { throwError } from 'rxjs';\r\nimport { catchError, map } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PhotoGalleryService {\r\n  constructor(private _http: HttpClient) { }\r\n\r\n  uploadPhoto(file: File, groupId: number, seasonId: number) {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('group_id', groupId.toString());\r\n    formData.append('season_id', seasonId.toString());\r\n\r\n    return this._http\r\n      .post<any>(`${environment.apiUrl}/photo-gallery/create`, formData)\r\n      .pipe(\r\n        map((data) => {\r\n          return data;\r\n        }),\r\n      );\r\n  }\r\n\r\n  deletePhotos(data) {\r\n    let params = new HttpParams();\r\n    params = params.append('ids', data);\r\n\r\n    return this._http\r\n      .post<any>(`${environment.apiUrl}/photo-gallery/delete`, params)\r\n      .pipe(\r\n        map((data) => {\r\n          return data;\r\n        })\r\n      );\r\n  }\r\n\r\n  getAllPhotos() {\r\n    return this._http\r\n      .post<any>(`${environment.apiUrl}/photo-gallery/all`, {})\r\n      .pipe(\r\n        map((data) => {\r\n          return data;\r\n        })\r\n      );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}