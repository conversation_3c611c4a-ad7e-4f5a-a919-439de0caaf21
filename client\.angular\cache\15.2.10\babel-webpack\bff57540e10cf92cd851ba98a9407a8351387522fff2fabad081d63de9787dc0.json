{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { AppConfig } from 'app/app-config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"app/services/tournament.service\";\nimport * as i4 from \"app/services/team.service\";\nexport class UpdateMatchDetailsComponent {\n  constructor(_translateService, _modalService, _tournamentService, _teamService) {\n    this._translateService = _translateService;\n    this._modalService = _modalService;\n    this._tournamentService = _tournamentService;\n    this._teamService = _teamService;\n    this.onUpdated = new EventEmitter();\n    this.fields = [{\n      key: 'type',\n      type: 'radio',\n      props: {\n        required: true,\n        label: this._translateService.instant('Type'),\n        options: [{\n          value: AppConfig.MATCH_DETAIL_TYPES.goal,\n          label: this._translateService.instant('Goal')\n        }, {\n          value: AppConfig.MATCH_DETAIL_TYPES.yellow_card,\n          label: this._translateService.instant('Yellow card')\n        }, {\n          value: AppConfig.MATCH_DETAIL_TYPES.red_card,\n          label: this._translateService.instant('Red card')\n        }]\n      },\n      defaultValue: AppConfig.MATCH_DETAIL_TYPES.goal\n    }, {\n      key: 'team_player_id',\n      type: 'select',\n      props: {\n        required: true,\n        label: this._translateService.instant('Select Player'),\n        placeholder: this._translateService.instant('Select Player'),\n        options: []\n      }\n    }, {\n      key: 'time',\n      type: 'input',\n      props: {\n        required: true,\n        label: this._translateService.instant('Time'),\n        type: 'number',\n        min: 0,\n        max: 255\n      },\n      defaultValue: 0\n    }, {\n      key: 'note',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Note'),\n        type: 'text',\n        placeholder: this._translateService.instant('Note')\n      }\n    }];\n    this.form = new FormGroup({});\n  }\n  ngOnInit() {\n    this.model = {\n      match_id: this.match.id\n    };\n    this.getPlayers();\n  }\n  onSubmit(model) {\n    console.log(model);\n    console.log('form', this.form);\n    if (this.form.invalid) {\n      return;\n    }\n    this._tournamentService.updateMatchDetails(model).subscribe(res => {\n      console.log(res);\n      this._modalService.dismissAll();\n      this.onUpdated.emit(res);\n      // dissmiss modal\n    });\n  }\n\n  getPlayers() {\n    this._teamService.getOptionsPlayer(this.team_id).subscribe(res => {\n      // find field has key team_player_id and set options\n      const field = this.fields.find(f => f.key === 'team_player_id');\n      field.props.options = res;\n    });\n  }\n  close() {\n    this._modalService.dismissAll();\n  }\n  static #_ = this.ɵfac = function UpdateMatchDetailsComponent_Factory(t) {\n    return new (t || UpdateMatchDetailsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.TournamentService), i0.ɵɵdirectiveInject(i4.TeamService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UpdateMatchDetailsComponent,\n    selectors: [[\"app-update-match-details\"]],\n    inputs: {\n      team_id: \"team_id\",\n      match: \"match\"\n    },\n    outputs: {\n      onUpdated: \"onUpdated\"\n    },\n    decls: 14,\n    vars: 10,\n    consts: [[1, \"modal-header\"], [\"id\", \"add_event\", 1, \"modal-title\", \"text-capitalize\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"]],\n    template: function UpdateMatchDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function UpdateMatchDetailsComponent_Template_button_click_4_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function UpdateMatchDetailsComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmit(ctx.model);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5);\n        i0.ɵɵelement(9, \"formly-form\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 6, \"Add event\"), \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.form)(\"fields\", ctx.fields)(\"model\", ctx.model);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 8, \"Add\"), \" \");\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,SAASC,SAAS,QAAQ,gBAAgB;;;;;;AAU1C,OAAM,MAAOC,2BAA2B;EA6DtCC,YACSC,iBAAmC,EACnCC,aAAuB,EACvBC,kBAAqC,EACrCC,YAAyB;IAHzB,sBAAiB,GAAjBH,iBAAiB;IACjB,kBAAa,GAAbC,aAAa;IACb,uBAAkB,GAAlBC,kBAAkB;IAClB,iBAAY,GAAZC,YAAY;IA9DX,cAAS,GAAG,IAAIR,YAAY,EAAE;IACxC,WAAM,GAAG,CACP;MACES,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,MAAM,CAAC;QAC7CC,OAAO,EAAE,CACP;UACEC,KAAK,EAAEd,SAAS,CAACe,kBAAkB,CAACC,IAAI;UACxCL,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,MAAM;SAC7C,EACD;UACEE,KAAK,EAAEd,SAAS,CAACe,kBAAkB,CAACE,WAAW;UAC/CN,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,aAAa;SACpD,EACD;UACEE,KAAK,EAAEd,SAAS,CAACe,kBAAkB,CAACG,QAAQ;UAC5CP,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,UAAU;SACjD;OAEJ;MACDO,YAAY,EAAEnB,SAAS,CAACe,kBAAkB,CAACC;KAC5C,EACD;MACET,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,QAAQ;MAEdC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,eAAe,CAAC;QACtDQ,WAAW,EAAE,IAAI,CAACjB,iBAAiB,CAACS,OAAO,CAAC,eAAe,CAAC;QAC5DC,OAAO,EAAE;;KAEZ,EACD;MACEN,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,MAAM,CAAC;QAC7CJ,IAAI,EAAE,QAAQ;QACda,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE;OACN;MACDH,YAAY,EAAE;KACf,EACD;MACEZ,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACLE,KAAK,EAAE,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAAC,MAAM,CAAC;QAC7CJ,IAAI,EAAE,MAAM;QACZY,WAAW,EAAE,IAAI,CAACjB,iBAAiB,CAACS,OAAO,CAAC,MAAM;;KAErD,CACF;IAOD,SAAI,GAAG,IAAIb,SAAS,CAAC,EAAE,CAAC;EADrB;EAGHwB,QAAQ;IACN,IAAI,CAACC,KAAK,GAAG;MACXC,QAAQ,EAAE,IAAI,CAACC,KAAK,CAACC;KACtB;IACD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,QAAQ,CAACL,KAAK;IACZM,OAAO,CAACC,GAAG,CAACP,KAAK,CAAC;IAClBM,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACC,IAAI,CAAC;IAE9B,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;MACrB;;IAEF,IAAI,CAAC5B,kBAAkB,CAAC6B,kBAAkB,CAACV,KAAK,CAAC,CAACW,SAAS,CAAEC,GAAG,IAAI;MAClEN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;MAChB,IAAI,CAAChC,aAAa,CAACiC,UAAU,EAAE;MAC/B,IAAI,CAACC,SAAS,CAACC,IAAI,CAACH,GAAG,CAAC;MACxB;IAEF,CAAC,CAAC;EACJ;;EAEAR,UAAU;IACR,IAAI,CAACtB,YAAY,CAACkC,gBAAgB,CAAC,IAAI,CAACC,OAAO,CAAC,CAACN,SAAS,CAAEC,GAAG,IAAI;MACjE;MACA,MAAMM,KAAK,GAAG,IAAI,CAACC,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACtC,GAAG,KAAK,gBAAgB,CAAC;MACjEmC,KAAK,CAACjC,KAAK,CAACI,OAAO,GAAGuB,GAAG;IAC3B,CAAC,CAAC;EACJ;EACAU,KAAK;IACH,IAAI,CAAC1C,aAAa,CAACiC,UAAU,EAAE;EACjC;EAAC;qBArGUpC,2BAA2B;EAAA;EAAA;UAA3BA,2BAA2B;IAAA8C;IAAAC;MAAAP;MAAAf;IAAA;IAAAuB;MAAAX;IAAA;IAAAY;IAAAC;IAAAC;IAAAC;MAAA;QCdxCC,8BAA0B;QAEtBA,YACF;;QAAAA,iBAAK;QACLA,iCAAyE;QAAlBA;UAAA,OAASC,WAAO;QAAA,EAAC;QACtED,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG3CA,+BAAsD;QAA7BA;UAAA,OAAYC,uBAAe;QAAA,EAAC;QACnDD,8BAAkD;QAQhDA,iCAA2E;QAC7EA,iBAAM;QACNA,+BAA0B;QAEtBA,aACF;;QAAAA,iBAAS;;;QApBTA,eACF;QADEA,kEACF;QAKIA,eAAkB;QAAlBA,oCAAkB;QASPA,eAAa;QAAbA,+BAAa;QAIxBA,eACF;QADEA,6DACF", "names": ["EventEmitter", "FormGroup", "AppConfig", "UpdateMatchDetailsComponent", "constructor", "_translateService", "_modalService", "_tournamentService", "_teamService", "key", "type", "props", "required", "label", "instant", "options", "value", "MATCH_DETAIL_TYPES", "goal", "yellow_card", "red_card", "defaultValue", "placeholder", "min", "max", "ngOnInit", "model", "match_id", "match", "id", "getPlayers", "onSubmit", "console", "log", "form", "invalid", "updateMatchDetails", "subscribe", "res", "dismissAll", "onUpdated", "emit", "getOptionsPlayer", "team_id", "field", "fields", "find", "f", "close", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\modal-update-match-details\\update-match-details.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\modal-update-match-details\\update-match-details.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'app-update-match-details',\r\n  templateUrl: './update-match-details.component.html',\r\n  styleUrls: ['./update-match-details.component.scss'],\r\n})\r\nexport class UpdateMatchDetailsComponent implements OnInit {\r\n  @Input() team_id: any;\r\n  @Input() match: any;\r\n  @Output() onUpdated = new EventEmitter();\r\n  fields = [\r\n    {\r\n      key: 'type',\r\n      type: 'radio',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Type'),\r\n        options: [\r\n          {\r\n            value: AppConfig.MATCH_DETAIL_TYPES.goal,\r\n            label: this._translateService.instant('Goal'),\r\n          },\r\n          {\r\n            value: AppConfig.MATCH_DETAIL_TYPES.yellow_card,\r\n            label: this._translateService.instant('Yellow card'),\r\n          },\r\n          {\r\n            value: AppConfig.MATCH_DETAIL_TYPES.red_card,\r\n            label: this._translateService.instant('Red card'),\r\n          },\r\n        ],\r\n      },\r\n      defaultValue: AppConfig.MATCH_DETAIL_TYPES.goal,\r\n    },\r\n    {\r\n      key: 'team_player_id',\r\n      type: 'select',\r\n\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Select Player'),\r\n        placeholder: this._translateService.instant('Select Player'),\r\n        options: [],\r\n      },\r\n    },\r\n    {\r\n      key: 'time',\r\n      type: 'input',\r\n      props: {\r\n        required: true,\r\n        label: this._translateService.instant('Time'),\r\n        type: 'number',\r\n        min: 0,\r\n        max: 255,\r\n      },\r\n      defaultValue: 0,\r\n    },\r\n    {\r\n      key: 'note',\r\n      type: 'input',\r\n      props: {\r\n        label: this._translateService.instant('Note'),\r\n        type: 'text',\r\n        placeholder: this._translateService.instant('Note'),\r\n      },\r\n    },\r\n  ];\r\n  constructor(\r\n    public _translateService: TranslateService,\r\n    public _modalService: NgbModal,\r\n    public _tournamentService: TournamentService,\r\n    public _teamService: TeamService\r\n  ) {}\r\n  form = new FormGroup({});\r\n  model: any;\r\n  ngOnInit(): void {\r\n    this.model = {\r\n      match_id: this.match.id,\r\n    };\r\n    this.getPlayers();\r\n  }\r\n\r\n  onSubmit(model) {\r\n    console.log(model);\r\n    console.log('form', this.form);\r\n\r\n    if (this.form.invalid) {\r\n      return;\r\n    }\r\n    this._tournamentService.updateMatchDetails(model).subscribe((res) => {\r\n      console.log(res);\r\n      this._modalService.dismissAll();\r\n      this.onUpdated.emit(res);\r\n      // dissmiss modal\r\n\r\n    });\r\n  }\r\n\r\n  getPlayers() {\r\n    this._teamService.getOptionsPlayer(this.team_id).subscribe((res) => {\r\n      // find field has key team_player_id and set options\r\n      const field = this.fields.find((f) => f.key === 'team_player_id');\r\n      field.props.options = res;\r\n    });\r\n  }\r\n  close() {\r\n    this._modalService.dismissAll();\r\n  }\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h4 class=\"modal-title text-capitalize\" id=\"add_event\">\r\n    {{ 'Add event' | translate }}\r\n  </h4>\r\n  <button type=\"button\" class=\"close\" aria-label=\"Close\" (click)=\"close()\">\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit(model)\">\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <!-- <div class=\"text-center\">\r\n      <img src=\"assets\\images\\backgrounds\\modal_event.png\" alt=\"\" />\r\n      <h4 class=\"modal-title text-capitalize mt-1\" id=\"add_event\">\r\n        {{ 'Add event' | translate }}\r\n      </h4>\r\n    </div> -->\r\n\r\n    <formly-form [form]=\"form\" [fields]=\"fields\" [model]=\"model\"></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n      {{ 'Add' | translate }}\r\n    </button>\r\n  </div>\r\n</form>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}