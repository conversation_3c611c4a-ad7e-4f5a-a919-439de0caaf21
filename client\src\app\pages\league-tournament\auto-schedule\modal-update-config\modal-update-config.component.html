<div class="modal-header">
  <h5 class="modal-title" id="editScheduleConfig">{{ "Edit Schedule Config" | translate }}</h5>
  <button
    type="button"
    class="close"
    (click)="closeModal()"
    aria-label="Close"
  >
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<form
  [formGroup]="editForm"
  (ngSubmit)="onSubmitEdit(editModel)"
>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <formly-form
      [form]="editForm"
      [fields]="editFields"
      [model]="editModel"
      (submit)="onSubmitEdit(editModel)"
    ></formly-form>
  </div>
  <div class="modal-footer">
    <button type="submit" class="w-100 btn btn-primary" rippleEffect
    [disabled]="editForm.invalid"
    >
      {{ 'Re-schedule Match' | translate }}
    </button>
  </div>
</form>