{"ast": null, "code": "import { AppConfig, coreConfig } from 'app/app-config';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"app/services/loading.service\";\nimport * as i4 from \"app/services/tournament.service\";\nimport * as i5 from \"app/services/commons.service\";\nimport * as i6 from \"app/services/user.service\";\nimport * as i7 from \"@angular/platform-browser\";\nfunction TeamFixturesComponent_section_2_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tab-fixtures\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"matches\", ctx_r2.matches);\n  }\n}\nfunction TeamFixturesComponent_section_2_ng_template_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h3\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"No results found\"), \" \");\n  }\n}\nfunction TeamFixturesComponent_section_2_ng_template_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 18)(2, \"div\", 31)(3, \"div\", 4)(4, \"div\", 32);\n    i0.ɵɵelement(5, \"img\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"h4\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h6\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const player_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (player_r6 == null ? null : player_r6.photo) ? player_r6 == null ? null : player_r6.photo : ctx_r5.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5._userService.fullName(player_r6.user));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"YOB: \", i0.ɵɵpipeBind2(11, 3, player_r6.dob, \"yyyy\"), \"\");\n  }\n}\nfunction TeamFixturesComponent_section_2_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, TeamFixturesComponent_section_2_ng_template_33_div_1_Template, 4, 3, \"div\", 26);\n    i0.ɵɵtemplate(2, TeamFixturesComponent_section_2_ng_template_33_div_2_Template, 12, 6, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.players == null ? null : ctx_r3.players.length) == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.players);\n  }\n}\nfunction TeamFixturesComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8);\n    i0.ɵɵelement(6, \"img\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"h2\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 13);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 14)(15, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TeamFixturesComponent_section_2_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onClickFollowTeam());\n    });\n    i0.ɵɵelement(16, \"i\", 16);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(20, \"div\", 17)(21, \"div\", 18)(22, \"ul\", 19, 20);\n    i0.ɵɵlistener(\"activeIdChange\", function TeamFixturesComponent_section_2_Template_ul_activeIdChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.activeId = $event);\n    });\n    i0.ɵɵelementStart(24, \"li\", 21)(25, \"a\", 22);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, TeamFixturesComponent_section_2_ng_template_28_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"li\", 21)(30, \"a\", 22);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, TeamFixturesComponent_section_2_ng_template_33_Template, 3, 2, \"ng-template\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(34, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r1 = i0.ɵɵreference(23);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", (ctx_r0.team == null ? null : ctx_r0.team.club == null ? null : ctx_r0.team.club.logo) ? ctx_r0.team.club.logo : ctx_r0.coreConfig.app.appLogoImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.team == null ? null : ctx_r0.team.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.team == null ? null : ctx_r0.team.group.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.team.club == null ? null : ctx_r0.team.club.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isFollowed ? \"btn-warning\" : \"btn-outline-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isFollowed ? \"feather icon-bell-off\" : \"feather icon-bell\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isFollowed ? i0.ɵɵpipeBind1(18, 13, \"Unfollow\") : i0.ɵɵpipeBind1(19, 15, \"Follow\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"activeId\", ctx_r0.activeId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngbNavItem\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 17, \"Fixtures & Results\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngbNavItem\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(32, 19, \"Players\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngbNavOutlet\", _r1);\n  }\n}\nexport class TeamFixturesComponent {\n  constructor(route, _trans, _loading, _tourService, _commonService, _userService, _titleService, _translateService) {\n    this.route = route;\n    this._trans = _trans;\n    this._loading = _loading;\n    this._tourService = _tourService;\n    this._commonService = _commonService;\n    this._userService = _userService;\n    this._titleService = _titleService;\n    this._translateService = _translateService;\n    this.AppConfig = AppConfig;\n    this.coreConfig = coreConfig;\n    this.contentHeader = null;\n    this.activeId = 1;\n    this.matches = [];\n    this.params = {\n      team_id: null\n    };\n    this.team = null;\n    this.players = [];\n    this.favoriteTeams = [];\n    this.isFollowed = false;\n    this.params.team_id = route.snapshot.paramMap.get('team_id');\n    console.log('this.team_id ', this.params.team_id);\n  }\n  ngOnInit() {\n    this.getFixtureResult(this.params);\n    this.onUrlChange();\n    this.getListFavoriteTeams();\n  }\n  getFixtureResult(params) {\n    this._loading.show();\n    this._tourService.fixturesResultsByTeam(this.params.team_id, params).subscribe(res => {\n      this.team = res.team;\n      this._titleService.setTitle(this.team?.name);\n      this.players = res.team.players;\n      this.matches = res.matches;\n    });\n  }\n  // on url change\n  onUrlChange() {\n    this.route.params.subscribe(params => {\n      this.params.team_id = params.team_id;\n      this.getFixtureResult(this.params);\n    });\n  }\n  getListFavoriteTeams() {\n    // get list of favorite teams\n    this._userService.getFavouriteTeams().subscribe(res => {\n      this.favoriteTeams = res.data;\n      console.log('this.favoriteTeams', this.favoriteTeams);\n      this.isFollowed = this.favoriteTeams.find(_v => _v.id === this.team.id) ? true : false;\n      console.log(this.isFollowed, 'isFollowed');\n    });\n  }\n  onClickFollowTeam() {\n    this._userService.toggleFavouriteTeam(this.team.id).subscribe(res => {\n      Swal.fire({\n        icon: 'success',\n        title: this._translateService.instant('Success'),\n        text: res.message\n      });\n      this.getListFavoriteTeams();\n    });\n  }\n  static #_ = this.ɵfac = function TeamFixturesComponent_Factory(t) {\n    return new (t || TeamFixturesComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.LoadingService), i0.ɵɵdirectiveInject(i4.TournamentService), i0.ɵɵdirectiveInject(i5.CommonsService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i2.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeamFixturesComponent,\n    selectors: [[\"app-team-fixtures\"]],\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [\"id\", \"team_fixtures-results\", 4, \"ngIf\"], [\"id\", \"team_fixtures-results\"], [1, \"row\"], [1, \"container-fluid\"], [1, \"card\", \"profile-header\"], [1, \"card-body\", \"team-info_wrapper\"], [1, \"club-logo\"], [\"alt\", \"Card image\", 1, \"rounded\", \"img-fluid\", 3, \"src\"], [1, \"club-info_wrapper\"], [1, \"club-info_name\"], [1, \"club-info_group\"], [1, \"club-info_club\"], [1, \"club-button\"], [\"type\", \"button\", \"rippleEffect\", \"\", 1, \"btn\", 3, \"ngClass\", \"click\"], [1, \"mr-25\", 3, \"ngClass\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\", 3, \"activeId\", \"activeIdChange\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\", 3, \"ngbNavItem\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [\"type\", \"fixtures\", 3, \"matches\"], [\"class\", \"col\", 4, \"ngIf\"], [\"class\", \"col-12 col-md-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col\"], [1, \"text-center\"], [1, \"col-12\", \"col-md-4\"], [1, \"card-body\"], [1, \"col-auto\"], [\"alt\", \"Card image\", 1, \"rounded\", \"img-fluid\", 2, \"width\", \"80px\", 3, \"src\"], [1, \"col\", \"text-dark\"]],\n    template: function TeamFixturesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, TeamFixturesComponent_section_2_Template, 35, 21, \"section\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.team);\n      }\n    },\n    styles: [\".team-info_wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 0.5rem;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  padding: 1rem;\\n}\\n@media (min-width: 1024px) {\\n  .team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%] {\\n    max-width: 100px;\\n  }\\n}\\n@media (max-width: 1023px) {\\n  .team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%] {\\n    max-width: 25%;\\n  }\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  width: 100%;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%]   .club-info_name[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin: 0;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%]   .club-info_group[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-info_wrapper[_ngcontent-%COMP%]   .club-info_club[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.team-info_wrapper[_ngcontent-%COMP%]   .club-button[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvZml4dHVyZXMtcmVzdWx0cy90ZWFtLWZpeHR1cmVzL3RlYW0tZml4dHVyZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtBQUNGO0FBQ0U7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7QUFDSjtBQUNJO0VBTEY7SUFNSSxnQkFBQTtFQUVKO0FBQ0Y7QUFESTtFQVJGO0lBU0ksY0FBQTtFQUlKO0FBQ0Y7QUFGSTtFQUNFLFdBQUE7QUFJTjtBQURFO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7QUFHSjtBQURJO0VBQ0UsaUJBQUE7RUFDQSxTQUFBO0FBR047QUFESTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBR047QUFESTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBR047QUFBRTtFQUNFLHNCQUFBO0FBRUoiLCJzb3VyY2VzQ29udGVudCI6WyIudGVhbS1pbmZvX3dyYXBwZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDFyZW07XHJcbiAgcGFkZGluZzogMC41cmVtO1xyXG5cclxuICAuY2x1Yi1sb2dvIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgcGFkZGluZzogMXJlbTtcclxuXHJcbiAgICBAbWVkaWEgKG1pbi13aWR0aDogMTAyNHB4KSB7XHJcbiAgICAgIG1heC13aWR0aDogMTAwcHg7XHJcbiAgICB9XHJcbiAgICBAbWVkaWEgKG1heC13aWR0aDogMTAyM3B4KSB7XHJcbiAgICAgIG1heC13aWR0aDogMjUlO1xyXG4gICAgfVxyXG5cclxuICAgIGltZyB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgfVxyXG4gIH1cclxuICAuY2x1Yi1pbmZvX3dyYXBwZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBnYXA6IDAuNXJlbTtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG5cclxuICAgIC5jbHViLWluZm9fbmFtZSB7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgICAuY2x1Yi1pbmZvX2dyb3VwIHtcclxuICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgICAuY2x1Yi1pbmZvX2NsdWIge1xyXG4gICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxuICB9XHJcbiAgLmNsdWItYnV0dG9uIHtcclxuICAgIG1pbi13aWR0aDogbWF4LWNvbnRlbnQ7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAKtD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;IC0DZC,mCACe;;;;IADeA,wCAAmB;;;;;IAU/CA,+BAA8C;IAE1CA,YACF;;IAAAA,iBAAK;;;IADHA,eACF;IADEA,yEACF;;;;;IAEFA,+BAA4D;IAKlDA,0BASE;IACJA,iBAAM;IAENA,+BAA2B;IACrBA,YAAwC;IAAAA,iBAAK;IACjDA,0BAAI;IAAAA,aAAqC;;IAAAA,iBAAK;;;;;IAb5CA,eAIC;IAJDA,sKAIC;IAQCA,eAAwC;IAAxCA,kEAAwC;IACxCA,eAAqC;IAArCA,gFAAqC;;;;;IAzBrDA,8BAAiB;IACfA,gGAIM;IACNA,iGAwBM;IACRA,iBAAM;;;;IA9BcA,eAA0B;IAA1BA,mFAA0B;IAKIA,eAAU;IAAVA,wCAAU;;;;;;IAhF1EA,kCAAiD;IAMrCA,yBAQE;IACJA,iBAAM;IAENA,+BAA+B;IAE3BA,YACF;IAAAA,iBAAK;IACLA,8BAA2B;IACzBA,aACF;IAAAA,iBAAI;IACJA,8BAA0B;IACxBA,aACF;IAAAA,iBAAI;IAENA,gCAAyB;IAMrBA;MAAAA;MAAA;MAAA,OAASA,yCAAmB;IAAA,EAAC;IAE7BA,yBAKK;IACLA,aAKF;;;IAAAA,iBAAS;IAKjBA,gCAAoB;IAIdA;MAAAA;MAAA;MAAA;IAAA,EAAuB;IAIvBA,+BAAgC;IAE5BA,aACF;;IAAAA,iBAAI;IACJA,mGAGc;IAChBA,iBAAK;IACLA,+BAAgC;IAE5BA,aACF;;IAAAA,iBAAI;IACJA,mGAiCc;IAChBA,iBAAK;IAGTA,2BAA6C;IAC/CA,iBAAM;;;;;IAxGIA,eAIC;IAJDA,2LAIC;IAQDA,eACF;IADEA,8EACF;IAEEA,eACF;IADEA,oFACF;IAEEA,eACF;IADEA,wFACF;IAMEA,eAA8D;IAA9DA,mFAA8D;IAM5DA,eAEC;IAFDA,2FAEC;IAEHA,eAKF;IALEA,0HAKF;IASFA,eAAuB;IAAvBA,0CAAuB;IAIRA,eAAgB;IAAhBA,8BAAgB;IAE3BA,eACF;IADEA,6EACF;IAMaA,eAAgB;IAAhBA,8BAAgB;IAE3BA,eACF;IADEA,kEACF;IAsCDA,eAAoB;IAApBA,kCAAoB;;;ADhGnC,OAAM,MAAOC,qBAAqB;EAehCC,YACSC,KAAqB,EACrBC,MAAwB,EACxBC,QAAwB,EACxBC,YAA+B,EAC/BC,cAA8B,EAC9BC,YAAyB,EACzBC,aAAoB,EACpBC,iBAAmC;IAPnC,UAAK,GAALP,KAAK;IACL,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IACR,iBAAY,GAAZC,YAAY;IACZ,mBAAc,GAAdC,cAAc;IACd,iBAAY,GAAZC,YAAY;IACZ,kBAAa,GAAbC,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IAtB1B,cAAS,GAAGb,SAAS;IACrB,eAAU,GAAGC,UAAU;IACvB,kBAAa,GAAG,IAAI;IACpB,aAAQ,GAAG,CAAC;IACZ,YAAO,GAAG,EAAE;IACZ,WAAM,GAAG;MACPa,OAAO,EAAE;KACV;IACD,SAAI,GAAG,IAAI;IACX,YAAO,GAAG,EAAE;IAEZ,kBAAa,GAAG,EAAE;IAClB,eAAU,GAAG,KAAK;IAYhB,IAAI,CAACC,MAAM,CAACD,OAAO,GAAGR,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC;IAC5DC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACL,MAAM,CAACD,OAAO,CAAC;EACnD;EAEAO,QAAQ;IACN,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACP,MAAM,CAAC;IAClC,IAAI,CAACQ,WAAW,EAAE;IAClB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAF,gBAAgB,CAACP,MAAM;IACrB,IAAI,CAACP,QAAQ,CAACiB,IAAI,EAAE;IACpB,IAAI,CAAChB,YAAY,CACdiB,qBAAqB,CAAC,IAAI,CAACX,MAAM,CAACD,OAAO,EAAEC,MAAM,CAAC,CAClDY,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI;MACpB,IAAI,CAACjB,aAAa,CAACkB,QAAQ,CAAC,IAAI,CAACD,IAAI,EAAEE,IAAI,CAAC;MAC5C,IAAI,CAACC,OAAO,GAAGJ,GAAG,CAACC,IAAI,CAACG,OAAO;MAC/B,IAAI,CAACC,OAAO,GAAGL,GAAG,CAACK,OAAO;IAC5B,CAAC,CAAC;EACN;EAEA;EACAV,WAAW;IACT,IAAI,CAACjB,KAAK,CAACS,MAAM,CAACY,SAAS,CAAEZ,MAAM,IAAI;MACrC,IAAI,CAACA,MAAM,CAACD,OAAO,GAAGC,MAAM,CAACD,OAAO;MACpC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,CAACP,MAAM,CAAC;IACpC,CAAC,CAAC;EACJ;EAEAS,oBAAoB;IAClB;IACA,IAAI,CAACb,YAAY,CAACuB,iBAAiB,EAAE,CAACP,SAAS,CAAEC,GAAG,IAAI;MACtD,IAAI,CAACO,aAAa,GAAGP,GAAG,CAACQ,IAAI;MAE7BjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACe,aAAa,CAAC;MAErD,IAAI,CAACE,UAAU,GAAG,IAAI,CAACF,aAAa,CAACG,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,EAAE,KAAK,IAAI,CAACX,IAAI,CAACW,EAAE,CAAC,GACrE,IAAI,GACJ,KAAK;MAETrB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACiB,UAAU,EAAE,YAAY,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEAI,iBAAiB;IACf,IAAI,CAAC9B,YAAY,CAAC+B,mBAAmB,CAAC,IAAI,CAACb,IAAI,CAACW,EAAE,CAAC,CAACb,SAAS,CAAEC,GAAG,IAAI;MACpE1B,IAAI,CAACyC,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI,CAAChC,iBAAiB,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChDC,IAAI,EAAEnB,GAAG,CAACoB;OACX,CAAC;MACF,IAAI,CAACxB,oBAAoB,EAAE;IAC7B,CAAC,CAAC;EACJ;EAAC;qBA/EUpB,qBAAqB;EAAA;EAAA;UAArBA,qBAAqB;IAAA6C;IAAAC;IAAAC;IAAAC;IAAAC;MAAA;QChBlClD,8BAA+C;QAE3CA,gFAiHU;QACZA,iBAAM;;;QAlHiCA,eAAU;QAAVA,+BAAU", "names": ["AppConfig", "coreConfig", "<PERSON><PERSON>", "i0", "TeamFixturesComponent", "constructor", "route", "_trans", "_loading", "_tourService", "_commonService", "_userService", "_titleService", "_translateService", "team_id", "params", "snapshot", "paramMap", "get", "console", "log", "ngOnInit", "getFixtureResult", "onUrlChange", "getListFavoriteTeams", "show", "fixturesResultsByTeam", "subscribe", "res", "team", "setTitle", "name", "players", "matches", "getFavouriteTeams", "favoriteTeams", "data", "isFollowed", "find", "_v", "id", "onClickFollowTeam", "toggleFavouriteTeam", "fire", "icon", "title", "instant", "text", "message", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\team-fixtures\\team-fixtures.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\fixtures-results\\team-fixtures\\team-fixtures.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { AppConfig, coreConfig } from 'app/app-config';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { UserService } from 'app/services/user.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-team-fixtures',\r\n  templateUrl: './team-fixtures.component.html',\r\n  styleUrls: ['./team-fixtures.component.scss'],\r\n})\r\nexport class TeamFixturesComponent implements OnInit {\r\n  AppConfig = AppConfig;\r\n  coreConfig = coreConfig;\r\n  contentHeader = null;\r\n  activeId = 1;\r\n  matches = [];\r\n  params = {\r\n    team_id: null,\r\n  };\r\n  team = null;\r\n  players = [];\r\n\r\n  favoriteTeams = [];\r\n  isFollowed = false;\r\n\r\n  constructor(\r\n    public route: ActivatedRoute,\r\n    public _trans: TranslateService,\r\n    public _loading: LoadingService,\r\n    public _tourService: TournamentService,\r\n    public _commonService: CommonsService,\r\n    public _userService: UserService,\r\n    public _titleService: Title,\r\n    public _translateService: TranslateService\r\n  ) {\r\n    this.params.team_id = route.snapshot.paramMap.get('team_id');\r\n    console.log('this.team_id ', this.params.team_id);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getFixtureResult(this.params);\r\n    this.onUrlChange();\r\n    this.getListFavoriteTeams();\r\n  }\r\n\r\n  getFixtureResult(params) {\r\n    this._loading.show();\r\n    this._tourService\r\n      .fixturesResultsByTeam(this.params.team_id, params)\r\n      .subscribe((res) => {\r\n        this.team = res.team;\r\n        this._titleService.setTitle(this.team?.name);\r\n        this.players = res.team.players;\r\n        this.matches = res.matches;\r\n      });\r\n  }\r\n\r\n  // on url change\r\n  onUrlChange() {\r\n    this.route.params.subscribe((params) => {\r\n      this.params.team_id = params.team_id;\r\n      this.getFixtureResult(this.params);\r\n    });\r\n  }\r\n\r\n  getListFavoriteTeams() {\r\n    // get list of favorite teams\r\n    this._userService.getFavouriteTeams().subscribe((res) => {\r\n      this.favoriteTeams = res.data;\r\n\r\n      console.log('this.favoriteTeams', this.favoriteTeams);\r\n\r\n      this.isFollowed = this.favoriteTeams.find((_v) => _v.id === this.team.id)\r\n        ? true\r\n        : false;\r\n\r\n      console.log(this.isFollowed, 'isFollowed');\r\n    });\r\n  }\r\n\r\n  onClickFollowTeam() {\r\n    this._userService.toggleFavouriteTeam(this.team.id).subscribe((res) => {\r\n      Swal.fire({\r\n        icon: 'success',\r\n        title: this._translateService.instant('Success'),\r\n        text: res.message,\r\n      });\r\n      this.getListFavoriteTeams();\r\n    });\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <section id=\"team_fixtures-results\" *ngIf=\"team\">\r\n      <div class=\"row\">\r\n        <div class=\"container-fluid\">\r\n          <div class=\"card profile-header\">\r\n            <div class=\"card-body team-info_wrapper\">\r\n              <div class=\"club-logo\">\r\n                <img\r\n                  [src]=\"\r\n                    team?.club?.logo\r\n                      ? team.club.logo\r\n                      : coreConfig.app.appLogoImage\r\n                  \"\r\n                  class=\"rounded img-fluid\"\r\n                  alt=\"Card image\"\r\n                />\r\n              </div>\r\n              <!-- profile title -->\r\n              <div class=\"club-info_wrapper\">\r\n                <h2 class=\"club-info_name\">\r\n                  {{ team?.name }}\r\n                </h2>\r\n                <p class=\"club-info_group\">\r\n                  {{ team?.group.name }}\r\n                </p>\r\n                <p class=\"club-info_club\">\r\n                  {{ team.club?.name }}\r\n                </p>\r\n              </div>\r\n              <div class=\"club-button\">\r\n                <button\r\n                  type=\"button\"\r\n                  class=\"btn\"\r\n                  [ngClass]=\"isFollowed ? 'btn-warning' : 'btn-outline-warning'\"\r\n                  rippleEffect\r\n                  (click)=\"onClickFollowTeam()\"\r\n                >\r\n                  <i\r\n                    class=\"mr-25\"\r\n                    [ngClass]=\"\r\n                      isFollowed ? 'feather icon-bell-off' : 'feather icon-bell'\r\n                    \"\r\n                  ></i>\r\n                  {{\r\n                    isFollowed\r\n                      ? ('Unfollow' | translate)\r\n                      : ('Follow' | translate)\r\n                  }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <ul\r\n              ngbNav\r\n              [(activeId)]=\"activeId\"\r\n              #nav=\"ngbNav\"\r\n              class=\"nav-tabs m-0\"\r\n            >\r\n              <li ngbNavItem [ngbNavItem]=\"1\">\r\n                <a ngbNavLink>\r\n                  {{ 'Fixtures & Results' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <tab-fixtures type=\"fixtures\" [matches]=\"matches\">\r\n                  </tab-fixtures>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem [ngbNavItem]=\"2\">\r\n                <a ngbNavLink>\r\n                  {{ 'Players' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <div class=\"row\">\r\n                    <div class=\"col\" *ngIf=\"players?.length == 0\">\r\n                      <h3 class=\"text-center\">\r\n                        {{ 'No results found' | translate }}\r\n                      </h3>\r\n                    </div>\r\n                    <div class=\"col-12 col-md-4\" *ngFor=\"let player of players\">\r\n                      <div class=\"card\">\r\n                        <div class=\"card-body\">\r\n                          <div class=\"row\">\r\n                            <div class=\"col-auto\">\r\n                              <img\r\n                                [src]=\"\r\n                                  player?.photo\r\n                                    ? player?.photo\r\n                                    : coreConfig.app.appLogoImage\r\n                                \"\r\n                                style=\"width: 80px\"\r\n                                class=\"rounded img-fluid\"\r\n                                alt=\"Card image\"\r\n                              />\r\n                            </div>\r\n                            <!-- profile title -->\r\n                            <div class=\"col text-dark\">\r\n                              <h4>{{ _userService.fullName(player.user) }}</h4>\r\n                              <h6>YOB: {{ player.dob | date : 'yyyy' }}</h6>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}