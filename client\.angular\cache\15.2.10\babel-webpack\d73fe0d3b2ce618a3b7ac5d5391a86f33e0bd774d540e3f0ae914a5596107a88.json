{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"../../../../services/season.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"../../../../services/loading.service\";\nimport * as i5 from \"../../../../services/auto-schedule.service\";\nimport * as i6 from \"ngx-toastr\";\nexport class ModalUpdateMatchComponent {\n  constructor(_translateService, _seasonService, _modalService, _loadingService, _autoScheduleService, _toastService) {\n    this._translateService = _translateService;\n    this._seasonService = _seasonService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._autoScheduleService = _autoScheduleService;\n    this._toastService = _toastService;\n    this.onSubmit = new EventEmitter();\n    this.updateMatchForm = new FormGroup({});\n    this.updateMatchModel = {};\n    this.updateMatchFields = [{\n      key: 'time_slot_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'list_referees',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: this._translateService.instant('Current match referees'),\n        placeholder: this._translateService.instant('Select match referees'),\n        options: []\n      },\n      hooks: {}\n    }];\n  }\n  ngOnInit() {\n    this.getSeasonReferees();\n    this.updateMatchFields[0].defaultValue = this.timeSlotInfo.time_slot_id;\n  }\n  onSubmitUpdateMatch(model) {\n    console.log(model);\n    this._autoScheduleService.updateMatchReferee(model).subscribe(res => {\n      this._toastService.success(this._translateService.instant('Match referee updated successfully'));\n      this.onSubmit.emit(res);\n      this._modalService.dismissAll();\n    }, error => {\n      console.log(\"🚀 ~ onSubmitUpdateMatch ~ error: \", error);\n    });\n  }\n  closeModal() {\n    this.updateMatchModel = {};\n    this._modalService.dismissAll();\n  }\n  getSeasonReferees() {\n    this._loadingService.show();\n    this._seasonService.getListSeasonReferees(this.seasonId).subscribe(response => {\n      // set options for field list_referees\n      this.updateMatchFields[1].props.options = response['data'].map(referee => {\n        return {\n          label: referee.user ? `${referee.user.first_name} ${referee.user.last_name}` : referee.referee_name,\n          value: referee.id\n        };\n      });\n      // set value for field list_referees\n      this.updateMatchModel = {\n        ...this.updateMatchModel,\n        list_referees: this.timeSlotInfo.referee_ids\n      };\n    }, () => {}, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  static #_ = this.ɵfac = function ModalUpdateMatchComponent_Factory(t) {\n    return new (t || ModalUpdateMatchComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.SeasonService), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.AutoScheduleService), i0.ɵɵdirectiveInject(i6.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalUpdateMatchComponent,\n    selectors: [[\"app-modal-update-match\"]],\n    inputs: {\n      seasonId: \"seasonId\",\n      timeSlotInfo: \"timeSlotInfo\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"updateMatch\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalUpdateMatchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalUpdateMatchComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalUpdateMatchComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitUpdateMatch(ctx.updateMatchModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalUpdateMatchComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitUpdateMatch(ctx.updateMatchModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Update Match Referee\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.updateMatchForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.updateMatchForm)(\"fields\", ctx.updateMatchFields)(\"model\", ctx.updateMatchModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.updateMatchForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Update\"), \" \");\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AAEtE,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;AAe1C,OAAM,MAAOC,yBAAyB;EAMlCC,YACYC,iBAAmC,EACnCC,cAA6B,EAC7BC,aAAuB,EACvBC,eAA+B,EAC/BC,oBAAyC,EACzCC,aAA4B;IAL5B,sBAAiB,GAAjBL,iBAAiB;IACjB,mBAAc,GAAdC,cAAc;IACd,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,yBAAoB,GAApBC,oBAAoB;IACpB,kBAAa,GAAbC,aAAa;IAVf,aAAQ,GAAG,IAAIT,YAAY,EAAE;IAcvC,oBAAe,GAAG,IAAIC,SAAS,CAAC,EAAE,CAAC;IACnC,qBAAgB,GAAG,EAAE;IACrB,sBAAiB,GAAwB,CACrC;MACIS,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHC,QAAQ,EAAE,IAAI;QACdC,cAAc,EAAE,IAAI;QACpBC,YAAY,EAAE,EAAE;QAChBC,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACa,OAAO,CAAC,wBAAwB,CAAC;QAC/DC,WAAW,EAAE,IAAI,CAACd,iBAAiB,CAACa,OAAO,CAAC,uBAAuB,CAAC;QACpEE,OAAO,EAAE;OACZ;MACDC,KAAK,EAAE;KACV,CACJ;EAzBD;EA2BAC,QAAQ;IACJ,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC,CAACR,YAAY,GAAG,IAAI,CAACS,YAAY,CAACC,YAAY;EAC3E;EAEAC,mBAAmB,CAACC,KAAK;IACrBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,IAAI,CAACnB,oBAAoB,CAACsB,kBAAkB,CAACH,KAAK,CAAC,CAACI,SAAS,CAAEC,GAAG,IAAI;MAClE,IAAI,CAACvB,aAAa,CAACwB,OAAO,CAAC,IAAI,CAAC7B,iBAAiB,CAACa,OAAO,CAAC,oCAAoC,CAAC,CAAC;MAChG,IAAI,CAACiB,QAAQ,CAACC,IAAI,CAACH,GAAG,CAAC;MACvB,IAAI,CAAC1B,aAAa,CAAC8B,UAAU,EAAE;IACnC,CAAC,EAAGC,KAAK,IAAI;MACTT,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEQ,KAAK,CAAC;IAC5D,CAAC,CAAC;EACN;EAEAC,UAAU;IACN,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACjC,aAAa,CAAC8B,UAAU,EAAE;EACnC;EAEAd,iBAAiB;IACb,IAAI,CAACf,eAAe,CAACiC,IAAI,EAAE;IAC3B,IAAI,CAACnC,cAAc,CAACoC,qBAAqB,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACX,SAAS,CAAEY,QAAQ,IAAI;MAC5E;MACA,IAAI,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAACX,KAAK,CAACO,OAAO,GAAGwB,QAAQ,CAAC,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,IAAI;QACvE,OAAO;UACH7B,KAAK,EAAE6B,OAAO,CAACC,IAAI,GACb,GAAGD,OAAO,CAACC,IAAI,CAACC,UAAU,IAAIF,OAAO,CAACC,IAAI,CAACE,SAAS,EAAE,GACtDH,OAAO,CAACI,YAAY;UAC1BC,KAAK,EAAEL,OAAO,CAACM;SAClB;MACL,CAAC,CAAC;MAEF;MACA,IAAI,CAACZ,gBAAgB,GAAG;QACpB,GAAG,IAAI,CAACA,gBAAgB;QACxBa,aAAa,EAAE,IAAI,CAAC5B,YAAY,CAAC6B;OACpC;IACL,CAAC,EAAE,MAAK,CAAG,CAAC,EAAE,MAAK;MACf,IAAI,CAAC9C,eAAe,CAAC+C,OAAO,EAAE;IAClC,CAAC,CAAC;EACN;EAAC;qBAnFQpD,yBAAyB;EAAA;EAAA;UAAzBA,yBAAyB;IAAAqD;IAAAC;MAAAd;MAAAlB;IAAA;IAAAiC;MAAAvB;IAAA;IAAAwB;IAAAC;IAAAC;IAAAC;MAAA;QCjBtCC,8BAA0B;QACiBA,YAAwC;;QAAAA,iBAAK;QACtFA,iCAKC;QAFCA;UAAA,OAASC,gBAAY;QAAA,EAAC;QAGtBD,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG3CA,+BAGC;QADCA;UAAA,OAAYC,6CAAqC;QAAA,EAAC;QAElDD,8BAAkD;QAK9CA;UAAA,OAAUC,6CAAqC;QAAA,EAAC;QACjDD,iBAAc;QAEjBA,+BAA0B;QAItBA,aACF;;QAAAA,iBAAS;;;QA3B8BA,eAAwC;QAAxCA,kEAAwC;QAWjFA,eAA6B;QAA7BA,+CAA6B;QAKzBA,eAAwB;QAAxBA,0CAAwB;QAQxBA,eAAoC;QAApCA,sDAAoC;QAEpCA,eACF;QADEA,gEACF", "names": ["EventEmitter", "FormGroup", "ModalUpdateMatchComponent", "constructor", "_translateService", "_seasonService", "_modalService", "_loadingService", "_autoScheduleService", "_toastService", "key", "type", "props", "multiple", "hideOnMultiple", "defaultValue", "label", "instant", "placeholder", "options", "hooks", "ngOnInit", "getSeasonReferees", "updateMatchFields", "timeSlotInfo", "time_slot_id", "onSubmitUpdateMatch", "model", "console", "log", "updateMatchReferee", "subscribe", "res", "success", "onSubmit", "emit", "dismissAll", "error", "closeModal", "updateMatchModel", "show", "getListSeasonReferees", "seasonId", "response", "map", "referee", "user", "first_name", "last_name", "referee_name", "value", "id", "list_referees", "referee_ids", "dismiss", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-update-match\\modal-update-match.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-update-match\\modal-update-match.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { FormlyFieldConfig } from \"@ngx-formly/core\";\r\nimport { FormGroup } from \"@angular/forms\";\r\nimport { TranslateService } from \"@ngx-translate/core\";\r\nimport { SeasonService } from \"../../../../services/season.service\";\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { NgbModal } from \"@ng-bootstrap/ng-bootstrap\";\r\nimport { LoadingService } from \"../../../../services/loading.service\";\r\nimport { AutoScheduleService } from \"../../../../services/auto-schedule.service\";\r\nimport Swal from \"sweetalert2\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\n\r\n@Component({\r\n    selector: 'app-modal-update-match',\r\n    templateUrl: './modal-update-match.component.html',\r\n    styleUrls: ['./modal-update-match.component.scss']\r\n})\r\nexport class ModalUpdateMatchComponent {\r\n\r\n    @Output() onSubmit = new EventEmitter();\r\n    @Input() seasonId: any;\r\n    @Input() timeSlotInfo: any;\r\n\r\n    constructor(\r\n        private _translateService: TranslateService,\r\n        private _seasonService: SeasonService,\r\n        private _modalService: NgbModal,\r\n        private _loadingService: LoadingService,\r\n        private _autoScheduleService: AutoScheduleService,\r\n        private _toastService: ToastrService\r\n    ) {\r\n    }\r\n\r\n    updateMatchForm = new FormGroup({});\r\n    updateMatchModel = {};\r\n    updateMatchFields: FormlyFieldConfig[] = [\r\n        {\r\n            key: 'time_slot_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            },\r\n        },\r\n        {\r\n            key: 'list_referees',\r\n            type: 'ng-select',\r\n            props: {\r\n                multiple: true,\r\n                hideOnMultiple: true,\r\n                defaultValue: [],\r\n                label: this._translateService.instant('Current match referees'),\r\n                placeholder: this._translateService.instant('Select match referees'),\r\n                options: []\r\n            },\r\n            hooks: {}\r\n        }\r\n    ];\r\n\r\n    ngOnInit() {\r\n        this.getSeasonReferees();\r\n        this.updateMatchFields[0].defaultValue = this.timeSlotInfo.time_slot_id;\r\n    }\r\n\r\n    onSubmitUpdateMatch(model) {\r\n        console.log(model);\r\n        this._autoScheduleService.updateMatchReferee(model).subscribe((res) => {\r\n            this._toastService.success(this._translateService.instant('Match referee updated successfully'));\r\n            this.onSubmit.emit(res);\r\n            this._modalService.dismissAll();\r\n        }, (error) => {\r\n            console.log(\"🚀 ~ onSubmitUpdateMatch ~ error: \", error);\r\n        });\r\n    }\r\n\r\n    closeModal() {\r\n        this.updateMatchModel = {};\r\n        this._modalService.dismissAll();\r\n    }\r\n\r\n    getSeasonReferees() {\r\n        this._loadingService.show();\r\n        this._seasonService.getListSeasonReferees(this.seasonId).subscribe((response) => {\r\n            // set options for field list_referees\r\n            this.updateMatchFields[1].props.options = response['data'].map((referee) => {\r\n                return {\r\n                    label: referee.user\r\n                        ? `${referee.user.first_name} ${referee.user.last_name}`\r\n                        : referee.referee_name,\r\n                    value: referee.id\r\n                };\r\n            })\r\n\r\n            // set value for field list_referees\r\n            this.updateMatchModel = {\r\n                ...this.updateMatchModel,\r\n                list_referees: this.timeSlotInfo.referee_ids\r\n            }\r\n        }, () => { }, () => {\r\n            this._loadingService.dismiss();\r\n        });\r\n    }\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"updateMatch\">{{ \"Update Match Referee\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"updateMatchForm\"\r\n  (ngSubmit)=\"onSubmitUpdateMatch(updateMatchModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <formly-form\r\n      [form]=\"updateMatchForm\"\r\n      [fields]=\"updateMatchFields\"\r\n      [model]=\"updateMatchModel\"\r\n      (submit)=\"onSubmitUpdateMatch(updateMatchModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n      <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n      [disabled]=\"updateMatchForm.invalid\"\r\n      >\r\n      {{ 'Update' | translate }}\r\n    </button>\r\n  </div>\r\n</form>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}