{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"app/services/team.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"app/services/loading.service\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"app/services/registration.service\";\nimport * as i10 from \"app/services/club.service\";\nimport * as i11 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i12 from \"@angular/platform-browser\";\nimport * as i13 from \"app/services/export.service\";\nconst _c0 = [\"rowActionBtn\"];\nfunction LeagueReportsComponent_ng_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 2, season_r4.name), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r5.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, club_r5.code), \" \");\n  }\n}\nfunction LeagueReportsComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-btn-dropdown-action\", 19);\n    i0.ɵɵlistener(\"emitter\", function LeagueReportsComponent_ng_template_25_Template_app_btn_dropdown_action_emitter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const emitter_r7 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r7($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.adtData;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"actions\", ctx_r3.rowActions)(\"data\", data_r6);\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(route, _router, _commonsService, _http, _trans, renderer, _teamService, _modalService, _loadingService, _toastService, _registrationService, _clubService, _translateService, _coreSidebarService, _titleService, _exportService) {\n    this.route = route;\n    this._router = _router;\n    this._commonsService = _commonsService;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._teamService = _teamService;\n    this._modalService = _modalService;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._titleService = _titleService;\n    this._exportService = _exportService;\n    this.dtElement = DataTableDirective;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.currentTeam = {};\n    this.table_name = 'team-table';\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Create New Tournament'),\n        edit: 'Edit team',\n        remove: 'Delete team'\n      },\n      url: `${environment.apiUrl}/teams/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }, {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }];\n    this.rowActions = [{\n      type: 'collection',\n      buttons: [{\n        label: 'Update score',\n        onClick: row => {\n          this.editor('edit', row);\n        },\n        icon: 'fa-regular fa-pen-to-square'\n      }]\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n      this.dtTrigger.next(this.dtOptions);\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  _getClubs() {\n    this._loadingService.show();\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n      // this.clubId = this.clubs[0];\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      if (this.dtElement.dtInstance) {\n        this.dtElement.dtInstance.then(dtInstance => {\n          dtInstance.ajax.reload();\n        });\n      }\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.dtElement.dtInstance.then(dtInstance => {\n      dtInstance.ajax.reload();\n    });\n  }\n  ngOnInit() {\n    var _this = this;\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      // serverSide: true,\n      rowId: 'id',\n      ajax: (dataTablesParameters, callback) => {\n        let params = this.clubId != undefined ? `?club_id=${this.clubId}` : '';\n        this._http.post(`${environment.apiUrl}/seasons/${this.seasonId}/matches${params}`, dataTablesParameters).subscribe(resp => {\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      language: this._commonsService.dataTableDefaults.lang,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('League/Knockout'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          return `<div class=\"text-center\" style=\"width:max-content;\">${data}</div>`;\n          // return `<div class=\"text-center\" style=\"min-width:max-content;\">${data}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        reder: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          // format to HH:mm from ISO 8601\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('User'),\n        data: 'user'\n      }, {\n        title: this._translateService.instant('Home'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa fa-file-excel-o mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              yield _this._exportService.exportCsv(data, 'League.csv');\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  setClubs(data) {\n    this.clubs = data;\n    // get field has key club_id\n    const clubField = this.fields.find(field => field.key === 'club_id');\n    // set options for club field\n    let current_clubs = [];\n    data.forEach(club => {\n      let club_name = this._translateService.instant(club.name);\n      current_clubs.push({\n        label: club_name,\n        value: club.id\n      });\n    });\n    clubField.props.options = current_clubs;\n  }\n  onCaptureEvent(event) {\n    // console.log(event);\n  }\n  editor(action, row) {\n    this.fields[0].defaultValue = this.clubId;\n    switch (action) {\n      case 'create':\n        this.fields[2].props.disabled = false;\n        break;\n      case 'edit':\n        this.fields[2].props.disabled = true;\n        break;\n      case 'remove':\n        break;\n      default:\n        break;\n    }\n    this.params.action = action;\n    this.params.row = row ? row : null;\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\n  }\n  ngAfterViewInit() {\n    this.unlistener = this.renderer.listen('document', 'click', event => {\n      if (event.target.hasAttribute('tournament_id')) {\n        let tournament_id = event.target.getAttribute('tournament_id');\n        let stage_id = event.target.getAttribute('stage_id');\n        //  navigate to path ':tournament_id/stages/:stage_id'\n        this._router.navigate([tournament_id, 'stages', stage_id], {\n          relativeTo: this.route\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    this.unlistener();\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i5.TeamService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i7.LoadingService), i0.ɵɵdirectiveInject(i8.ToastrService), i0.ɵɵdirectiveInject(i9.RegistrationService), i0.ɵɵdirectiveInject(i10.ClubService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i11.CoreSidebarService), i0.ɵɵdirectiveInject(i12.Title), i0.ɵɵdirectiveInject(i13.ExportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    viewQuery: function LeagueReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowActionBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 27,\n    vars: 27,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"row\", \"mb-1\"], [1, \"col-6\"], [\"for\", \"season\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"club\"], [3, \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"pt-2\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"], [\"rowActionBtn\", \"\"], [3, \"value\"], [\"btnStyle\", \"font-size:15px;color:black!important\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"ng-select\", 6);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_8_listener($event) {\n          return ctx.seasonId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_8_listener($event) {\n          return ctx.onSelectSeason($event);\n        });\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵtemplate(10, LeagueReportsComponent_ng_option_10_Template, 3, 4, \"ng-option\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"label\", 8);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"ng-select\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueReportsComponent_Template_ng_select_ngModelChange_15_listener($event) {\n          return ctx.clubId = $event;\n        })(\"change\", function LeagueReportsComponent_Template_ng_select_change_15_listener($event) {\n          return ctx.onSelectClub($event);\n        });\n        i0.ɵɵpipe(16, \"translate\");\n        i0.ɵɵtemplate(17, LeagueReportsComponent_ng_option_17_Template, 3, 4, \"ng-option\", 7);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"div\", 13);\n        i0.ɵɵelement(22, \"table\", 14);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(23, \"core-sidebar\", 15);\n        i0.ɵɵelement(24, \"app-editor-sidebar\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(25, LeagueReportsComponent_ng_template_25_Template, 1, 2, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 19, \"Season\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(9, 21, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.seasonId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 23, \"Club\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(16, 25, \"Select Club\"));\n        i0.ɵɵproperty(\"clearable\", false)(\"clearable\", true)(\"ngModel\", ctx.clubId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clubs);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLm1pbi13LW1heCB7XHJcbiAgbWluLXdpZHRoOiBtYXgtY29udGVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAaA,SAASA,kBAAkB,QAAQ,oBAAoB;AAMvD,SAASC,WAAW,QAAQ,0BAA0B;AAGtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,IAAI,MAAM,aAAa;AAO9B,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;ICnBtBC,qCAA8D;IAAAA,YAC9D;;IAAAA,iBAAY;;;;IAD8BA,oCAAmB;IAACA,eAC9D;IAD8DA,oEAC9D;;;;;IAOAA,qCAAwD;IACvDA,YACD;;IAAAA,iBAAY;;;;IAF0BA,kCAAiB;IACtDA,eACD;IADCA,mEACD;;;;;;IAyBJA,mDACiD;IADaA;MAAA;MAAA;MAAA,OAAWA,iCAAe;IAAA,EAAC;IACxCA,iBAA0B;;;;;IADlDA,2CAAsB;;;ADRhD,OAAM,MAAOC,sBAAsB;EAkEjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,eAA+B,EAC/BC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,YAAyB,EACzBC,aAAuB,EACvBC,eAA+B,EAC/BC,aAA4B,EAC5BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,mBAAuC,EACvCC,aAAoB,EACnBC,cAA6B;IAf7B,UAAK,GAALf,KAAK;IACN,YAAO,GAAPC,OAAO;IACP,oBAAe,GAAfC,eAAe;IACf,UAAK,GAALC,KAAK;IACL,WAAM,GAANC,MAAM;IACN,aAAQ,GAARC,QAAQ;IACR,iBAAY,GAAZC,YAAY;IACZ,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IACb,yBAAoB,GAApBC,oBAAoB;IACpB,iBAAY,GAAZC,YAAY;IACZ,sBAAiB,GAAjBC,iBAAiB;IACjB,wBAAmB,GAAnBC,mBAAmB;IACnB,kBAAa,GAAbC,aAAa;IACZ,mBAAc,GAAdC,cAAc;IA/ExB,cAAS,GAAQvB,kBAAkB;IACnC,cAAS,GAAyB,IAAIE,OAAO,EAAe;IAC5D,cAAS,GAAQ,EAAE;IAEZ,gBAAW,GAAQ,EAAE;IAOrB,eAAU,GAAG,YAAY;IACzB,WAAM,GAAwB;MACnCsB,SAAS,EAAE,IAAI,CAACC,UAAU;MAC1BC,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAACP,iBAAiB,CAACQ,OAAO,CAAC,uBAAuB,CAAC;QAC/DC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAG9B,WAAW,CAAC+B,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,WAAM,GAAU,CACrB;MACEC,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAClB,iBAAiB,CAACQ,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,EACD;MACEL,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLC,KAAK,EAAE,IAAI,CAAClB,iBAAiB,CAACQ,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,CACF;IAEM,eAAU,GAAmB,CAClC;MACEJ,IAAI,EAAE,YAAY;MAClBK,OAAO,EAAE,CACP;QACEH,KAAK,EAAE,cAAc;QACrBI,OAAO,EAAGC,GAAQ,IAAI;UACpB,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,GAAG,CAAC;QAC1B,CAAC;QACDE,IAAI,EAAE;OACP;KAEJ,CACF;IAuBC,IAAI,CAACvB,aAAa,CAACwB,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiB;IACf,IAAI,CAAC7B,oBAAoB,CAAC8B,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,IAAI,CAACC,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;MAEJ,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC;IACrC,CAAC,EACAC,KAAK,IAAI;MACR3D,IAAI,CAAC4D,IAAI,CAAC;QACRrC,KAAK,EAAE,OAAO;QACdsC,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnBpB,IAAI,EAAE,OAAO;QACbqB,iBAAiB,EAAE,IAAI,CAAC9C,iBAAiB,CAACQ,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAuC,SAAS;IACP,IAAI,CAACnD,eAAe,CAACoD,IAAI,EAAE;IAC3B,IAAI,CAACjD,YAAY,CAACkD,WAAW,EAAE,CAACpB,SAAS,CACtCqB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAACpB,IAAI;MACrB;MACA,IAAI,IAAI,CAACI,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;IAEN,CAAC,EACAI,KAAK,IAAI;MACR3D,IAAI,CAAC4D,IAAI,CAAC;QACRrC,KAAK,EAAE,OAAO;QACdsC,IAAI,EAAEF,KAAK,CAACG,OAAO;QACnBpB,IAAI,EAAE,OAAO;QACbqB,iBAAiB,EAAE,IAAI,CAAC9C,iBAAiB,CAACQ,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEA4C,cAAc,CAACC,MAAM;IACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACxB,QAAQ,GAAGqB,MAAM;MACtB,IAAI,IAAI,CAACnB,SAAS,CAACC,UAAU,EAAE;QAC7B,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;UAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;QAC1B,CAAC,CAAC;;MAEJiB,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAE,YAAY,CAACJ,MAAM;IACjBK,OAAO,CAACC,GAAG,CAAC,iBAAiBN,MAAM,EAAE,CAAC;IACtC,IAAI,CAACO,MAAM,GAAGP,MAAM;IACpB,IAAI,CAACnB,SAAS,CAACC,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;MAC5DA,UAAU,CAACE,IAAI,CAACC,MAAM,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAuB,QAAQ;IAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACvE,MAAM,CAACgB,OAAO,CAAC,gBAAgB,CAAC;MAClDwD,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVjD,IAAI,EAAE,EAAE;QACRkD,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAAC3E,MAAM,CAACgB,OAAO,CAAC,aAAa,CAAC;UACxC4D,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAAC3E,MAAM,CAACgB,OAAO,CAAC,gBAAgB,CAAC;UAC3C4D,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAACzC,iBAAiB,EAAE;IACxB,IAAI,CAACoB,SAAS,EAAE;IAChB,IAAI,CAACN,SAAS,GAAG;MACf4B,GAAG,EAAE,IAAI,CAAC/E,eAAe,CAACgF,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChB;MACAC,KAAK,EAAE,IAAI;MACXnC,IAAI,EAAE,CAACoC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C,IAAIC,MAAM,GACR,IAAI,CAACf,MAAM,IAAIgB,SAAS,GAAG,YAAY,IAAI,CAAChB,MAAM,EAAE,GAAG,EAAE;QAC3D,IAAI,CAACrE,KAAK,CACPsF,IAAI,CACH,GAAGhG,WAAW,CAAC+B,MAAM,YAAY,IAAI,CAACoB,QAAQ,WAAW2C,MAAM,EAAE,EACjEF,oBAAoB,CACrB,CACA5C,SAAS,CAAEiD,IAAS,IAAI;UACvBJ,QAAQ,CAAC;YACPK,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrClD,IAAI,EAAEgD,IAAI,CAAChD;WACZ,CAAC;QACJ,CAAC,CAAC;MACN,CAAC;MACDmD,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI,CAAC7F,eAAe,CAACgF,iBAAiB,CAACc,IAAI;MACrDC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACElF,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,iBAAiB,CAAC;QACxDsB,IAAI,EAAE,MAAM;QACZ2D,SAAS,EAAE,oBAAoB;QAC/BzE,IAAI,EAAE,YAAY;QAClB0E,MAAM,EAAE;UACNC,OAAO,EAAE,CAAC7D,IAAI,EAAEd,IAAI,EAAEO,GAAG,KAAI;YAC3B,OAAOO,IAAI,IAAI,KAAK;UACtB,CAAC;UACD8D,MAAM,EAAE,CAAC9D,IAAI,EAAEd,IAAI,EAAEO,GAAG,KAAI;YAC1B,OAAOO,IAAI;UACb;;OAEH,EACD;QACExB,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,OAAO,CAAC;QAC9CsB,IAAI,EAAE,YAAY;QAClB4D,MAAM,EAAE,UAAS5D,IAAI;UACnB,OAAO,uDAAuDA,IAAI,QAAQ;UAC1E;QAEF;OACD,EACD;QACExB,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,MAAM,CAAC;QAC7CsB,IAAI,EAAE,YAAY;QAClB2D,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,UAAS5D,IAAI,EAAEd,IAAI,EAAEO,GAAG;UAC9B,MAAMsE,WAAW,GAAI,CAAC/D,IAAI,IAAIA,IAAI,KAAK,KAAK,GAAI,KAAK,GAAG9C,MAAM,CAAC8C,IAAI,CAAC,CAACgE,MAAM,CAAC,YAAY,CAAC;UACzF,OAAO,4DAA4DD,WAAW,QAAQ;QACxF;OACD,EACD;QACEvF,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,YAAY,CAAC;QACnDsB,IAAI,EAAE,YAAY;QAClB2D,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,UAAS5D,IAAI,EAAEd,IAAI,EAAEO,GAAG;UAC9B,IAAI,CAACO,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAO9C,MAAM,CAAC8C,IAAI,CAAC,CAACgE,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACExF,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,UAAU,CAAC;QACjDsB,IAAI,EAAE,UAAU;QAChB2D,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,UAAS5D,IAAI,EAAEd,IAAI,EAAEO,GAAG;UAC9B,IAAI,CAACO,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAO9C,MAAM,CAAC8C,IAAI,CAAC,CAACgE,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACExF,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,UAAU,CAAC;QACjDsB,IAAI,EAAE,UAAU;QAChBiE,KAAK,EAAE,UAASjE,IAAI,EAAEd,IAAI,EAAEO,GAAG;UAC7B,IAAI,CAACO,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd;UACA,OAAOA,IAAI;QACb;OACD,EACD;QACExB,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,MAAM,CAAC;QAC7CsB,IAAI,EAAE;OACP,EACD;QACExB,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,MAAM,CAAC;QAC7CsB,IAAI,EAAE,gBAAgB;QACtB2D,SAAS,EAAE;OACZ,EACD;QACEnF,KAAK,EAAE,IAAI;QACXwB,IAAI,EAAE,IAAI;QACV2D,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,UAAS5D,IAAI,EAAEd,IAAI,EAAEO,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACEjB,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,MAAM,CAAC;QAC7CsB,IAAI,EAAE,gBAAgB;QACtB2D,SAAS,EAAE;OACZ,EACD;QACEnF,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,YAAY,CAAC;QACnDsB,IAAI,EAAE,YAAY;QAClB2D,SAAS,EAAE;OACZ,EACD;QACE3D,IAAI,EAAE,IAAI;QACV2D,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,UAAS5D,IAAI,EAAEd,IAAI,EAAEO,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACEjB,KAAK,EAAE,IAAI,CAACN,iBAAiB,CAACQ,OAAO,CAAC,YAAY,CAAC;QACnDsB,IAAI,EAAE,YAAY;QAClB2D,SAAS,EAAE;OACZ,CACF;MACDpE,OAAO,EAAE;QACPgD,GAAG,EAAE,IAAI,CAAC/E,eAAe,CAACgF,iBAAiB,CAACjD,OAAO,CAACgD,GAAG;QACvDhD,OAAO,EAAE,CACP;UACEuB,IAAI,EAAE,2CAA2C,IAAI,CAAC5C,iBAAiB,CAACQ,OAAO,CAC7E,YAAY,CACb,EAAE;UACHwF,MAAM,EAAE,KAAK;UACblF,MAAM;YAAA,6BAAE,WAAOmF,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAMtE,IAAI,GAAGoE,EAAE,CAAC7E,OAAO,CAACgF,UAAU,EAAE;cACpC,MAAM,KAAI,CAAClG,cAAc,CAACmG,SAAS,CAACxE,IAAI,EAAE,YAAY,CAAC;YACzD,CAAC;YAAA,gBAHDhB,MAAM;cAAA;YAAA;UAAA;SAIP;;KAGN;EACH;EAEAyF,QAAQ,CAACzE,IAAI;IACX,IAAI,CAACqB,KAAK,GAAGrB,IAAI;IACjB;IACA,MAAM0E,SAAS,GAAG,IAAI,CAACC,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAAC5F,GAAG,KAAK,SAAS,CAAC;IACtE;IACA,IAAI6F,aAAa,GAAG,EAAE;IACtB9E,IAAI,CAAC+E,OAAO,CAAEC,IAAI,IAAI;MACpB,IAAIC,SAAS,GAAG,IAAI,CAAC/G,iBAAiB,CAACQ,OAAO,CAACsG,IAAI,CAAC3C,IAAI,CAAC;MACzDyC,aAAa,CAACI,IAAI,CAAC;QACjB9F,KAAK,EAAE6F,SAAS;QAChBE,KAAK,EAAEH,IAAI,CAAC7E;OACb,CAAC;IACJ,CAAC,CAAC;IACFuE,SAAS,CAACvF,KAAK,CAACiG,OAAO,GAAGN,aAAa;EACzC;EAEAO,cAAc,CAACC,KAAU;IACvB;EAAA;EAGF5F,MAAM,CAACV,MAAM,EAAES,GAAI;IACjB,IAAI,CAACkF,MAAM,CAAC,CAAC,CAAC,CAACY,YAAY,GAAG,IAAI,CAACzD,MAAM;IACzC,QAAQ9C,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAAC2F,MAAM,CAAC,CAAC,CAAC,CAACxF,KAAK,CAACqG,QAAQ,GAAG,KAAK;QACrC;MACF,KAAK,MAAM;QACT,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,CAACxF,KAAK,CAACqG,QAAQ,GAAG,IAAI;QACpC;MACF,KAAK,QAAQ;QACX;MACF;QACE;IAAM;IAEV,IAAI,CAAC3C,MAAM,CAAC7D,MAAM,GAAGA,MAAM;IAC3B,IAAI,CAAC6D,MAAM,CAACpD,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClC,IAAI,CAACtB,mBAAmB,CAACsH,kBAAkB,CAAC,IAAI,CAAClH,UAAU,CAAC,CAACmH,UAAU,EAAE;EAC3E;EAEAC,eAAe;IACb,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjI,QAAQ,CAACkI,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGP,KAAK,IAAI;MACpE,IAAIA,KAAK,CAACQ,MAAM,CAACC,YAAY,CAAC,eAAe,CAAC,EAAE;QAC9C,IAAIC,aAAa,GAAGV,KAAK,CAACQ,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;QAC9D,IAAIC,QAAQ,GAAGZ,KAAK,CAACQ,MAAM,CAACG,YAAY,CAAC,UAAU,CAAC;QACpD;QACA,IAAI,CAAC1I,OAAO,CAAC4I,QAAQ,CAAC,CAACH,aAAa,EAAE,QAAQ,EAAEE,QAAQ,CAAC,EAAE;UACzDE,UAAU,EAAE,IAAI,CAAC9I;SAClB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA+I,WAAW;IACT,IAAI,CAAC5F,SAAS,CAAC6F,WAAW,EAAE;IAC5B,IAAI,CAACV,UAAU,EAAE;EACnB;EAAC;qBA5XUxI,sBAAsB;EAAA;EAAA;UAAtBA,sBAAsB;IAAAmJ;IAAAC;MAAA;;uBAEtB1J,kBAAkB;;;;;;;;;;;;;QCxC/BK,8BAA+C;QAG7CA,wCAAyE;QAEzEA,8BAAsB;QAGAA,YAAsB;;QAAAA,iBAAQ;QAClDA,oCACmC;QAD4DA;UAAA;QAAA,EAAsB;UAAA,OAC1GsJ,0BAAsB;QAAA,EADoF;;QAEpHtJ,qFACY;QACbA,iBAAY;QAEbA,+BAAmB;QACAA,aAAoB;;QAAAA,iBAAQ;QAC9CA,qCACiC;QAD2DA;UAAA;QAAA,EAAoB;UAAA,OACrGsJ,wBAAoB;QAAA,EADiF;;QAE/GtJ,qFAEY;QACbA,iBAAY;QAIdA,gCAAiB;QAIbA,6BAEQ;QACTA,iBAAM;QAOXA,yCAAqH;QACpHA,0CACqB;QACtBA,iBAAe;QAEfA,2HAGc;;;QA7CQA,eAA+B;QAA/BA,iDAA+B;QAK7BA,eAAsB;QAAtBA,qDAAsB;QACSA,eAA2C;QAA3CA,+EAA2C;QAAnFA,iCAAmB;QAECA,eAAU;QAAVA,qCAAU;QAKvBA,eAAoB;QAApBA,oDAAoB;QACYA,eAAyC;QAAzCA,8EAAyC;QAAhFA,iCAAmB;QAEDA,eAAQ;QAARA,mCAAQ;QAWlBA,eAAuB;QAAvBA,yCAAuB;QAUqBA,eAAmB;QAAnBA,qCAAmB;QACjEA,eAAmB;QAAnBA,qCAAmB", "names": ["DataTableDirective", "environment", "Subject", "<PERSON><PERSON>", "moment", "i0", "LeagueReportsComponent", "constructor", "route", "_router", "_commonsService", "_http", "_trans", "renderer", "_teamService", "_modalService", "_loadingService", "_toastService", "_registrationService", "_clubService", "_translateService", "_coreSidebarService", "_titleService", "_exportService", "editor_id", "table_name", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "key", "type", "props", "label", "placeholder", "required", "buttons", "onClick", "row", "editor", "icon", "setTitle", "_getCurrentSeason", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "dtElement", "dtInstance", "then", "ajax", "reload", "dtTrigger", "next", "dtOptions", "error", "fire", "text", "message", "confirmButtonText", "_getClubs", "show", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "onSelectClub", "console", "log", "clubId", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "name", "isLink", "dom", "dataTableDefaults", "select", "rowId", "dataTablesParameters", "callback", "params", "undefined", "post", "resp", "recordsTotal", "recordsFiltered", "responsive", "scrollX", "language", "lang", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "display", "filter", "displayDate", "format", "reder", "extend", "e", "dt", "button", "config", "exportData", "exportCsv", "setClubs", "clubField", "fields", "find", "field", "current_clubs", "for<PERSON>ach", "club", "club_name", "push", "value", "options", "onCaptureEvent", "event", "defaultValue", "disabled", "getSidebarRegistry", "toggle<PERSON><PERSON>", "ngAfterViewInit", "unlistener", "listen", "target", "hasAttribute", "tournament_id", "getAttribute", "stage_id", "navigate", "relativeTo", "ngOnDestroy", "unsubscribe", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  AfterViewInit,\r\n  ViewEncapsulation,\r\n  TemplateRef\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { CommonsService } from 'app/services/commons.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { ExportService } from 'app/services/export.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { takeUntil } from 'rxjs/operators';\r\nimport { Subject } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\r\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { EZBtnActions } from 'app/components/btn-dropdown-action/btn-dropdown-action.component';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss']\r\n})\r\nexport class LeagueReportsComponent implements AfterViewInit, OnInit {\r\n  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n  dtOptions: any = {};\r\n  private unlistener: () => void;\r\n  public currentTeam: any = {};\r\n  public seasonId: any;\r\n  public clubId: any;\r\n  public modalRef: any;\r\n  public contentHeader: object;\r\n  public seasons;\r\n  public clubs;\r\n  public table_name = 'team-table';\r\n  public params: EditorSidebarParams = {\r\n    editor_id: this.table_name,\r\n    title: {\r\n      create: this._translateService.instant('Create New Tournament'),\r\n      edit: 'Edit team',\r\n      remove: 'Delete team'\r\n    },\r\n    url: `${environment.apiUrl}/teams/editor`,\r\n    method: 'POST',\r\n    action: 'create'\r\n  };\r\n\r\n  public fields: any[] = [\r\n    {\r\n      key: 'home_score',\r\n      type: 'number',\r\n      props: {\r\n        label: this._translateService.instant('Home team score'),\r\n        placeholder: this._translateService.instant('Enter score of team'),\r\n        required: true\r\n      }\r\n    },\r\n    {\r\n      key: 'away_score',\r\n      type: 'number',\r\n      props: {\r\n        label: this._translateService.instant('Away team score'),\r\n        placeholder: this._translateService.instant('Enter score of team'),\r\n        required: true\r\n      }\r\n    }\r\n  ];\r\n\r\n  public rowActions: EZBtnActions[] = [\r\n    {\r\n      type: 'collection',\r\n      buttons: [\r\n        {\r\n          label: 'Update score',\r\n          onClick: (row: any) => {\r\n            this.editor('edit', row);\r\n          },\r\n          icon: 'fa-regular fa-pen-to-square'\r\n        }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  // private variables\r\n  private _unsubscribeAll: Subject<any>;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _commonsService: CommonsService,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _teamService: TeamService,\r\n    public _modalService: NgbModal,\r\n    public _loadingService: LoadingService,\r\n    public _toastService: ToastrService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _coreSidebarService: CoreSidebarService,\r\n    public _titleService: Title,\r\n    private _exportService: ExportService\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n        this.dtTrigger.next(this.dtOptions);\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._loadingService.show();\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n        // this.clubId = this.clubs[0];\r\n        if (this.dtElement.dtInstance) {\r\n          this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n            dtInstance.ajax.reload();\r\n          });\r\n        }\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      if (this.dtElement.dtInstance) {\r\n        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n          dtInstance.ajax.reload();\r\n        });\r\n      }\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\r\n      dtInstance.ajax.reload();\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n    this.dtOptions = {\r\n      dom: this._commonsService.dataTableDefaults.dom,\r\n      select: 'single',\r\n      // serverSide: true,\r\n      rowId: 'id',\r\n      ajax: (dataTablesParameters: any, callback) => {\r\n        let params =\r\n          this.clubId != undefined ? `?club_id=${this.clubId}` : '';\r\n        this._http\r\n          .post<any>(\r\n            `${environment.apiUrl}/seasons/${this.seasonId}/matches${params}`,\r\n            dataTablesParameters\r\n          )\r\n          .subscribe((resp: any) => {\r\n            callback({\r\n              recordsTotal: resp.recordsTotal,\r\n              recordsFiltered: resp.recordsFiltered,\r\n              data: resp.data\r\n            });\r\n          });\r\n      },\r\n      responsive: false,\r\n      scrollX: true,\r\n      language: this._commonsService.dataTableDefaults.lang,\r\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\r\n      columns: [\r\n        {\r\n          title: this._translateService.instant('League/Knockout'),\r\n          data: 'name',\r\n          className: 'font-weight-bolder',\r\n          type: 'any-number',\r\n          render: {\r\n            display: (data, type, row) => {\r\n              return data ?? 'TBD';\r\n            },\r\n            filter: (data, type, row) => {\r\n              return data;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Round'),\r\n          data: 'round_name',\r\n          render: function(data) {\r\n            return `<div class=\"text-center\" style=\"width:max-content;\">${data}</div>`;\r\n            // return `<div class=\"text-center\" style=\"min-width:max-content;\">${data}</div>`;\r\n\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Date'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            const displayDate = (!data || data === 'TBD') ? 'TBD' : moment(data).format('YYYY-MM-DD');\r\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Start time'),\r\n          data: 'start_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('End time'),\r\n          data: 'end_time',\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return moment(data).format('HH:mm');\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Location'),\r\n          data: 'location',\r\n          reder: function(data, type, row) {\r\n            if (!data || data == 'TBD') {\r\n              return 'TBD';\r\n            }\r\n            // format to HH:mm from ISO 8601\r\n            return data;\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('User'),\r\n          data: 'user'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home'),\r\n          data: 'home_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: 'VS',\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return 'vs';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away'),\r\n          data: 'away_team_name',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          title: this._translateService.instant('Home score'),\r\n          data: 'home_score',\r\n          className: 'text-center'\r\n        },\r\n        {\r\n          data: null,\r\n          className: 'text-center',\r\n          render: function(data, type, row) {\r\n            return '-';\r\n          }\r\n        },\r\n        {\r\n          title: this._translateService.instant('Away score'),\r\n          data: 'away_score',\r\n          className: 'text-center'\r\n        }\r\n      ],\r\n      buttons: {\r\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\r\n        buttons: [\r\n          {\r\n            text: `<i class=\"fa fa-file-excel-o mr-1\"></i> ${this._translateService.instant(\r\n              'Export CSV'\r\n            )}`,\r\n            extend: 'csv',\r\n            action: async (e: any, dt: any, button: any, config: any) => {\r\n              const data = dt.buttons.exportData();\r\n              await this._exportService.exportCsv(data, 'League.csv');\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n\r\n  setClubs(data) {\r\n    this.clubs = data;\r\n    // get field has key club_id\r\n    const clubField = this.fields.find((field) => field.key === 'club_id');\r\n    // set options for club field\r\n    let current_clubs = [];\r\n    data.forEach((club) => {\r\n      let club_name = this._translateService.instant(club.name);\r\n      current_clubs.push({\r\n        label: club_name,\r\n        value: club.id\r\n      });\r\n    });\r\n    clubField.props.options = current_clubs;\r\n  }\r\n\r\n  onCaptureEvent(event: any) {\r\n    // console.log(event);\r\n  }\r\n\r\n  editor(action, row?) {\r\n    this.fields[0].defaultValue = this.clubId;\r\n    switch (action) {\r\n      case 'create':\r\n        this.fields[2].props.disabled = false;\r\n        break;\r\n      case 'edit':\r\n        this.fields[2].props.disabled = true;\r\n        break;\r\n      case 'remove':\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.params.action = action;\r\n    this.params.row = row ? row : null;\r\n    this._coreSidebarService.getSidebarRegistry(this.table_name).toggleOpen();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.unlistener = this.renderer.listen('document', 'click', (event) => {\r\n      if (event.target.hasAttribute('tournament_id')) {\r\n        let tournament_id = event.target.getAttribute('tournament_id');\r\n        let stage_id = event.target.getAttribute('stage_id');\r\n        //  navigate to path ':tournament_id/stages/:stage_id'\r\n        this._router.navigate([tournament_id, 'stages', stage_id], {\r\n          relativeTo: this.route\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.dtTrigger.unsubscribe();\r\n    this.unlistener();\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n\t<div class=\"content-body\">\r\n\t\t<!-- content-header component -->\r\n\t\t<app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n\t\t<div class=\"row mb-1\">\r\n\t\t\t<!-- ng select season -->\r\n\t\t\t<div class=\"col-6\">\r\n\t\t\t\t<label for=\"season\">{{'Season'|translate}}</label>\r\n\t\t\t\t<ng-select [searchable]=\"true\" [clearable]=\"false\" placeholder=\"{{'Select Season'|translate}}\" [(ngModel)]=\"seasonId\"\r\n\t\t\t\t\t(change)=\"onSelectSeason($event)\">\r\n\t\t\t\t\t<ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">{{ season.name | translate }}\r\n\t\t\t\t\t</ng-option>\r\n\t\t\t\t</ng-select>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"col-6\">\r\n\t\t\t\t<label for=\"club\">{{'Club'|translate}}</label>\r\n\t\t\t\t<ng-select [clearable]=\"false\" [clearable]=\"true\" placeholder=\"{{'Select Club'|translate}}\" [(ngModel)]=\"clubId\"\r\n\t\t\t\t\t(change)=\"onSelectClub($event)\">\r\n\t\t\t\t\t<ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">\r\n\t\t\t\t\t\t{{ club.code | translate }}\r\n\t\t\t\t\t</ng-option>\r\n\t\t\t\t</ng-select>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"row\">\r\n\t\t\t<div class=\"col-12\">\r\n\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t<div class=\"pt-2\">\r\n\t\t\t\t\t\t<table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\"\r\n\t\t\t\t\t\t\tclass=\"table border row-border hover\">\r\n\t\t\t\t\t\t</table>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</div>\r\n\r\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\r\n\t<app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\r\n\t</app-editor-sidebar>\r\n</core-sidebar>\r\n\r\n<ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\r\n\t<app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\"\r\n\t\tbtnStyle=\"font-size:15px;color:black!important\"></app-btn-dropdown-action>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}