{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport Swal from 'sweetalert2';\nimport { Subject } from 'rxjs';\nimport { DataTableDirective } from 'angular-datatables';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/stage.service\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nfunction ModalAssignRefereesComponent_ngb_alert_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ngb-alert\", 10)(1, \"div\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"type\", \"warning\")(\"dismissible\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 4, \"Warning\"), \" \", i0.ɵɵpipeBind1(4, 6, \"You are editing multiple rows! Please be careful, data will be updated for all selected rows.\"), \" \");\n  }\n}\nexport class ModalAssignRefereesComponent {\n  constructor(_stageService, _translateService, _modalService) {\n    this._stageService = _stageService;\n    this._translateService = _translateService;\n    this._modalService = _modalService;\n    this.isMultipleAssign = false;\n    this.selectedIds = [];\n    this.listReferees = [];\n    this.dtOptions = {};\n    this.dtTrigger = new Subject();\n    this.dtElement = DataTableDirective;\n    this.onSubmit = new EventEmitter();\n  }\n  ngOnInit() {}\n  getListSelectedReferees() {\n    console.log(this.assignRefereeModel.list_referees);\n    console.log(this.listReferees.filter(e => this.assignRefereeModel.list_referees.includes(e.id)));\n    return this.listReferees.filter(e => this.assignRefereeModel.list_referees.includes(e.id));\n  }\n  closeModal() {\n    this._modalService.dismissAll();\n  }\n  onSubmitAssignReferee({\n    list_referees\n  }) {\n    console.log(list_referees);\n    Swal.fire({\n      title: this._translateService.instant('Assign Referee'),\n      text: `Are you sure you want to assign ${list_referees.length} referees to the selected matches?`,\n      icon: 'info',\n      confirmButtonText: this._translateService.instant('Assign')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._stageService.assignReferees(this.selectedIds, list_referees).subscribe(response => {\n          this.onSubmit.emit(true);\n          this.closeModal();\n          Swal.fire({\n            title: 'Success',\n            text: response.message,\n            icon: 'success',\n            confirmButtonText: 'OK'\n          });\n        }, error => {\n          Swal.fire({\n            title: 'Error',\n            text: error.message,\n            icon: 'error',\n            confirmButtonText: 'OK'\n          });\n        });\n      }\n    });\n  }\n  removeReferee(referee) {\n    console.log(referee);\n  }\n  static #_ = this.ɵfac = function ModalAssignRefereesComponent_Factory(t) {\n    return new (t || ModalAssignRefereesComponent)(i0.ɵɵdirectiveInject(i1.StageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.NgbModal));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalAssignRefereesComponent,\n    selectors: [[\"app-modal-assign-referees\"]],\n    viewQuery: function ModalAssignRefereesComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    inputs: {\n      isMultipleAssign: \"isMultipleAssign\",\n      assignRefereeForm: \"assignRefereeForm\",\n      assignRefereeFields: \"assignRefereeFields\",\n      assignRefereeModel: \"assignRefereeModel\",\n      selectedIds: \"selectedIds\",\n      listReferees: \"listReferees\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 15,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalAssignReferee\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"type\", \"dismissible\", 4, \"ngIf\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"btn\", \"btn-primary\"], [3, \"type\", \"dismissible\"], [1, \"alert-body\"]],\n    template: function ModalAssignRefereesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalAssignRefereesComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalAssignRefereesComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitAssignReferee(ctx.assignRefereeModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5);\n        i0.ɵɵtemplate(9, ModalAssignRefereesComponent_ngb_alert_9_Template, 5, 8, \"ngb-alert\", 6);\n        i0.ɵɵelementStart(10, \"formly-form\", 7);\n        i0.ɵɵlistener(\"submit\", function ModalAssignRefereesComponent_Template_formly_form_submit_10_listener() {\n          return ctx.onSubmitAssignReferee(ctx.assignRefereeModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Assign Referee\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.assignRefereeForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isMultipleAssign);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"form\", ctx.assignRefereeForm)(\"fields\", ctx.assignRefereeFields)(\"model\", ctx.assignRefereeModel);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 9, \"Assign\"), \" \");\n      }\n    },\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAkC,eAAe;AAEjF,OAAOC,IAAI,MAAM,aAAa;AAI9B,SAASC,OAAO,QAAQ,MAAM;AAG9B,SAASC,kBAAkB,QAAQ,oBAAoB;;;;;;;ICOnDC,qCAIC;IAEGA,YAOF;;;IAAAA,iBAAM;;;IAXNA,gCAAkB;IAIhBA,eAOF;IAPEA,4LAOF;;;ADXN,OAAM,MAAOC,4BAA4B;EAkBvCC,YACUC,aAA2B,EAC3BC,iBAAmC,EACnCC,aAAuB;IAFvB,kBAAa,GAAbF,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IACjB,kBAAa,GAAbC,aAAa;IAnBd,qBAAgB,GAAY,KAAK;IAIjC,gBAAW,GAAU,EAAE;IACvB,iBAAY,GAAG,EAAE;IAE1B,cAAS,GAAQ,EAAE;IACnB,cAAS,GAAyB,IAAIP,OAAO,EAAe;IAG5D,cAAS,GAAQC,kBAAkB;IAEzB,aAAQ,GAAG,IAAIH,YAAY,EAAE;EASvC;EAEAU,QAAQ,IACR;EAEAC,uBAAuB;IACrBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,kBAAkB,CAACC,aAAa,CAAC;IAClDH,OAAO,CAACC,GAAG,CAAC,IAAI,CAACG,YAAY,CAACC,MAAM,CAAEC,CAAC,IAAK,IAAI,CAACJ,kBAAkB,CAACC,aAAa,CAACI,QAAQ,CAACD,CAAC,CAACE,EAAE,CAAC,CAAC,CAAC;IAClG,OAAO,IAAI,CAACJ,YAAY,CAACC,MAAM,CAAEC,CAAC,IAAK,IAAI,CAACJ,kBAAkB,CAACC,aAAa,CAACI,QAAQ,CAACD,CAAC,CAACE,EAAE,CAAC,CAAC;EAC9F;EAEAC,UAAU;IACR,IAAI,CAACZ,aAAa,CAACa,UAAU,EAAE;EACjC;EAGAC,qBAAqB,CAAC;IAAER;EAAa,CAAE;IAErCH,OAAO,CAACC,GAAG,CAACE,aAAa,CAAC;IAE1Bd,IAAI,CAACuB,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI,CAACjB,iBAAiB,CAACkB,OAAO,CAAC,gBAAgB,CAAC;MACvDC,IAAI,EAAE,mCAAmCZ,aAAa,CAACa,MAAM,oCAAoC;MACjGC,IAAI,EAAE,MAAM;MACZC,iBAAiB,EAAE,IAAI,CAACtB,iBAAiB,CAACkB,OAAO,CAAC,QAAQ;KAC3D,CAAC,CAACK,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI,CAAC1B,aAAa,CAAC2B,cAAc,CAAC,IAAI,CAACC,WAAW,EAAEpB,aAAa,CAAC,CAC/DqB,SAAS,CAAEC,QAAQ,IAAI;UACtB,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;UAExB,IAAI,CAAClB,UAAU,EAAE;UAEjBpB,IAAI,CAACuB,IAAI,CAAC;YACRC,KAAK,EAAE,SAAS;YAChBE,IAAI,EAAEU,QAAQ,CAACG,OAAO;YACtBX,IAAI,EAAE,SAAS;YACfC,iBAAiB,EAAE;WACpB,CAAC;QACJ,CAAC,EAAGW,KAAK,IAAI;UACXxC,IAAI,CAACuB,IAAI,CAAC;YACRC,KAAK,EAAE,OAAO;YACdE,IAAI,EAAEc,KAAK,CAACD,OAAO;YACnBX,IAAI,EAAE,OAAO;YACbC,iBAAiB,EAAE;WACpB,CAAC;QACJ,CAAC,CAAC;;IAIR,CAAC,CAAC;EACJ;EAEAY,aAAa,CAACC,OAAO;IACnB/B,OAAO,CAACC,GAAG,CAAC8B,OAAO,CAAC;EACtB;EAAC;qBA/EUtC,4BAA4B;EAAA;EAAA;UAA5BA,4BAA4B;IAAAuC;IAAAC;MAAA;uBAY5B1C,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;QC9B/BC,8BAA0B;QACwBA,YAAkC;;QAAAA,iBAAK;QACvFA,iCAKC;QAFCA;UAAA,OAAS0C,gBAAY;QAAA,EAAC;QAGtB1C,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG3CA,+BAGC;QADCA;UAAA,OAAY0C,iDAAyC;QAAA,EAAC;QAEtD1C,8BAAkD;QAChDA,yFAcY;QACZA,uCAKC;QADCA;UAAA,OAAU0C,iDAAyC;QAAA,EAAC;QACrD1C,iBAAc;QAEjBA,+BAA0B;QAEtBA,aACF;;QAAAA,iBAAS;;;QAxCqCA,eAAkC;QAAlCA,4DAAkC;QAWlFA,eAA+B;QAA/BA,iDAA+B;QAK1BA,eAAsB;QAAtBA,2CAAsB;QAevBA,eAA0B;QAA1BA,4CAA0B;QAQ1BA,eACF;QADEA,gEACF", "names": ["EventEmitter", "<PERSON><PERSON>", "Subject", "DataTableDirective", "i0", "ModalAssignRefereesComponent", "constructor", "_stageService", "_translateService", "_modalService", "ngOnInit", "getListSelectedReferees", "console", "log", "assignRefereeModel", "list_referees", "listReferees", "filter", "e", "includes", "id", "closeModal", "dismissAll", "onSubmitAssignReferee", "fire", "title", "instant", "text", "length", "icon", "confirmButtonText", "then", "result", "isConfirmed", "assignReferees", "selectedIds", "subscribe", "response", "onSubmit", "emit", "message", "error", "remove<PERSON><PERSON><PERSON><PERSON>", "referee", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\modal-assign-referees\\modal-assign-referees.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-matches\\modal-assign-referees\\modal-assign-referees.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport Swal from 'sweetalert2';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { Subject } from 'rxjs';\r\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\r\nimport { environment } from '../../../../../../environments/environment';\r\nimport { DataTableDirective } from 'angular-datatables';\r\nimport { CommonsService } from '../../../../../services/commons.service';\r\nimport { HttpClient } from '@angular/common/http';\r\n\r\n@Component({\r\n  selector: 'app-modal-assign-referees',\r\n  templateUrl: './modal-assign-referees.component.html',\r\n  styleUrls: ['./modal-assign-referees.component.scss']\r\n})\r\nexport class ModalAssignRefereesComponent {\r\n\r\n  @Input() isMultipleAssign: boolean = false;\r\n  @Input() assignRefereeForm;\r\n  @Input() assignRefereeFields;\r\n  @Input() assignRefereeModel;\r\n  @Input() selectedIds: any[] = [];\r\n  @Input() listReferees = [];\r\n\r\n  dtOptions: any = {};\r\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\r\n\r\n  @ViewChild(DataTableDirective, { static: false })\r\n  dtElement: any = DataTableDirective;\r\n\r\n  @Output() onSubmit = new EventEmitter();\r\n\r\n\r\n  constructor(\r\n    private _stageService: StageService,\r\n    private _translateService: TranslateService,\r\n    private _modalService: NgbModal\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n  getListSelectedReferees() {\r\n    console.log(this.assignRefereeModel.list_referees);\r\n    console.log(this.listReferees.filter((e) => this.assignRefereeModel.list_referees.includes(e.id)));\r\n    return this.listReferees.filter((e) => this.assignRefereeModel.list_referees.includes(e.id));\r\n  }\r\n\r\n  closeModal() {\r\n    this._modalService.dismissAll();\r\n  }\r\n\r\n\r\n  onSubmitAssignReferee({ list_referees }) {\r\n\r\n    console.log(list_referees);\r\n\r\n    Swal.fire({\r\n      title: this._translateService.instant('Assign Referee'),\r\n      text: `Are you sure you want to assign ${list_referees.length} referees to the selected matches?`,\r\n      icon: 'info',\r\n      confirmButtonText: this._translateService.instant('Assign')\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this._stageService.assignReferees(this.selectedIds, list_referees)\r\n          .subscribe((response) => {\r\n            this.onSubmit.emit(true);\r\n\r\n            this.closeModal();\r\n\r\n            Swal.fire({\r\n              title: 'Success',\r\n              text: response.message,\r\n              icon: 'success',\r\n              confirmButtonText: 'OK'\r\n            });\r\n          }, (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: 'OK'\r\n            });\r\n          });\r\n\r\n      }\r\n\r\n    });\r\n  }\r\n\r\n  removeReferee(referee) {\r\n    console.log(referee);\r\n  }\r\n}", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"modalAssignReferee\">{{ \"Assign Referee\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"assignRefereeForm\"\r\n  (ngSubmit)=\"onSubmitAssignReferee(assignRefereeModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <ngb-alert\r\n      *ngIf=\"isMultipleAssign\"\r\n      [type]=\"'warning'\"\r\n      [dismissible]=\"false\"\r\n    >\r\n      <div class=\"alert-body\">\r\n        {{\r\n          'Warning'\r\n            | translate\r\n        }}\r\n        {{\r\n          'You are editing multiple rows! Please be careful, data will be updated for all selected rows.' |translate\r\n        }}\r\n      </div>\r\n    </ngb-alert>\r\n    <formly-form\r\n      [form]=\"assignRefereeForm\"\r\n      [fields]=\"assignRefereeFields\"\r\n      [model]=\"assignRefereeModel\"\r\n      (submit)=\"onSubmitAssignReferee(assignRefereeModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"btn btn-primary\" rippleEffect>\r\n      {{ 'Assign' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}