{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { AppConfig } from 'app/app-config';\nimport { SortByNamePipe } from '@core/pipes/sort-by-name.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/services/team.service\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction ModalAddGroupTeamComponent_label_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"Group Name\"));\n  }\n}\nfunction ModalAddGroupTeamComponent_ng_container_15_ng_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r6.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.label, \" \");\n  }\n}\nfunction ModalAddGroupTeamComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ng-select\", 17);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, ModalAddGroupTeamComponent_ng_container_15_ng_option_3_Template, 2, 2, \"ng-option\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"small\", 19);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"addTagText\", i0.ɵɵpipeBind1(2, 5, \"Create New\"));\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.groupPlaceholder)(\"addTag\", ctx_r1.createNew);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.groupNameOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"*\", i0.ɵɵpipeBind1(7, 7, \"Group name length must be less than 21 characters\"), \"\");\n  }\n}\nfunction ModalAddGroupTeamComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Group Name is required\"), \" \");\n  }\n}\nfunction ModalAddGroupTeamComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ModalAddGroupTeamComponent_div_16_div_1_Template, 3, 3, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.groupForm.get(\"group_name\").errors.required);\n  }\n}\nfunction ModalAddGroupTeamComponent_ng_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ng-option\", 22);\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r8.id)(\"innerHTML\", item_r8.name, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ModalAddGroupTeamComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"Selected Team is required\"), \" \");\n  }\n}\nfunction ModalAddGroupTeamComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ModalAddGroupTeamComponent_div_26_div_1_Template, 3, 3, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.groupForm.get(\"selected_team\").errors.required);\n  }\n}\nexport class ModalAddGroupTeamComponent {\n  constructor(_teamService, _activeModal, _translateService) {\n    this._teamService = _teamService;\n    this._activeModal = _activeModal;\n    this._translateService = _translateService;\n    this.selected_team = null;\n    this.stage_type = null;\n    this.group_name = '';\n    this.AppConfig = AppConfig;\n    this.groupNameOptions = [];\n    this.selectedGroupName = null;\n    this.groupForm = new FormGroup({\n      group_name: new FormControl(null, [Validators.required, Validators.maxLength(20)]),\n      selected_team: new FormControl(null, Validators.required) // add the selected_team form control\n    });\n\n    this.groupPlaceholder = this._translateService.instant(\"'Select' or 'Type new' the Group Name\");\n    this.teamPlaceholder = this._translateService.instant('Choose teams for group');\n  }\n  createNew(group) {\n    // max length of group name is 20\n    if (group.length > 20) {\n      group = group.substring(0, 20);\n    }\n    return group;\n  }\n  onClose() {\n    this.teams = [];\n    this._activeModal.close();\n  }\n  onSubmit() {\n    if (this.groupForm.valid) {\n      // submit the form data\n      this._activeModal.close(this.groupForm.value);\n    } else {\n      // show validation errors\n      // console.log('this.stage_type', this.stage_type);\n      if (this.stage_type != 'Groups') {\n        this.groupForm.get('group_name').disable();\n        // check again if form is valid\n        if (this.groupForm.valid) {\n          this._activeModal.close(this.groupForm.value);\n        }\n      }\n      this.groupForm.markAllAsTouched();\n    }\n  }\n  selectAllTeam() {\n    // select all team in ng-select\n    this.groupForm.get('selected_team').setValue(this.teams.map(team => team.id));\n  }\n  ngOnInit() {\n    // console.log('this.current_group', this.current_group);\n    // console.log('team', this.teams);\n    this.current_group.forEach(group => {\n      this.groupNameOptions.push({\n        label: group,\n        value: group\n      });\n    });\n  }\n  static #_ = this.ɵfac = function ModalAddGroupTeamComponent_Factory(t) {\n    return new (t || ModalAddGroupTeamComponent)(i0.ɵɵdirectiveInject(i1.TeamService), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i3.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalAddGroupTeamComponent,\n    selectors: [[\"app-modal-add-group-team\"]],\n    inputs: {\n      teams: \"teams\",\n      selected_team: \"selected_team\",\n      stage_type: \"stage_type\",\n      group_name: \"group_name\",\n      current_group: \"current_group\"\n    },\n    features: [i0.ɵɵProvidersFeature([SortByNamePipe])],\n    decls: 31,\n    vars: 27,\n    consts: [[1, \"modal-header\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [1, \"\"], [1, \"pb-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"group_name\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"team\"], [\"data-toggle\", \"tooltip\", \"data-placement\", \"right\", \"title\", \"Select all team in this group\", 1, \"float-right\", \"text-primary\", 3, \"click\"], [\"formControlName\", \"selected_team\", 3, \"multiple\", \"placeholder\", \"closeOnSelect\"], [3, \"value\", \"innerHTML\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"float-right\"], [1, \"fa\", \"fa-plus\"], [\"for\", \"group_name\"], [\"formControlName\", \"group_name\", 3, \"placeholder\", \"addTagText\", \"addTag\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-secondary\"], [3, \"value\"], [1, \"text-danger\"], [3, \"value\", \"innerHTML\"]],\n    template: function ModalAddGroupTeamComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"div\", 0)(2, \"button\", 1);\n        i0.ɵɵlistener(\"click\", function ModalAddGroupTeamComponent_Template_button_click_2_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵelementStart(3, \"span\", 2);\n        i0.ɵɵtext(4, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\")(7, \"h4\")(8, \"span\", 4);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵpipe(11, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(12, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalAddGroupTeamComponent_Template_form_ngSubmit_12_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(13, \"div\", 6);\n        i0.ɵɵtemplate(14, ModalAddGroupTeamComponent_label_14_Template, 3, 3, \"label\", 7);\n        i0.ɵɵtemplate(15, ModalAddGroupTeamComponent_ng_container_15_Template, 8, 9, \"ng-container\", 8);\n        i0.ɵɵtemplate(16, ModalAddGroupTeamComponent_div_16_Template, 2, 1, \"div\", 9);\n        i0.ɵɵelementStart(17, \"label\", 10);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"a\", 11);\n        i0.ɵɵlistener(\"click\", function ModalAddGroupTeamComponent_Template_a_click_20_listener() {\n          return ctx.selectAllTeam();\n        });\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"ng-select\", 12);\n        i0.ɵɵtemplate(24, ModalAddGroupTeamComponent_ng_option_24_Template, 1, 2, \"ng-option\", 13);\n        i0.ɵɵpipe(25, \"sortByName\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(26, ModalAddGroupTeamComponent_div_26_Template, 2, 1, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"button\", 14);\n        i0.ɵɵelement(28, \"i\", 15);\n        i0.ɵɵtext(29);\n        i0.ɵɵpipe(30, \"translate\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(10, 14, \"Add Teams For\"), \" \", i0.ɵɵpipeBind1(11, 16, \"Groups\"), \"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.groupForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == ctx.AppConfig.TOURNAMENT_TYPES.groups);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.stage_type == ctx.AppConfig.TOURNAMENT_TYPES.groups);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.groupForm.get(\"group_name\").invalid && (ctx.groupForm.get(\"group_name\").dirty || ctx.groupForm.get(\"group_name\").touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 18, \"Teams\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 20, \"Select all\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"multiple\", true)(\"placeholder\", ctx.teamPlaceholder)(\"closeOnSelect\", false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(25, 22, ctx.teams, \"text\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.groupForm.get(\"selected_team\").invalid && (ctx.groupForm.get(\"selected_team\").dirty || ctx.groupForm.get(\"selected_team\").touched));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(30, 25, \"Add\"), \" \");\n      }\n    },\n    styles: [\".ng-select.ng-select-multiple[_ngcontent-%COMP%]   .ng-placeholder[_ngcontent-%COMP%] {\\n  display: block;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n  color: #999;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlLXRlYW1zL21vZGFsLWFkZC1ncm91cC10ZWFtL21vZGFsLWFkZC1ncm91cC10ZWFtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksY0FBQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLG9CQUFBO0VBQ0EsV0FBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLm5nLXNlbGVjdC5uZy1zZWxlY3QtbXVsdGlwbGUgLm5nLXBsYWNlaG9sZGVyIHtcclxuICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgdG9wOiAwO1xyXG4gICAgbGVmdDogMDtcclxuICAgIHJpZ2h0OiAwO1xyXG4gICAgYm90dG9tOiAwO1xyXG4gICAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbiAgICBjb2xvcjogIzk5OTtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,+BAA+B;;;;;;;ICctDC,iCAAiF;IAAAA,YAE7E;;IAAAA,iBAAQ;;;IAFqEA,eAE7E;IAF6EA,wDAE7E;;;;;IAKAA,qCAAsE;IACpEA,YACF;IAAAA,iBAAY;;;;IAFqCA,qCAAoB;IACnEA,eACF;IADEA,8CACF;;;;;IALJA,6BAAuE;IACrEA,qCAC+D;;IAC7DA,wGAEY;IACdA,iBAAY;IACZA,yBAAG;IAC6BA,YAAkE;;IAAAA,iBAAQ;IAE5GA,0BAAe;;;;IARXA,eAAuC;IAAvCA,0EAAuC;IAD9BA,qDAAgC;IAEbA,eAAmB;IAAnBA,iDAAmB;IAKjBA,eAAkE;IAAlEA,yGAAkE;;;;;IAQlGA,2BAAyD;IACvDA,YACF;;IAAAA,iBAAM;;;IADJA,eACF;IADEA,+EACF;;;;;IAPFA,+BAIwB;IACtBA,kFAEM;IACRA,iBAAM;;;;IAHEA,eAAiD;IAAjDA,yEAAiD;;;;;IAevDA,gCACY;;;;IAD+CA,kCAAiB;;;;;IAQ5EA,2BAA4D;IAC1DA,YACF;;IAAAA,iBAAM;;;IADJA,eACF;IADEA,kFACF;;;;;IAPFA,+BAIwB;IACtBA,kFAEM;IACRA,iBAAM;;;;IAHEA,eAAoD;IAApDA,4EAAoD;;;ADjDpE,OAAM,MAAOC,0BAA0B;EAoBrCC,YACUC,YAAyB,EACzBC,YAA4B,EAC5BC,iBAAmC;IAFnC,iBAAY,GAAZF,YAAY;IACZ,iBAAY,GAAZC,YAAY;IACZ,sBAAiB,GAAjBC,iBAAiB;IArBlB,kBAAa,GAAQ,IAAI;IACzB,eAAU,GAAQ,IAAI;IACtB,eAAU,GAAW,EAAE;IAEhC,cAAS,GAAGP,SAAS;IAErB,qBAAgB,GAAG,EAAE;IAErB,sBAAiB,GAAQ,IAAI;IAE7B,cAAS,GAAG,IAAIF,SAAS,CAAC;MACxBU,UAAU,EAAE,IAAIX,WAAW,CAAC,IAAI,EAAE,CAChCE,UAAU,CAACU,QAAQ,EACnBV,UAAU,CAACW,SAAS,CAAC,EAAE,CAAC,CACzB,CAAC;MACFC,aAAa,EAAE,IAAId,WAAW,CAAC,IAAI,EAAEE,UAAU,CAACU,QAAQ,CAAC,CAAE;KAC5D,CAAC;;IA+CF,qBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACK,OAAO,CAC/C,uCAAuC,CACxC;IACD,oBAAe,GAAG,IAAI,CAACL,iBAAiB,CAACK,OAAO,CAAC,wBAAwB,CAAC;EA5CvE;EAIHC,SAAS,CAACC,KAAK;IACb;IACA,IAAIA,KAAK,CAACC,MAAM,GAAG,EAAE,EAAE;MACrBD,KAAK,GAAGA,KAAK,CAACE,SAAS,CAAC,CAAC,EAAC,EAAE,CAAC;;IAE/B,OAAOF,KAAK;EACd;EAEAG,OAAO;IACL,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACZ,YAAY,CAACa,KAAK,EAAE;EAC3B;EAEAC,QAAQ;IACN,IAAI,IAAI,CAACC,SAAS,CAACC,KAAK,EAAE;MACxB;MACA,IAAI,CAAChB,YAAY,CAACa,KAAK,CAAC,IAAI,CAACE,SAAS,CAACE,KAAK,CAAC;KAC9C,MAAM;MACL;MACA;MACA,IAAI,IAAI,CAACC,UAAU,IAAI,QAAQ,EAAE;QAC/B,IAAI,CAACH,SAAS,CAACI,GAAG,CAAC,YAAY,CAAC,CAACC,OAAO,EAAE;QAC1C;QACA,IAAI,IAAI,CAACL,SAAS,CAACC,KAAK,EAAE;UACxB,IAAI,CAAChB,YAAY,CAACa,KAAK,CAAC,IAAI,CAACE,SAAS,CAACE,KAAK,CAAC;;;MAGjD,IAAI,CAACF,SAAS,CAACM,gBAAgB,EAAE;;EAErC;EACAC,aAAa;IACX;IACA,IAAI,CAACP,SAAS,CACXI,GAAG,CAAC,eAAe,CAAC,CACpBI,QAAQ,CAAC,IAAI,CAACX,KAAK,CAACY,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,EAAE,CAAC,CAAC;EAChD;EAMAC,QAAQ;IACN;IACA;IACA,IAAI,CAACC,aAAa,CAACC,OAAO,CAAErB,KAAK,IAAI;MACnC,IAAI,CAACsB,gBAAgB,CAACC,IAAI,CAAC;QACzBC,KAAK,EAAExB,KAAK;QACZS,KAAK,EAAET;OACR,CAAC;IACJ,CAAC,CAAC;EACJ;EAAC;qBA9EUX,0BAA0B;EAAA;EAAA;UAA1BA,0BAA0B;IAAAoC;IAAAC;MAAAtB;MAAAP;MAAAa;MAAAhB;MAAA0B;IAAA;IAAAO,iCAF3B,CAACxC,cAAc,CAAC;IAAAyC;IAAAC;IAAAC;IAAAC;MAAA;QCZ5B3C,2BAAK;QAO8EA;UAAA,OAAS4C,aAAS;QAAA,EAAC;QAChG5C,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG3CA,8BAAkD;QAG7BA,YAA0D;;;QAAAA,iBAAO;QAIpFA,gCAAmE;QAArCA;UAAA,OAAY4C,cAAU;QAAA,EAAC;QACnD5C,+BAAwB;QACtBA,iFAEY;QAEZA,+FAUe;QACfA,6EAQM;QAENA,kCAAkB;QAAAA,aAAyB;;QAAAA,iBAAQ;QAGnDA,8BACwC;QADrCA;UAAA,OAAS4C,mBAAe;QAAA,EAAC;QAE1B5C,aACF;;QAAAA,iBAAI;QAEJA,sCAC0B;QACxBA,0FACY;;QACdA,iBAAY;QACZA,6EAQM;QACRA,iBAAM;QAENA,mCAA0D;QACxDA,yBAA0B;QAC1BA,aACF;;QAAAA,iBAAS;;;QA1DQA,eAA0D;QAA1DA,6GAA0D;QAIvEA,eAAuB;QAAvBA,yCAAuB;QAEjBA,eAAsD;QAAtDA,8EAAsD;QAI/CA,eAAsD;QAAtDA,8EAAsD;QAW/DA,eAIL;QAJKA,oJAIL;QAMiBA,eAAyB;QAAzBA,qDAAyB;QAKzCA,eACF;QADEA,qEACF;QAEWA,eAAiB;QAAjBA,+BAAiB;QAEEA,eAA4B;QAA5BA,mEAA4B;QAGpDA,eAIL;QAJKA,6JAIL;QASDA,eACF;QADEA,8DACF", "names": ["FormControl", "FormGroup", "Validators", "AppConfig", "SortByNamePipe", "i0", "ModalAddGroupTeamComponent", "constructor", "_teamService", "_activeModal", "_translateService", "group_name", "required", "max<PERSON><PERSON><PERSON>", "selected_team", "instant", "createNew", "group", "length", "substring", "onClose", "teams", "close", "onSubmit", "groupForm", "valid", "value", "stage_type", "get", "disable", "mark<PERSON>llAsTouched", "selectAllTeam", "setValue", "map", "team", "id", "ngOnInit", "current_group", "for<PERSON>ach", "groupNameOptions", "push", "label", "selectors", "inputs", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\modal-add-group-team\\modal-add-group-team.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-teams\\modal-add-group-team\\modal-add-group-team.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { TeamService } from 'app/services/team.service';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { SortByNamePipe } from '@core/pipes/sort-by-name.pipe';\r\n\r\n@Component({\r\n  selector: 'app-modal-add-group-team',\r\n  templateUrl: './modal-add-group-team.component.html',\r\n  styleUrls: ['./modal-add-group-team.component.scss'],\r\n  providers:[SortByNamePipe]\r\n})\r\nexport class ModalAddGroupTeamComponent implements OnInit {\r\n  @Input() teams: any;\r\n  @Input() selected_team: any = null;\r\n  @Input() stage_type: any = null;\r\n  @Input() group_name: string = '';\r\n  @Input() current_group: any;\r\n  AppConfig = AppConfig;\r\n\r\n  groupNameOptions = [];\r\n\r\n  selectedGroupName: any = null;\r\n\r\n  groupForm = new FormGroup({\r\n    group_name: new FormControl(null, [\r\n      Validators.required,\r\n      Validators.maxLength(20),\r\n    ]),\r\n    selected_team: new FormControl(null, Validators.required), // add the selected_team form control\r\n  });\r\n\r\n  constructor(\r\n    private _teamService: TeamService,\r\n    private _activeModal: NgbActiveModal,\r\n    private _translateService: TranslateService\r\n  ) {}\r\n\r\n\r\n\r\n  createNew(group) {\r\n    // max length of group name is 20\r\n    if (group.length > 20) {\r\n      group = group.substring(0,20);\r\n    }\r\n    return group;\r\n  }\r\n\r\n  onClose() {\r\n    this.teams = [];\r\n    this._activeModal.close();\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.groupForm.valid) {\r\n      // submit the form data\r\n      this._activeModal.close(this.groupForm.value);\r\n    } else {\r\n      // show validation errors\r\n      // console.log('this.stage_type', this.stage_type);\r\n      if (this.stage_type != 'Groups') {\r\n        this.groupForm.get('group_name').disable();\r\n        // check again if form is valid\r\n        if (this.groupForm.valid) {\r\n          this._activeModal.close(this.groupForm.value);\r\n        }\r\n      }\r\n      this.groupForm.markAllAsTouched();\r\n    }\r\n  }\r\n  selectAllTeam() {\r\n    // select all team in ng-select\r\n    this.groupForm\r\n      .get('selected_team')\r\n      .setValue(this.teams.map((team) => team.id));\r\n  }\r\n\r\n  groupPlaceholder = this._translateService.instant(\r\n    \"'Select' or 'Type new' the Group Name\"\r\n  );\r\n  teamPlaceholder = this._translateService.instant('Choose teams for group');\r\n  ngOnInit(): void {\r\n    // console.log('this.current_group', this.current_group);\r\n    // console.log('team', this.teams);\r\n    this.current_group.forEach((group) => {\r\n      this.groupNameOptions.push({\r\n        label: group,\r\n        value: group,\r\n      });\r\n    });\r\n  }\r\n}\r\n", "<div>\r\n  <div class=\"modal-header\">\r\n    <!-- text center title and subtitle -->\r\n    <!-- title is 'Team List 'H2 -->\r\n    <!-- subtitle is 'Assign player to team' h5-->\r\n\r\n\r\n    <button type=\"button\" class=\"close\" data-bs-dismiss=\"modal\" aria-label=\"Close\" (click)=\"onClose()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <div>\r\n      <h4>\r\n        <span class=\"\">{{ 'Add Teams For' | translate }} {{'Groups' | translate}}</span>\r\n      </h4>\r\n    </div>\r\n    <!-- form group -->\r\n    <form [formGroup]=\"groupForm\" (ngSubmit)=\"onSubmit()\" class=\"pb-3\">\r\n      <div class=\"form-group\">\r\n        <label *ngIf=\"stage_type ==  AppConfig.TOURNAMENT_TYPES.groups\" for=\"group_name\">{{\r\n          'Group Name' | translate\r\n          }}</label>\r\n\r\n        <ng-container *ngIf=\"stage_type ==  AppConfig.TOURNAMENT_TYPES.groups\">\r\n          <ng-select [placeholder]=\"groupPlaceholder\" formControlName=\"group_name\"\r\n            addTagText=\"{{'Create New'|translate}}\" [addTag]=\"createNew\">\r\n            <ng-option *ngFor=\"let item of groupNameOptions\" [value]=\"item.value\">\r\n              {{ item.label }}\r\n            </ng-option>\r\n          </ng-select>\r\n          <p>\r\n            <small class=\"text-secondary\">*{{'Group name length must be less than 21 characters'|translate}}</small>\r\n          </p>\r\n        </ng-container>\r\n        <div *ngIf=\"\r\n            groupForm.get('group_name').invalid &&\r\n            (groupForm.get('group_name').dirty ||\r\n              groupForm.get('group_name').touched)\r\n          \" class=\"text-danger\">\r\n          <div *ngIf=\"groupForm.get('group_name').errors.required\">\r\n            {{ 'Group Name is required' | translate }}\r\n          </div>\r\n        </div>\r\n\r\n        <label for=\"team\">{{ 'Teams' | translate }}</label>\r\n        <!-- select all team -->\r\n        <!-- hover underline -->\r\n        <a (click)=\"selectAllTeam()\" class=\"float-right text-primary\" data-toggle=\"tooltip\" data-placement=\"right\"\r\n          title=\"Select all team in this group\">\r\n          {{ 'Select all' | translate }}\r\n        </a>\r\n\r\n        <ng-select [multiple]=\"true\" [placeholder]=\"teamPlaceholder\" formControlName=\"selected_team\"\r\n          [closeOnSelect]=\"false\">\r\n          <ng-option *ngFor=\"let item of teams | sortByName:'text' \" [value]=\"item.id\" [innerHTML]=\"item.name\">\r\n          </ng-option>\r\n        </ng-select>\r\n        <div *ngIf=\"\r\n            groupForm.get('selected_team').invalid &&\r\n            (groupForm.get('selected_team').dirty ||\r\n              groupForm.get('selected_team').touched)\r\n          \" class=\"text-danger\">\r\n          <div *ngIf=\"groupForm.get('selected_team').errors.required\">\r\n            {{ 'Selected Team is required' | translate }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <button type=\"submit\" class=\"btn btn-primary float-right\">\r\n        <i class=\"fa fa-plus\"></i>\r\n        {{ 'Add' | translate }}\r\n      </button>\r\n    </form>\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}