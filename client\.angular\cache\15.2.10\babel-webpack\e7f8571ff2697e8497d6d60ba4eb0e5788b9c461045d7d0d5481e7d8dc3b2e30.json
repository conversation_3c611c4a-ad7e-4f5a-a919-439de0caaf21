{"ast": null, "code": "import { ViewContainerRef } from '@angular/core';\nimport { FieldWrapper } from '@ngx-formly/core';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"fieldComponent\"];\nfunction DetailsWrapperComponent_ng_container_3_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p\", 4);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r2.description, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DetailsWrapperComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsWrapperComponent_ng_container_3_p_1_Template, 1, 1, \"p\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r2.value === ctx_r1.field.formControl.value && item_r2.description);\n  }\n}\nexport class DetailsWrapperComponent extends FieldWrapper {\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵDetailsWrapperComponent_BaseFactory;\n    return function DetailsWrapperComponent_Factory(t) {\n      return (ɵDetailsWrapperComponent_BaseFactory || (ɵDetailsWrapperComponent_BaseFactory = i0.ɵɵgetInheritedFactory(DetailsWrapperComponent)))(t || DetailsWrapperComponent);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DetailsWrapperComponent,\n    selectors: [[\"formly-wrapper-details\"]],\n    viewQuery: function DetailsWrapperComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fieldComponent = _t.first);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 4,\n    vars: 1,\n    consts: [[1, \"form-group\"], [\"fieldComponent\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"ml-1\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"ml-1\", 3, \"innerHTML\"]],\n    template: function DetailsWrapperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementContainer(1, null, 1);\n        i0.ɵɵtemplate(3, DetailsWrapperComponent_ng_container_3_Template, 2, 1, \"ng-container\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.field.props.options);\n      }\n    },\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAA+BA,gBAAgB,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,kBAAkB;;;;;IASvCC,uBACI;;;;IAD+EA,kEAA8B;;;;;IADnHA,6BAAuD;IACrDA,mFACI;IACNA,0BAAe;;;;;IAFIA,eAAgE;IAAhEA,8FAAgE;;;AAMzF,OAAM,MAAOC,uBAAwB,SAAQF,YAAY;EAAA;;;uHAA5CE,uBAAuB,SAAvBA,uBAAuB;IAAA;EAAA;EAAA;UAAvBA,uBAAuB;IAAAC;IAAAC;MAAA;+BACGL,gBAAgB;;;;;;;;;;;;;QAVnDE,8BAAwB;QACtBA,iCAA6C;QAC7CA,0FAGe;QACjBA,iBAAM;;;QAJ2BA,eAAsB;QAAtBA,iDAAsB", "names": ["ViewContainerRef", "FieldWrapper", "i0", "DetailsWrapperComponent", "selectors", "viewQuery"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stage-details\\details-wraper.component.ts"], "sourcesContent": ["import { Component, ViewChild, ViewContainerRef } from '@angular/core';\r\nimport { FieldWrapper } from '@ngx-formly/core';\r\n\r\n@Component({\r\n  selector: 'formly-wrapper-details',\r\n  template: `\r\n    <style></style>\r\n    <div class=\"form-group\">\r\n      <ng-container #fieldComponent></ng-container>\r\n      <ng-container *ngFor=\"let item of field.props.options\">\r\n        <p class=\"ml-1\" *ngIf=\"item.value === field.formControl.value && item.description\" [innerHTML]=\"item.description\">\r\n        </p>\r\n      </ng-container>\r\n    </div>\r\n  `,\r\n})\r\nexport class DetailsWrapperComponent extends FieldWrapper {\r\n  @ViewChild('fieldComponent', { read: ViewContainerRef })\r\n  fieldComponent: ViewContainerRef;\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}