{"ast": null, "code": "import { AppConfig } from 'app/app-config';\nimport { fork<PERSON>oin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"app/services/tournament.service\";\nimport * as i4 from \"app/services/stage.service\";\nimport * as i5 from \"app/services/loading.service\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"app/layout/components/content-header/content-header.component\";\nimport * as i10 from \"./stage-details/stage-details.component\";\nimport * as i11 from \"./stage-tables/stage-tables.component\";\nimport * as i12 from \"./stage-teams/stage-teams.component\";\nimport * as i13 from \"./stage-matches/stage-matches.component\";\nfunction StagesComponent_section_3_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"stage-details\", 15);\n    i0.ɵɵlistener(\"onDataChange\", function StagesComponent_section_3_ng_template_10_Template_stage_details_onDataChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.onDataChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"stage\", ctx_r2.current_Stage)(\"tournament\", ctx_r2.current_Tournament);\n  }\n}\nfunction StagesComponent_section_3_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"stage-teams\", 16);\n    i0.ɵɵlistener(\"onDataChange\", function StagesComponent_section_3_ng_template_15_Template_stage_teams_onDataChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onTeamChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"stage\", ctx_r3.current_Stage)(\"tournament\", ctx_r3.current_Tournament)(\"group_stages\", ctx_r3.group_stages);\n  }\n}\nfunction StagesComponent_section_3_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-stage-matches\", 17);\n    i0.ɵɵlistener(\"onDataChange\", function StagesComponent_section_3_ng_template_20_Template_app_stage_matches_onDataChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onMatchTableChange($event));\n    })(\"onUpdateScore\", function StagesComponent_section_3_ng_template_20_Template_app_stage_matches_onUpdateScore_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onUpdateScore($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"stage\", ctx_r4.current_Stage)(\"tournament\", ctx_r4.current_Tournament);\n  }\n}\nfunction StagesComponent_section_3_li_21_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"stage-tables\", 19);\n    i0.ɵɵlistener(\"onDataChange\", function StagesComponent_section_3_li_21_ng_template_4_Template_stage_tables_onDataChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.onDataChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"stage\", ctx_r13.current_Stage)(\"tableData\", ctx_r13.tableData);\n  }\n}\nfunction StagesComponent_section_3_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 10)(1, \"a\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StagesComponent_section_3_li_21_ng_template_4_Template, 1, 2, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"Tables\"));\n  }\n}\nfunction StagesComponent_section_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7)(4, \"ul\", 8, 9)(6, \"li\", 10)(7, \"a\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, StagesComponent_section_3_ng_template_10_Template, 1, 2, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"li\", 10)(12, \"a\", 11);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, StagesComponent_section_3_ng_template_15_Template, 1, 3, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\", 10)(17, \"a\", 11);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, StagesComponent_section_3_ng_template_20_Template, 1, 2, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, StagesComponent_section_3_li_21_Template, 5, 3, \"li\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"div\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r1 = i0.ɵɵreference(5);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"Details\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 7, \"Teams\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 9, \"Matches\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.current_Stage.type != \"Knockout\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbNavOutlet\", _r1);\n  }\n}\nexport class StagesComponent {\n  constructor(_trans, _route, _router, _tournamentService, _stageService, _loadingService, stageService, _titleService) {\n    this._trans = _trans;\n    this._route = _route;\n    this._router = _router;\n    this._tournamentService = _tournamentService;\n    this._stageService = _stageService;\n    this._loadingService = _loadingService;\n    this.stageService = stageService;\n    this._titleService = _titleService;\n    this.current_Tournament = null;\n    this.current_Stage = null;\n    this.group_stages = [];\n    this.allowEditTeam = true;\n    this.tournament_id = this._route.snapshot.paramMap.get('tournament_id');\n    this.stage_id = this._route.snapshot.paramMap.get('stage_id');\n    _tournamentService.getTournament(this.tournament_id).subscribe(res => {\n      this.current_Tournament = res;\n      _stageService.getStage(this.stage_id).subscribe(res => {\n        this.current_Stage = res;\n        this._loadingService.show();\n        forkJoin([this.getTeamsInStage(), this.hasMatches(), this.getTableData()]).subscribe({\n          complete: () => {\n            this._loadingService.dismiss();\n          }\n        });\n        _titleService.setTitle(this.current_Tournament.name);\n        this.contentHeader = {\n          headerTitle: this.current_Tournament.name,\n          actionButton: false,\n          breadcrumb: {\n            type: '',\n            links: [{\n              name: this._trans.instant('Leagues'),\n              isLink: false\n            }, {\n              name: this._trans.instant('Manage Leagues'),\n              isLink: true,\n              link: '/leagues/manage'\n            }, {\n              name: this.current_Stage.name\n            }, {\n              name: this.current_Tournament.group_name\n            }, {\n              name: this.current_Tournament.name\n            }]\n          }\n        };\n      });\n    });\n  }\n  getTableData(updateOrder = false) {\n    let query = `?force_display=1`;\n    if (updateOrder) {\n      query += `&update_order=true`;\n    }\n    this.stageService.getTableData(this.current_Stage.id, query).subscribe(data => {\n      this.tableData = data;\n      if (this.current_Stage.type === AppConfig.TOURNAMENT_TYPES.groups) {\n        this._stageService.checkMatchScore(this.current_Stage.id).subscribe();\n      }\n    });\n  }\n  getTeamsInStage() {\n    this._stageService.getTeamsInStage(this.current_Stage.id).toPromise().then(res => {\n      this.group_stages = res.data;\n    });\n  }\n  hasMatches() {\n    this._stageService.hasMatches(this.current_Stage.id).toPromise().then(res => {\n      this.allowEditTeam = !res.hasMatches;\n      return this.allowEditTeam;\n    });\n  }\n  onDataChange($event) {\n    this.getTableData(true);\n  }\n  onMatchTableChange($event) {\n    this.hasMatches();\n    this.getTableData(true);\n  }\n  onTeamChange($event) {\n    this.group_stages = $event;\n    this.getTableData(true);\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function StagesComponent_Factory(t) {\n    return new (t || StagesComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TournamentService), i0.ɵɵdirectiveInject(i4.StageService), i0.ɵɵdirectiveInject(i5.LoadingService), i0.ɵɵdirectiveInject(i4.StageService), i0.ɵɵdirectiveInject(i6.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StagesComponent,\n    selectors: [[\"app-stages\"]],\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"stage-page\", 4, \"ngIf\"], [\"id\", \"stage-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"\", 4, \"ngIf\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"], [3, \"stage\", \"tournament\", \"onDataChange\"], [3, \"stage\", \"tournament\", \"group_stages\", \"onDataChange\"], [3, \"stage\", \"tournament\", \"onDataChange\", \"onUpdateScore\"], [\"href\", \"javascript:void(0)\", \"ngbNavLink\", \"\"], [3, \"stage\", \"tableData\", \"onDataChange\"]],\n    template: function StagesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵtemplate(3, StagesComponent_section_3_Template, 23, 11, \"section\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.current_Stage);\n      }\n    },\n    dependencies: [i7.NgIf, i8.NgbNavContent, i8.NgbNav, i8.NgbNavItem, i8.NgbNavLink, i8.NgbNavOutlet, i9.ContentHeaderComponent, i10.StageDetailsComponent, i11.StageTablesComponent, i12.StageTeamsComponent, i13.StageMatchesComponent, i1.TranslatePipe],\n    styles: [\"ng-template {\\n  display: block;\\n  position: relative;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvc3RhZ2VzL3N0YWdlcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGNBQUE7RUFDQSxrQkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsibmctdGVtcGxhdGUge1xyXG4gICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "mappings": "AAIA,SAASA,SAAS,QAAQ,gBAAgB;AAK1C,SAASC,QAAQ,QAAQ,MAAM;;;;;;;;;;;;;;;;;;ICGbC,yCAIC;IADCA;MAAAA;MAAA;MAAA,OAAgBA,0CAAoB;IAAA,EAAC;IAEvCA,iBAAgB;;;;IAJdA,4CAAuB;;;;;;IAUzBA,uCAKC;IADCA;MAAAA;MAAA;MAAA,OAAgBA,0CAAoB;IAAA,EAAC;IAEvCA,iBAAc;;;;IALZA,4CAAuB;;;;;;IAWzBA,6CAKC;IAFCA;MAAAA;MAAA;MAAA,OAAgBA,iDAA0B;IAAA,EAAC;MAAAA;MAAA;MAAA,OAC1BA,4CAAqB;IAAA,EADK;IAE5CA,iBAAoB;;;;IAJnBA,4CAAuB;;;;;;IAYzBA,wCAIC;IADCA;MAAAA;MAAA;MAAA,OAAgBA,2CAAoB;IAAA,EAAC;IACtCA,iBAAe;;;;IAHdA,6CAAuB;;;;;IAN7BA,8BAAwD;IACdA,YAEtC;;IAAAA,iBAAI;IACNA,iGAMc;IAChBA,iBAAK;;;IAVqCA,eAEtC;IAFsCA,oDAEtC;;;;;IA1CdA,kCAA+C;IAMrBA,YAA2B;;IAAAA,iBAAI;IAC7CA,6FAOc;IAChBA,iBAAK;IACLA,+BAAe;IACCA,aAAyB;;IAAAA,iBAAI;IAC3CA,6FAQc;IAChBA,iBAAK;IACLA,+BAAe;IACCA,aAA2B;;IAAAA,iBAAI;IAC7CA,6FAOc;IAChBA,iBAAK;IACLA,2EAWK;IACPA,iBAAK;IAEPA,2BAA6C;IAC/CA,iBAAM;;;;;IAhDgBA,eAA2B;IAA3BA,qDAA2B;IAW3BA,eAAyB;IAAzBA,oDAAyB;IAYzBA,eAA2B;IAA3BA,sDAA2B;IAU3BA,eAAsC;IAAtCA,8DAAsC;IAcrDA,eAAoB;IAApBA,kCAAoB;;;ADxCnC,OAAM,MAAOC,eAAe;EAS1BC,YACSC,MAAwB,EACxBC,MAAsB,EACtBC,OAAe,EACfC,kBAAqC,EACrCC,aAA2B,EAC3BC,eAA+B,EAC/BC,YAA0B,EAC1BC,aAAoB;IAPpB,WAAM,GAANP,MAAM;IACN,WAAM,GAANC,MAAM;IACN,YAAO,GAAPC,OAAO;IACP,uBAAkB,GAAlBC,kBAAkB;IAClB,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,iBAAY,GAAZC,YAAY;IACZ,kBAAa,GAAbC,aAAa;IAbtB,uBAAkB,GAAQ,IAAI;IAC9B,kBAAa,GAAQ,IAAI;IACzB,iBAAY,GAAQ,EAAE;IAEtB,kBAAa,GAAG,IAAI;IAWlB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC;IACvE,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACX,MAAM,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC;IAC7DR,kBAAkB,CACfU,aAAa,CAAC,IAAI,CAACL,aAAa,CAAC,CACjCM,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAACC,kBAAkB,GAAGD,GAAG;MAC7BX,aAAa,CACVa,QAAQ,CAAC,IAAI,CAACL,QAAQ,CAAC,CACvBE,SAAS,CAAEC,GAAQ,IAAI;QACtB,IAAI,CAACG,aAAa,GAAGH,GAAG;QAExB,IAAI,CAACV,eAAe,CAACc,IAAI,EAAE;QAC3BvB,QAAQ,CAAC,CAAC,IAAI,CAACwB,eAAe,EAAE,EAChC,IAAI,CAACC,UAAU,EAAE,EACjB,IAAI,CAACC,YAAY,EAAE,CAAC,CAAC,CAACR,SAAS,CAAC;UAC9BS,QAAQ,EAAE,MAAK;YACb,IAAI,CAAClB,eAAe,CAACmB,OAAO,EAAE;UAChC;SACD,CAAC;QAEFjB,aAAa,CAACkB,QAAQ,CAAC,IAAI,CAACT,kBAAkB,CAACU,IAAI,CAAC;QACpD,IAAI,CAACC,aAAa,GAAG;UACnBC,WAAW,EAAE,IAAI,CAACZ,kBAAkB,CAACU,IAAI;UACzCG,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE;YACVC,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,CACL;cACEN,IAAI,EAAE,IAAI,CAAC1B,MAAM,CAACiC,OAAO,CAAC,SAAS,CAAC;cACpCC,MAAM,EAAE;aACT,EACD;cACER,IAAI,EAAE,IAAI,CAAC1B,MAAM,CAACiC,OAAO,CAAC,gBAAgB,CAAC;cAC3CC,MAAM,EAAE,IAAI;cACZC,IAAI,EAAE;aACP,EACD;cACET,IAAI,EAAE,IAAI,CAACR,aAAa,CAACQ;aAC1B,EACD;cACEA,IAAI,EAAE,IAAI,CAACV,kBAAkB,CAACoB;aAC/B,EACD;cACEV,IAAI,EAAE,IAAI,CAACV,kBAAkB,CAACU;aAC/B;;SAGN;MACH,CAAC,CAAC;IACN,CAAC,CAAC;EAEN;EAEAJ,YAAY,CAACe,WAAW,GAAG,KAAK;IAC9B,IAAIC,KAAK,GAAG,kBAAkB;IAC9B,IAAID,WAAW,EAAE;MACfC,KAAK,IAAI,oBAAoB;;IAE/B,IAAI,CAAChC,YAAY,CACdgB,YAAY,CAAC,IAAI,CAACJ,aAAa,CAACqB,EAAE,EAAED,KAAK,CAAC,CAC1CxB,SAAS,CAAE0B,IAAS,IAAI;MACvB,IAAI,CAACC,SAAS,GAAGD,IAAI;MACrB,IAAI,IAAI,CAACtB,aAAa,CAACa,IAAI,KAAKpC,SAAS,CAAC+C,gBAAgB,CAACC,MAAM,EAAE;QACjE,IAAI,CAACvC,aAAa,CAACwC,eAAe,CAAC,IAAI,CAAC1B,aAAa,CAACqB,EAAE,CAAC,CAACzB,SAAS,EAAE;;IAEzE,CAAC,CAAC;EACN;EAEAM,eAAe;IACb,IAAI,CAAChB,aAAa,CACfgB,eAAe,CAAC,IAAI,CAACF,aAAa,CAACqB,EAAE,CAAC,CACtCM,SAAS,EAAE,CACXC,IAAI,CAAE/B,GAAQ,IAAI;MACjB,IAAI,CAACgC,YAAY,GAAGhC,GAAG,CAACyB,IAAI;IAC9B,CAAC,CAAC;EACN;EAEAnB,UAAU;IACR,IAAI,CAACjB,aAAa,CACfiB,UAAU,CAAC,IAAI,CAACH,aAAa,CAACqB,EAAE,CAAC,CACjCM,SAAS,EAAE,CACXC,IAAI,CAAE/B,GAAQ,IAAI;MACjB,IAAI,CAACiC,aAAa,GAAG,CAACjC,GAAG,CAACM,UAAU;MACpC,OAAO,IAAI,CAAC2B,aAAa;IAC3B,CAAC,CAAC;EACN;EAEAC,YAAY,CAACC,MAAM;IACjB,IAAI,CAAC5B,YAAY,CAAC,IAAI,CAAC;EACzB;EAEA6B,kBAAkB,CAACD,MAAM;IACvB,IAAI,CAAC7B,UAAU,EAAE;IACjB,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC;EACzB;EAEA8B,YAAY,CAACF,MAAM;IACjB,IAAI,CAACH,YAAY,GAAGG,MAAM;IAC1B,IAAI,CAAC5B,YAAY,CAAC,IAAI,CAAC;EACzB;EAEA+B,QAAQ,IAAW;EAAC;qBAxHTvD,eAAe;EAAA;EAAA;UAAfA,eAAe;IAAAwD;IAAAC;IAAAC;IAAAC;IAAAC;MAAA;QCjB5B7D,8BAA+C;QAE3CA,wCAAyE;QAEzEA,0EAwDU;QACZA,iBAAM;;;QA3DgBA,eAA+B;QAA/BA,iDAA+B;QAEzBA,eAAmB;QAAnBA,wCAAmB", "names": ["AppConfig", "fork<PERSON><PERSON>n", "i0", "StagesComponent", "constructor", "_trans", "_route", "_router", "_tournamentService", "_stageService", "_loadingService", "stageService", "_titleService", "tournament_id", "snapshot", "paramMap", "get", "stage_id", "getTournament", "subscribe", "res", "current_Tournament", "getStage", "current_Stage", "show", "getTeamsInStage", "hasMatches", "getTableData", "complete", "dismiss", "setTitle", "name", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "instant", "isLink", "link", "group_name", "updateOrder", "query", "id", "data", "tableData", "TOURNAMENT_TYPES", "groups", "checkMatchScore", "to<PERSON>romise", "then", "group_stages", "allowEditTeam", "onDataChange", "$event", "onMatchTableChange", "onTeamChange", "ngOnInit", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stages.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\stages\\stages.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AppConfig } from 'app/app-config';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { StageService } from 'app/services/stage.service';\r\nimport { TournamentService } from 'app/services/tournament.service';\r\nimport { fork } from 'child_process';\r\nimport { forkJoin } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-stages',\r\n  templateUrl: './stages.component.html',\r\n  styleUrls: ['./stages.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class StagesComponent implements OnInit {\r\n  contentHeader: object;\r\n  tournament_id: any;\r\n  stage_id: any;\r\n  current_Tournament: any = null;\r\n  current_Stage: any = null;\r\n  group_stages: any = [];\r\n  tableData: any;\r\n  allowEditTeam = true;\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _tournamentService: TournamentService,\r\n    public _stageService: StageService,\r\n    public _loadingService: LoadingService,\r\n    public stageService: StageService,\r\n    public _titleService: Title\r\n  ) {\r\n    this.tournament_id = this._route.snapshot.paramMap.get('tournament_id');\r\n    this.stage_id = this._route.snapshot.paramMap.get('stage_id');\r\n    _tournamentService\r\n      .getTournament(this.tournament_id)\r\n      .subscribe((res: any) => {\r\n        this.current_Tournament = res;\r\n        _stageService\r\n          .getStage(this.stage_id)\r\n          .subscribe((res: any) => {\r\n            this.current_Stage = res;\r\n\r\n            this._loadingService.show();\r\n            forkJoin([this.getTeamsInStage(),\r\n            this.hasMatches(),\r\n            this.getTableData()]).subscribe({\r\n              complete: () => {\r\n                this._loadingService.dismiss();\r\n              }\r\n            });\r\n\r\n            _titleService.setTitle(this.current_Tournament.name);\r\n            this.contentHeader = {\r\n              headerTitle: this.current_Tournament.name,\r\n              actionButton: false,\r\n              breadcrumb: {\r\n                type: '',\r\n                links: [\r\n                  {\r\n                    name: this._trans.instant('Leagues'),\r\n                    isLink: false,\r\n                  },\r\n                  {\r\n                    name: this._trans.instant('Manage Leagues'),\r\n                    isLink: true,\r\n                    link: '/leagues/manage',\r\n                  },\r\n                  {\r\n                    name: this.current_Stage.name\r\n                  },\r\n                  {\r\n                    name: this.current_Tournament.group_name,\r\n                  },\r\n                  {\r\n                    name: this.current_Tournament.name,\r\n                  },\r\n                ],\r\n              },\r\n            };\r\n          });\r\n      });\r\n\r\n  }\r\n\r\n  getTableData(updateOrder = false) {\r\n    let query = `?force_display=1`;\r\n    if (updateOrder) {\r\n      query += `&update_order=true`;\r\n    }\r\n    this.stageService\r\n      .getTableData(this.current_Stage.id, query)\r\n      .subscribe((data: any) => {\r\n        this.tableData = data;\r\n        if (this.current_Stage.type === AppConfig.TOURNAMENT_TYPES.groups) {\r\n          this._stageService.checkMatchScore(this.current_Stage.id).subscribe();\r\n        }\r\n      });\r\n  }\r\n\r\n  getTeamsInStage() {\r\n    this._stageService\r\n      .getTeamsInStage(this.current_Stage.id)\r\n      .toPromise()\r\n      .then((res: any) => {\r\n        this.group_stages = res.data;\r\n      });\r\n  }\r\n\r\n  hasMatches() {\r\n    this._stageService\r\n      .hasMatches(this.current_Stage.id)\r\n      .toPromise()\r\n      .then((res: any) => {\r\n        this.allowEditTeam = !res.hasMatches;\r\n        return this.allowEditTeam;\r\n      });\r\n  }\r\n\r\n  onDataChange($event) {\r\n    this.getTableData(true);\r\n  }\r\n\r\n  onMatchTableChange($event) {\r\n    this.hasMatches();\r\n    this.getTableData(true);\r\n  }\r\n\r\n  onTeamChange($event) {\r\n    this.group_stages = $event;\r\n    this.getTableData(true);\r\n  }\r\n\r\n  ngOnInit(): void { }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <section id=\"stage-page\" *ngIf=\"current_Stage\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n              <li ngbNavItem>\r\n                <a ngbNavLink>{{ 'Details' | translate }}</a>\r\n                <ng-template ngbNavContent>\r\n                  <stage-details\r\n                    [stage]=\"current_Stage\"\r\n                    [tournament]=\"current_Tournament\"\r\n                    (onDataChange)=\"onDataChange($event)\"\r\n                  >\r\n                  </stage-details>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem>\r\n                <a ngbNavLink>{{ 'Teams' | translate }}</a>\r\n                <ng-template ngbNavContent>\r\n                  <stage-teams\r\n                    [stage]=\"current_Stage\"\r\n                    [tournament]=\"current_Tournament\"\r\n                    [group_stages]=\"group_stages\"\r\n                    (onDataChange)=\"onTeamChange($event)\"\r\n                  >\r\n                  </stage-teams>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem>\r\n                <a ngbNavLink>{{ 'Matches' | translate }}</a>\r\n                <ng-template ngbNavContent>\r\n                  <app-stage-matches\r\n                    [stage]=\"current_Stage\"\r\n                    [tournament]=\"current_Tournament\"\r\n                    (onDataChange)=\"onMatchTableChange($event)\"\r\n                    (onUpdateScore)=\"onUpdateScore($event)\"\r\n                  ></app-stage-matches>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem *ngIf=\"current_Stage.type != 'Knockout'\">\r\n                <a href=\"javascript:void(0)\" ngbNavLink>{{\r\n                  'Tables' | translate\r\n                }}</a>\r\n                <ng-template ngbNavContent>\r\n                  <stage-tables\r\n                    [stage]=\"current_Stage\"\r\n                    [tableData]=\"tableData\"\r\n                    (onDataChange)=\"onDataChange($event)\"\r\n                  ></stage-tables>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}